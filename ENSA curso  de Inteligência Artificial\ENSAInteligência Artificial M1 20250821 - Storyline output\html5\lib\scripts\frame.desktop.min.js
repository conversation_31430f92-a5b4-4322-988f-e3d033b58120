﻿/*! ds-bootstrap - v1.0.0.30234 - 2023-04-7 8:37pm UTC
* Copyright (c) 2023 ; Not Licensed */!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(o=i.key,a=void 0,a=function(t,n){if("object"!==e(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,n||"default");if("object"!==e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(o,"string"),"symbol"===e(a)?a:String(a)),i)}var o,a}var n=DS,r=n._,i=n.pubSub,o=n.events,a=n.constants,c=[],l=function(){function e(t,n){var c=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.frame=t,this.frame.blocked=!1,this.preso=n,this.setupControlOptions(),this.layouts={},this.dockSettings={dockedState:a.docked.NONE,width:0},this.setLayout(this.frame.default_layout,a.refs.FRAME),this.resourceDescription=this.frame.resourceData.description;var l=n.getFirstSlide();for(var s in this.slideWidth=l.get("width"),this.slideHeight=l.get("height"),this.temp=[],this.frame.layouts)this.temp.push(s);this.rtl="rtl"===this.frame.textdirection,this.hasModernText=0!==this.frame.renderingEngineType,this.dir=this.dir.bind(this),r.bindAll(this,"onLayoutChanged"),i.on(o.controlLayout.CHANGED,this.onLayoutChanged),i.on(o.controlLayout.UPDATE,(function(e,t,n){c.frame.controlLayouts[e]=n,c.setLayout(e,t)})),i.on(o.controlOptions.CHANGED,(function(e){var t=c.optionChangesRequireMenuRefresh(c.frame.controlOptions.menuOptions,e.menuOptions);c.frame.controlOptions=e,c.setupControlOptions(),i.trigger(o.controlOptions.RESET),t&&i.trigger(o.navData.REFRESH_VIEW)})),i.on(o.frame.FONT_SCALE,(function(e){c.frame.fontscale=e,i.trigger(o.controlOptions.RESET)})),i.on(o.glossary.UPDATE,(function(e){c.frame.glossaryData=e,i.trigger(o.glossary.REFRESH_VIEW)})),i.on(o.navData.UPDATE,(function(e){c.frame.navData=e,i.trigger(o.navData.REFRESH_VIEW)})),i.on(o.resources.UPDATE,(function(e){c.frame.resourceData.resources=e,i.trigger(o.resources.REFRESH_VIEW)})),i.on(o.resources.UPDATE_DESCRIPTION,(function(e){c.frame.resourceData.description=e,i.trigger(o.resources.REFRESH_VIEW)})),i.on(o.layer.DIALOG_SHOWN,(function(e){var t=DS.windowManager.getCurrentWindow();i.trigger(o.frameModel.BLOCKED_CHANGED,t.frame.id,!0,e)})),i.on(o.layer.DIALOG_HIDDEN,(function(){var e=DS.windowManager.getCurrentWindow();i.trigger(o.frameModel.BLOCKED_CHANGED,e.frame.id,!1)}))}var n,l,s;return n=e,(l=[{key:"setupControlOptions",value:function(){var e=this.frame.controlOptions.sidebarOptions;this.sidebarOpts=e,this.bottomBarOpts=this.frame.controlOptions.bottomBarOptions,this.topTabs=e.tabs.linkRight||[],this.topTabsLeft=e.tabs.linkLeft||[],this.topTabsRight=e.tabs.linkRight||[],this.sidebarTabs=e.tabs.sidebar||[],this.outlineInSidebar=this.sidebarTabs.some((function(e){return"outline"===e.name})),this.buttonOptions=this.frame.controlOptions.buttonoptions,this.title={enabled:e.titleEnabled,text:e.titleText}}},{key:"optionChangesRequireMenuRefresh",value:function(e,t){return e.wrapListItems!==t.wrapListItems||e.autonumber!==t.autonumber}},{key:"onLayoutChanged",value:function(e,t){this.setLayout(e,t)}},{key:"hasTopLinks",value:function(){return 0!==this.topTabsLeft.length||0!==this.topTabsRight.length}},{key:"getString",value:function(e){var t=this.currLayout.string_table,n=this.frame.stringTables[t].string[e];return null==n?(c.includes(e)||(c.push(e),console.warn("could not find ".concat(e," in string table ").concat(t))),e.replace("acc_","").replace(/_/g," ")):n}},{key:"setDocked",value:function(e,t){this.dockSettings={dockedState:e,width:t}}},{key:"setLayout",value:function(e,t){this.currLayout=this.frame.layouts[e],this.currControlLayout=this.frame.controlLayouts[e],this.layouts[t]=this.currControlLayout,i.trigger(o.frameModel.LAYOUT_CHANGED,this.currControlLayout,t)}},{key:"getWndControlLayout",value:function(e){return this.layouts[e]||this.currControlLayout}},{key:"dir",value:function(e){if(null!=e)return this.rtl?e.reverse():e}}])&&t(n.prototype,l),s&&t(n,s),Object.defineProperty(n,"prototype",{writable:!1}),e}(),s=l;function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function f(e,t,n){return(t=y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,c=[],l=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,y(r.key),r)}}function y(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}var b,v=DS,m=v._,g=v.scaler,w=v.detection,S=v.detection.orientation,k=v.utils,C=k.getPath,O=k.scaleVal,E=k.pxify,x=v.dom,T=x.addClass,L=x.removeClass,P=v.constants.refs.FRAME,j=document.getElementById(DS.constants.els.PRESO),A={x:"w",xl:"w",xp:"w",y:"h",yl:"h",yp:"h",wl:"w",wp:"w",hl:"h",hp:"h"},D=["wrapper","lightBoxWrapper"],I=function(e){DS.flagManager.playbackSpeedControl&&null!=e.beforeUpdateHook&&e.beforeUpdateHook()},R=function(e,t,n){I(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.l+n,e.update(!0),t.l=e.x+e.w},B=function(e,t,n){I(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.r-e.w-n,e.update(!0),t.r=e.x},M=function(e,t,n){I(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.t+n,e.update(!0),t.t=e.y+e.h},N=function(e,t,n){I(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.b-e.h-n,e.update(!0),t.b=e.y},H={l:R,r:B,t:M,b:N},F=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.ViewLogic=t,this.params=n,this.nameKey=null!=n&&n.nameKey||t.nameKey,this.enabled=!0}var t,n,r;return t=e,n=[{key:"init",value:function(e){if(this.hasInited)console.warn("has already initialized",this);else{var t;this.hasInited=!0;var n=this.ViewLogic,r=this.params;null==r&&null!=n?r=n:null!=n&&(t=!0),m.isFunction(r)&&(r=r(this.nameSpace||e)),r.w=r.w||r.minW||100,r.h=r.h||r.minH||100,this.orientationProps(r,"x","y","w","h","scale"),(r=Object.assign({position:"absolute",x:0,y:0,minW:0,maxW:Number.MAX_SAFE_INTEGER,minH:0,maxH:Number.MAX_SAFE_INTEGER,wPad:0,scale:1,visibility:"reflow",visual:!0,bgColor:null,overflow:"hidden",origin:"center center",z:null,opacity:null,visible:!0,attrs:{},noContent:!0,calcTextSize:!1},r)).calcTextSize&&(r.noContent=!1),this.noContent=r.noContent,this.lastWidthByText=0,this.lastHeightByText=0,this.padLeft=r.padLeft||0,this.padRight=r.padRight||0,this.childDef=r.childDef,this.childViews=r.childViews,this.updateHook=r.updateHook,this.onCaptionChanged=r.onCaptionChanged,r.childViews=null,r.updateHook=null,r.childDef=null,r.nameKey=null,r.onCaptionChanged=null,Object.assign(this,r.methods),r.methods=null,this.createDynamicGetters(r),r.visual&&(this.el=document.createElement(r.tag||"div"),this.initAttributes(r.attrs),this.initStyles(r),"button"===r.tag&&(this.el.style.cursor="pointer"),this.initContent(r),r.add&&j.appendChild(this.el),this.hasInitialized=!0),this.initVisibility(r),this.initChildRefs(),t&&(this.viewLogic=new n(this))}}},{key:"orientationProp",value:function(e,t){var n="".concat(t,"l"),r="".concat(t,"p");null!=e[n]&&null!=e[r]&&(e[t]=function(){return S.isLandscape?this[n]:this[r]})}},{key:"orientationProps",value:function(e){for(var t=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];r.forEach((function(n){return t.orientationProp(e,n)}))}},{key:"initContent",value:function(e){if(this.noContent){var t=this.html;null!=t&&(this.el.innerHTML=t)}else this.content=document.createElement("div"),this.content.setAttribute("class","view-content"),this.content.setAttribute("tabindex",-1),Object.assign(this.content.style,{position:"relative","text-align":"center",top:0}),Object.assign(this.content.style,e.contentStyle||{}),this.initAttributes(e.contentAttrs,this.content),null!=e.html&&(this.content.innerHTML=this.html),this.el.appendChild(this.content)}},{key:"initAttributes",value:function(e,t){for(var n in e)if(null!=e[n]){var r=e[n];"id"===n?r=m.kebabCase(r):"tabindex"===n&&-1!==r&&this.el.setAttribute("data-".concat(n),r),(t||this.el).setAttribute(n,r)}}},{key:"initStyles",value:function(e){if(Object.assign(this.el.style,{position:e.position,left:0,top:0,backgroundColor:e.bgColor,border:e.border,overflow:e.overflow,transformOrigin:e.origin,opacity:e.opacity,zIndex:e.z}),null!=C(e,"style.display")){var t=e.style.display;"boolean"==typeof t&&(e.style.display=t?"block":"none")}Object.assign(this.el.style,e.style)}},{key:"initVisibility",value:function(e){!1===e.visible&&this.setVisibility(!1)}},{key:"initChildRefs",value:function(){this.children=[],this.childList=[];for(var e=this.el.querySelectorAll("[data-ref]"),t=0;t<e.length;t++)this.children[e[t].dataset.ref]={el:e[t]}}},{key:"updateHtml",value:function(){var e=this.html;null!=e&&(this.noContent?this.el.innerHTML=e:this.content.innerHTML=e)}},{key:"createDynamicGetters",value:function(t){for(var n in t)"id"!==n&&null!=t[n]&&e.prop(this,n,t[n])}},{key:"updateSize",value:function(){this.el.style.width=E(O(this.w)),this.el.style.height=E(O(this.h))}},{key:"updateTrans",value:function(){var e=D.includes(this.nameKey)?m.identity:O,t=["translate(".concat(E(e(this.x)),", ").concat(E(e(this.y)),")")];if(this.xs)for(var n=0;n<this.xs.length;n++)t.push("translateX(".concat(E(e(this.xs[n])),")"));if(this.ys)for(var r=0;r<this.ys.length;r++)t.push("translateY(".concat(E(e(this.ys[r])),")"));w.deviceView.isMobile&&null!=this.scale&&t.push("scale(".concat(this.scale,")")),this.el.style.transform=t.join(" ")}},{key:"calcChildrensWidth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!0},t=0;return null!=this.children&&this.children.forEach((function(n){e(n)&&(n.update(),t+=n.w)})),t}},{key:"calcChildrensHeight",value:function(){var e=0;return null!=this.children&&this.children.forEach((function(t){t.update(),e+=t.h})),e}},{key:"positionChildren",value:function(e){var t=e.vertical,n=e.toTop,r=e.toLeft,i=e.pad,o=e.startPos,a=e.reverse,c=void 0!==a&&a,l=e.hook,s=void 0===l?m.noop:l,u=e.rtl,f=void 0!==u&&u,h=e.sizeToChildren,p=e.alignChild,y=t?n?N:M:r?B:R,b=Object.assign({l:o,r:o,t:o,b:o},e.bounds),v=c!==f?this.children.slice().reverse():this.children,g=d(t?["height","t"]:["width","l"],2),w=g[0],k=g[1],C=S.isLandscape?m.first:m.last;v.forEach((function(e){(e.beforeReflowHook||m.noop)();var t=p&&C(e.parentAlign);!e.visible&&"reflow"===e.visibility||"-"===t||(t?H[t](e,b,i):y(e,b,i),s(e))})),h&&(this[w]=b[k])}},{key:"flowChildren",value:function(e){(e=Object.assign({pad:0,sizeToChildren:!1,startPos:0,toLeft:!1,reverse:!1,fullUpdate:!1,rtl:!1,hook:function(){}},e)).fullUpdate?this.hasAllChildren()&&(this.positionChildren(e),this.updateSize()):this.positionChildren(e)}},{key:"isBlocked",value:function(){if(w.theme.isClassic)return!1;var e=z.getTopNameSpace(),t=z.getCurrentNameSpace(),n=z.getBlocker(t.name);return null!=e&&null!=n&&n.visible}},{key:"setEnabled",value:function(e,t){this.enabled!=e&&(this.enabled=e,e?L(this.el,"cs-disabled"):T(this.el,"cs-disabled"),this.el.setAttribute("aria-disabled",!e));var n=this.el.getAttribute("data-tabindex");null!=n&&this.el.setAttribute("tabindex",t&&!e||this.isBlocked()?-1:n)}},{key:"setVisibility",value:function(e,t){var n=this.visible!==e,r=!1;return"no-reflow"===this.visibility?(this.el.style.visibility=e?"visible":"hidden",this.el.style.pointerEvents=e?"":"none"):"reflow"===this.visibility&&(this.el.style.display=e?"block":"none",r=!0),t&&(this.layoutDefaultVisible=e),this.visible=e,n&&C(this,"viewLogic.didChangeVisibility")&&this.viewLogic.didChangeVisibility(e),r}},{key:"childVisibilityChanged",value:function(){null!=this.childVisibilityChangedHook?this.childVisibilityChangedHook():null!=this.parent&&this.parent.childVisibilityChanged()}},{key:"update",value:function(e){null==this.beforeUpdateHook||e&&DS.flagManager.playbackSpeedControl||this.beforeUpdateHook(),this.updateSize(),this.updateTrans(),null!=this.updateHook&&this.updateHook()}},{key:"updateChildren",value:function(e){this.children.forEach((function(t){t.update(),e&&t.updateChildren(e)}))}},{key:"doTextCalcs",value:function(){var e=1/g.getScale(),t=this.content.clientWidth*e,n=this.content.clientHeight*e;this.lastWidthByText=t+4+this.padLeft+this.padRight,this.lastHeightByText=n+4}},{key:"wasAppended",value:function(){var e=this;this.calcTextSize&&this.doTextCalcs(),null!=this.childViews&&this.childViews.forEach((function(t){"string"==typeof t&&(t=z.getOrCreateView(t)),t.init(e.nameSpace),e.append(t)}))}},{key:"setChildNum",value:function(e){this.defaultChildNum=e}},{key:"hasAllChildren",value:function(){return this.hasInitialized&&this.children.length===this.defaultChildNum}},{key:"append",value:function(e,t){if(e.parent=this,this.children[m.camelCase(e.nameKey)]=e,t?this.children.unshift(e):this.children.push(e),this.noContent||e.outsideContent?this.el.appendChild(e.el):this.content.appendChild(e.el),e.wasAppended(),e.update(),null==e.nameSpace){for(var n=this;null!=n&&null==n.nameSpace;)n=n.parent;e.nameSpace=n.nameSpace,z.hasNamespace(n.nameSpace)?z.getNamespace(n.nameSpace)[e.nameKey]=e:console.warn("could not find namespace ".concat(n.nameSpace," when appending"))}}},{key:"destroy",value:function(){null!=this.children&&this.children.forEach((function(e){return e.destroy()})),null!=this.viewLogic&&this.viewLogic.teardown(),null!=this.el.parentNode&&this.el.parentNode.removeChild(this.el),this.nameSpace=null}},{key:"startFloat",value:function(){this.floating=!0,this.lastFloatParent=this.el.parentNode,this.shouldReparent&&z.getNamespace(this.nameSpace).wrapper.el.appendChild(this.el)}},{key:"endFloat",value:function(){this.floating&&(this.floating=!1,this.shouldReparent&&this.lastFloatParent.appendChild(this.el))}},{key:"right",value:function(){return this.floating?0:this.x+this.w}},{key:"bottom",value:function(){return this.floating?0:this.y+this.h}},{key:"getBox",value:function(){if(null==z.getNamespace(this.nameSpace).wrapper)return null;var e=z.getNamespace(this.nameSpace).wrapper.dimScale||1,t=(z.getNamespace(this.nameSpace).wrapper.scale||1)*e,n=this.x,r=this.y,i=this.w,o=this.h,a=this.offsets,c=(a=void 0===a?{}:a).l,l=void 0===c?0:c,s=a.t,u=this;for(n=(n+l)*t,r=(r+(void 0===s?0:s))*t;u=u.parent;){var f=null!=u.parent?t:1,d=u.offsets,h=(d=void 0===d?{}:d).l,p=void 0===h?0:h,y=d.t,b=void 0===y?0:y;n+=(u.x+p)*f,r+=(u.y+b)*f}return{x:n,y:r,w:i*=t,h:o*=t}}}],n&&p(t.prototype,n),r&&p(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();F.propFns={"fit-to-text-w":function(){return Math.max(this.minW,this.lastWidthByText)+this.wPad},"fit-to-text-h":function(){return Math.max(this.minH,this.lastHeightByText)},"vertical-center":function(){return this.parent.h/2-this.h/2},"horizontal-center":function(){return this.parent.w/2-this.w/2}},F.prop=function(e,t,n){if("number"==typeof n)e[t]=n;else if("string"==typeof n&&null!=F.propFns[n])Object.defineProperties(e,f({},t,{get:F.propFns[n],set:m.noop}));else if("string"==typeof n&&n.endsWith("%")){var r=parseFloat(n)/100,i=null!=A[t]?A[t]:t;Object.defineProperties(e,f({},t,{get:function(){return this.parent[i]?e.parent[i]*r:0},set:m.noop}))}else if(m.isFunction(n)){var o;if("w"===t||"h"===t){var a=n.bind(e),c=t.toUpperCase(),l=e["min".concat(c)],s=e["max".concat(c)];o=function(){var e=a();return e<l?e=l:e>s&&(e=s),e}.bind(e)}else o=n;Object.defineProperties(e,f({},t,{get:o,set:m.noop}))}else e[t]=n;e[t]=n};var V={},W={},U={},K=function(e){var t,n,r=u(e);return"string"===r?t=e:"object"===r?n=e[t=Object.keys(e)[0]]:console.warn("invalid view definition. ".concat(e," is a ").concat(u(e))),{viewName:t,children:n}},z={nameSpaces:{},nsStack:[],getNamespace:function(e){return this.nameSpaces[e]},hasNamespace:function(e){return null!=this.nameSpaces[e]},setModel:function(e){return this.model=e,this},resetStates:function(e){var t=this.getNamespace(e);m.forEach(t,(function(e){return e&&e.setEnabled&&e.setEnabled(!0)}))},updateVisibility:function(e,t){var n=!1;for(var r in e){var i=this.nameSpaces[t][r];if(null!=i)i.setVisibility(e[r],!0)&&(n=!0)}return n},def:function(e,t,n){null==n?t.nameKey=e:n.nameKey=e;var r=new F(t,n);return null==V[e]?(V[e]=r,W[e]={ViewLogic:t,p:n},U[e]=0):console.warn("views connot share the same name ".concat(e)),r},addNameSpace:function(e){b=e,this.nameSpaces[b]=this.nameSpaces[b]||{name:e,topLevelElements:[],isAttached:!0,tabReachable:!0,detach:function(){this.isAttached=!1,this.topLevelElements.forEach((function(e){return j.removeChild(e.el)}))},reattach:function(){this.isAttached=!0,this.topLevelElements.forEach((function(e){return j.appendChild(e.el)}))},updateTabIndex:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=function(t){if(!t.dataset.leavealone){var n=t.getAttribute("data-tabindex");null==n&&(n=t.tabIndex,t.setAttribute("data-tabindex",n)),t.tabIndex=e.tabReachable?n:-1}};this.topLevelElements.forEach((function(i){var o=function(e,t){t?e.el.removeAttribute("aria-hidden"):e.el.setAttribute("aria-hidden",!0)};t&&(i.children.some((function(e){return"startoverlay"===e.nameKey}))?i.children.filter((function(e){return"startoverlay"!==e.nameKey})).forEach((function(t){return o(t,e.tabReachable)})):o(i,e.tabReachable)),i.el.querySelectorAll(n?"[tabIndex]:not(.acc-shadow-el):not(.slide-object)":"[tabIndex]").forEach((function(e){return r(e)})),"lightBoxClose"===i.nameKey&&r(i.el)}))}}},update:function(e){function t(t,n){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){e.forEach((function(e){m.isFunction(e)?e():Array.isArray(e)?update(e,t):null==e||!e.update||e.noUpdate&&!t||(e.update(),t&&e.updateHtml())}))})),getViewConfig:function(e){var t=W[e];return{name:e,ViewLogic:t.ViewLogic,p:t.p}},getOrCreateView:function(e){if(++U[e]>1){var t=W[e],n=t.ViewLogic,r=t.p;return new F(n,r)}return V[e]},tree:function(e,t){for(var n=this,r=[],i=function t(r){if(null!==r){var i=K(r),o=i.viewName,a=i.children,c=n.nameSpaces[e][o]=n.getOrCreateView(o);if(null!=c){if(c.nameSpace=e,C(a,"length")>0){c.setChildNum(a.length),c.hasChildren=!0;for(var l=0;l<a.length;l++)t(a[l])}}else console.warn("could not find view '".concat(o,"'"))}},o=function t(i,o){if(null!==i){var a=K(i),c=a.viewName,l=a.children,s=n.nameSpaces[e][c];if(null!=s){if(s.init(e),null!=o?o.append(s):n.nameSpaces[e].topLevelElements.push(s),null!=s.childDef&&s.childDef(),r.push(s),s.hasChildren)for(var u=0;u<l.length;u++)t(l[u],s)}else console.warn("could not find view '".concat(c,"'"))}},a=0;a<t.length;a++)i(t[a]);for(var c=0;c<t.length;c++)o(t[c]);return r},getCurrentNameSpace:function(){return this.nameSpaces[b]},getTopNameSpace:function(){return m.last(this.nsStack)},getCurrentNameSpaceString:function(){return b},getFrameNameSpace:function(){return this.nameSpaces[P]},getBlocker:function(e){switch(e){case DS.constants.refs.FRAME:return z.getNamespace(e).frameBlocker;case"LightboxWnd":return z.getNamespace(e).lightBoxBlocker;case"LightboxControlsWnd":return z.getNamespace(e).lightBoxControlsBlocker;default:return null}}};function G(e){return function(e){if(Array.isArray(e))return e}(e)||Z(e)||Q(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e){return function(e){if(Array.isArray(e))return Y(e)}(e)||Z(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(e,t):void 0}}function Z(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var X,$=DS,J=$._,ee=$.pubSub,te=$.events,ne={},re=!1,ie={},oe=function(e){try{var t=q(document.styleSheets).find(e);X=q(t.rules)}catch(e){}};if(oe((function(e){return null!=e.href&&e.href.includes("output.min.css")})),ee.on(te.scheme.CHANGED,(function(e){oe((function(t){return null!=t.ownerNode&&t.ownerNode.id===e})),ie={}})),null!=X){var ae=function(e,t){var n,r;if(re){n="".concat(t,".cs-").concat(z.model.frame.default_layout);var i=e.split(" ");r=(i=i.map((function(e){return(e.startsWith(".")?".":"")+J.compact(e.split(".")).reverse().join(".")}))).join(" ")}else n=".cs-".concat(z.model.frame.default_layout).concat(t),r=e;return"".concat(n," ").concat(r)};ne.getColor=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=ae(t,r);if(null==ie[i]){var o=X.find((function(e){return e.selectorText===i}));null==o&&(re=!re,i=ae(t,r),o=X.find((function(e){return e.selectorText===i}))||{}),ie[i]=o.style||{}}return ie[i][n]||""}}else{var ce=function(e,t){var n=document.createElement(e);return n.setAttribute("class",t),n},le=function(e){return ce("div",e)},se=/^\./,ue=/\./g;ne.getColor=function(e,t,n){var r,i,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=(o||t).split(/\s+/),c=z.getNamespace(e).wrapper.el;a.forEach((function(e,t){if(se.test(e))r=le(e.replace(ue," "));else{var n=G(e.split(".")),o=n[0],a=n.slice(1);r=ce(o,a.join(" "))}0===t&&(r.style.position="absolute",r.style.display="none",i=r),c.appendChild(r),c=r}));var l=window.getComputedStyle(r).getPropertyValue(n);return""===l&&"border-color"===n&&(l=window.getComputedStyle(r).getPropertyValue("border-top-color")),i.parentNode.removeChild(i),l||""}}var fe=ne;function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function he(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==de(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==de(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===de(o)?o:String(o)),r)}var i,o}var pe,ye,be,ve=DS,me=ve.dom,ge=ve._,we=ve.detection,Se=ve.pubSub,ke=ve.events,Ce=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ge.bindAll(this,"onLoaderShow","onLoaderMute","onLoaderUnmute","onLoaderHide","onRemoveLoaderTitle");var t={};t[ke.loader.SHOW]=this.onLoaderShow,t[ke.loader.MUTE]=this.onLoaderMute,t[ke.loader.UNMUTE]=this.onLoaderUnmute,t[ke.loader.HIDE]=this.onLoaderHide,t[ke.loader.REMOVE_TITLE]=this.onRemoveLoaderTitle,t[ke.startOverlay.READY]=this.onLoaderHide,me.addClass(document.body,"theme-".concat(window.globals.themeName)),document.body.classList.contains("view-tablet")&&me.addClass(document.body,"is-touchable-tablet"),we.env.is360&&me.addClass(document.body,"is-360"),Se.on(t),this.setupBrandingColor()}var t,n,r;return t=e,(n=[{key:"setupBrandingColor",value:function(){window.requestAnimationFrame((function(){var e=fe.getColor(DS.constants.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");null!=e&&(DS.constants.theme.brandingHighlight=e,Se.trigger(ke.app.BRANDING_COLOR,e))}))}},{key:"onRemoveLoaderTitle",value:function(){var e=document.querySelector("body > .mobile-load-title-overlay");null!=e&&e.parentNode.removeChild(e)}},{key:"getSpinLoader",value:function(){return document.querySelector("body > .slide-loader")}},{key:"onLoaderMute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=0)}},{key:"showLoaderDelayed",value:function(e){clearTimeout(this.loaderTimeout),this.loaderTimeout=setTimeout(this.onLoaderShow,e)}},{key:"onLoaderHide",value:function(){clearTimeout(this.loaderTimeout),this.getSpinLoader().style.display="none",me.addClass(document.getElementById("preso"),"hide-slide-loader"),Se.trigger(ke.app.HIDE_LOADER)}},{key:"onLoaderUnmute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=1)}},{key:"onLoaderShow",value:function(e){e>0?this.showLoaderDelayed(e):(this.getSpinLoader().style.display="block",me.removeClass(document.getElementById("preso"),"hide-slide-loader"),Se.trigger(ke.app.SHOW_LOADER))}}])&&he(t.prototype,n),r&&he(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Oe=DS,Ee=Oe.globalEventHelper.addWindowListener,xe=Oe.events,Te=xe.ds,Le=Te.FRAME_DATA_LOADED,Pe=Te.PRESO_READY,je=xe.window.STACKING_CHANGED,_e=xe.frame,Ae=_e.MODEL_READY,De=_e.SCALE,Ie=xe.resume.SET_DATA,Re=xe.startOverlay.READY,Be=xe.controlOptions.RESET,Me=xe.sidebar.ACTIVE_TAB_SET,Ne=xe.renderTree.DESTROYED,He=Oe.constants,Fe=(He.els.PRESO,He.refs.FRAME),Ve=Oe.detection.theme,We=Oe.pubSub,Ue=Oe.focusManager,Ke=Oe.flagManager,ze=Oe.playerGlobals,Ge=Oe.stringTabler,qe=Oe.shortcutManager,Qe=Oe.dom,Ze={},Ye=function(e){var t=z.nsStack.indexOf(e);t>=0&&z.nsStack.splice(t,1)},Xe=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];z.nsStack.forEach((function(t,n,r){t.tabReachable=n===r.length-1&&e,t.updateTabIndex()}))},$e=function(e){pe={createWindow:function(t){var n,r,i=this,o=z.getNamespace(t),a=function(){return function(e){var t=z.getNamespace(e),n=t.isAttached;t.reattach(),Ye(t),z.nsStack.push(t),n||Ze[e].all();var r=z.nsStack.length;z.nsStack.forEach((function(e,t){var n=e.slide.el;t===r-1?(Qe.addClass(n,"primary-slide"),Qe.removeClass(n,"secondary-slide")):(Qe.removeClass(n,"primary-slide"),Qe.addClass(n,"secondary-slide"))})),Xe(),We.trigger(je,e)}(t)},c=function(e,n,r){if(e===t){var i=r?r.getWndBlockerBackground():"",a=z.getBlocker(e);null!=a&&(a.setBackgroundColor(i),a.setVisibility(n)),o.tabReachable=!n,o.updateTabIndex(!1,!0)}};return null==o&&(z.addNameSpace(t),r=e[t](ye),n=Ee("resize",(function(){r.resize(),We.trigger(De)})),t===Fe?ye.setLayout(ye.frame.default_layout,Fe):r.all(),o=z.getNamespace(t),Ze[t]=r,o.moveToTop=a,Ve.isUnified&&(We.on(DS.events.frameModel.BLOCKED_CHANGED,c),We.on(DS.events.window.MAIN_CHANGED,(function(e,t){return c(t,!1)})))),a(),We.on(Be,(function e(){var t=function(){var e=[],t=z.getNamespace(Fe).tabs.viewLogic.getSelectedTab();return t&&e.push(t.nameKey),_.each(z.getNamespace(Fe).topTabs.children,(function(t){_.each(_.filter(t.children,"viewLogic.showing",!0),(function(t){return e.push(t.nameKey)}))})),e}();Ye(z.getNamespace(Fe)),r.destroy(),DS.pubSub.trigger(Ne),z.nameSpaces[Fe]=null,n(),We.off(Be,e),i.createWindow("_frame"),r.rerender(),t.forEach((function(e){We.trigger(Me,e)}))})),{id:t,el:o.slide.el,wndEl:o.wrapper.el,captionEl:(o.captionContainer||{}).el,x:function(){return o.wrapper.x},y:function(){return o.wrapper.y},close:function(){o.zoomBounds=null,o.detach(),Ye(o),z.nsStack[z.nsStack.length-1].moveToTop(),Ue.reCenter()},moveToTop:a,getWinScale:function(){return o.slide.winScale||1},getPinchZoomBounds:function(){return o.slide.pinchZoomBounds},onPinchZoom:Ze[t].pinchZoom?function(e){o.zoomBounds=e,Ze[t].pinchZoom()}:function(){},onWndBlockedChanged:c}},getSidebarPosition:function(){return ye.sidebarOpts.sidebarPos},getDefaultLayout:function(){return be.default_layout},getFonts:function(){return be.fonts},getFontScale:function(){return ye.frame.fontscale},getCaptionData:function(){var e=ye.frame.controlOptions.controls;return{font:e.font,enabled:e.closed_captions}},getNavData:function(){return ye.frame.navData.outline.links},isReadOnlyOnce:function(){return be.controlOptions.controls.readonlyOnce},topmostUnreachable:function(){return z.nsStack},setAllAccVisibility:function(e){_.forEach(z.nameSpaces,(function(t){var n=t.wrapper;e?n.el.removeAttribute("aria-hidden"):n.el.setAttribute("aria-hidden",!0)}))}},We.on(Ie,Xe),We.on(Re,(function(){Xe(!1)})),ze.player=pe},Je=DS,et=(Je.MicroScrollBar,Je.detection,function(e){return null!=e&&2===e.split(".").length}),tt=function(e,t){return e.some((function(e){var n=t[e.name];return null==n||"outline"===e.name&&n.enabled||!0===n}))},nt=10,rt=20,it=46;function ot(e){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ot(e)}function at(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ot(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ot(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ot(o)?o:String(o)),r)}var i,o}var ct,lt=DS.utils,st=lt.scaleVal,ut=lt.pxify,ft=10,dt=function(e){return ut(st(e,!1))},ht=function(e){return ut(st(e))},pt=function(e){return"scale(".concat(st(e,!1),")")},yt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.model=t}var t,n,r;return t=e,(n=[{key:"calcStyles",value:function(){return"\n      .cs-".concat(this.model.frame.default_layout,".cs-base {\n        border-width: ").concat(dt(1)," !important;\n      }\n\n      #frame {\n        border-radius: ").concat(ht(ft),";\n      }\n\n      .btn {\n        border-radius: ").concat(dt(7),";\n      }\n\n      #nav-controls .btn svg,\n      #light-box-bottom .btn svg {\n        width: ").concat(ht(8),";\n        height: ").concat(ht(11),";\n      }\n\n      #reset svg {\n        width: ").concat(ht(18),";\n        height: ").concat(ht(16),";\n      }\n\n      .search-heading {\n        padding: ").concat(ht(ft),";\n        margin: 0;\n        padding-bottom: ").concat(ht(12),";\n      }\n\n      #outline-search {\n        padding: ").concat(ht(ft),'\n      }\n\n      .search input[type="search"] {\n        height: ').concat(ht(28),";\n        width: calc(100% - ").concat(ht(25),");\n        border-top-left-radius: ").concat(dt(4),";\n        border-bottom-left-radius: ").concat(dt(4),";\n      }\n\n      #outline-search button {\n        width: ").concat(ht(25),";\n        height: ").concat(ht(28),";\n        border-top-right-radius: ").concat(dt(4),";\n        border-bottom-right-radius: ").concat(dt(4),";\n      }\n\n      .icon-search {\n        width: ").concat(ht(14),";\n        height: ").concat(ht(14),";\n      }\n\n      #outline-content > ul {\n        padding: ").concat(ht(1)," ").concat(ht(3)," ").concat(ht(2),";\n      }\n\n      #outline-content svg {\n        width: ").concat(ht(20),";\n        height: ").concat(ht(20),";\n      }\n\n      .note-content, .note-title, #transcript-content hr,\n      .resources-content, .resources-title, #resources-content hr,\n      #search-results-content hr {\n        margin-left: ").concat(dt(ft),";\n        margin-right: ").concat(dt(ft),";\n      }\n\n      .resources-title, .note-title {\n        margin-top: ").concat(dt(ft),";\n        margin-bottom: ").concat(dt(ft),";\n      }\n\n      hr {\n        border-width: ").concat(dt(1),";\n        width: calc(100% - ").concat(dt(20),");\n      }\n\n      #transcript-content hr {\n        top: ").concat(dt(2),";\n      }\n\n      .glossary-title {\n        padding: ").concat(ht(ft),";\n      }\n\n      .term button {\n        padding-top: ").concat(ht(7),";\n        padding-right: ").concat(ht(10),";\n        padding-bottom: ").concat(ht(7),";\n        padding-left: ").concat(ht(10),";\n      }\n\n      #captions {\n        border-radius: ").concat(dt(7),";\n      }\n\n      #captions svg {\n        width: ").concat(ht(22),";\n        height: ").concat(ht(18),";\n      }\n\n      #light-box-close svg {\n        width: ").concat(ht(10),";\n        height: ").concat(ht(10),";\n      }\n\n      .message-window {\n        border-width: ").concat(dt(1),";\n      }\n\n      .message-window-header {\n        padding-right: ").concat(ht(10),";\n        padding-left: ").concat(ht(10),";\n      }\n\n      .message-window-header hr {\n        right: ").concat(ht(10),";\n        left: ").concat(ht(10),";\n      }\n\n      .message-window-header hr + hr {\n        bottom: ").concat(ht(1),";\n      }\n\n      .volume-panel {\n        border-radius: ").concat(dt(7),"\n      }\n\n      .volume-panel .btn {\n        height: ").concat(dt(30),";\n      }\n\n      .volume-panel .slider-bar {\n        transform: ").concat(pt(1),";\n      }\n\n      /* search results */\n      .icon-gear {\n        width: ").concat(dt(14),";\n        height: ").concat(dt(14),";\n      }\n\n      .search-results li div {\n        padding-left: ").concat(ht(20),";\n      }\n\n      .search-results {\n        padding: ").concat(ht(1)," ").concat(ht(3),";\n      }\n\n      .search-filter {\n        top: ").concat(dt(7),";\n        right: ").concat(dt(5),';\n      }\n\n      .search-options input[type="checkbox"] {\n        transform: ').concat(pt(1),";\n      }\n\n      .search-options p {\n        margin-left: ").concat(ht(10),";\n        margin-right: ").concat(ht(10),";\n      }\n\n      .search-options label {\n        margin-left: ").concat(ht(10),";\n        margin-right: ").concat(ht(10),";\n      }\n\n      .search-content {\n        height: calc(100% - ").concat(dt(40),");\n      }\n\n      .search-clear {\n        padding: 0 ").concat(dt(8),";\n      }\n\n      .search-clear svg {\n        height: ").concat(dt(14),";\n        width: ").concat(dt(13),";\n      }\n\n      .search-options {\n        height: ").concat(dt(80),";\n      }\n\n      #light-box-bottom button {\n        height: ").concat(dt(28)," !important;\n      }\n\n    ")}},{key:"update",value:function(){null!=ct&&ct.parentNode.removeChild(ct),ct=document.createElement("style"),document.body.appendChild(ct),ct.innerHTML=this.calcStyles()}}])&&at(t.prototype,n),r&&at(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function bt(e){return bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bt(e)}function vt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==bt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==bt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===bt(o)?o:String(o)),r)}var i,o}var mt=DS,gt=mt.dom,wt=mt._,St=mt.pubSub,kt=mt.globalEventHelper.addWindowListener,Ct=mt.events.slide.HAS_MOUNTED,Ot=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),"show all"!==window.globals.scale&&(this.el=t.el,wt.bindAll(this,"onResize"),this.removeListener=kt("resize",this.onResize),St.once(Ct,this.onResize))}var t,n,r;return t=e,(n=[{key:"onResize",value:function(){var e=document.body;e.clientHeight<this.el.clientHeight||e.clientWidth<this.el.clientWidth?gt.addClass(e,"has-scrollbar"):gt.removeClass(e,"has-scrollbar")}},{key:"teardown",value:function(){null!=this.removeListener&&this.removeListener()}}])&&vt(t.prototype,n),r&&vt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Et="wrapper",xt=DS,Tt=xt.utils.scaleVal,Lt=xt.scaler;function Pt(e){return Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pt(e)}function jt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Pt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Pt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Pt(o)?o:String(o)),r)}var i,o}z.def(Et,Ot,(function(){var e=z.model,t=e.preso.display().width(),n=e.preso.display().height();return{attrs:{id:Et,class:"cs-base cs-".concat(e.frame.default_layout," fn-").concat(e.frame.default_layout)},style:{fontSize:"".concat(Lt.getScaledFontSize(),"%")},w:t,h:n,origin:"0 0",dimScale:function(){return Lt.getScale()},html:function(){return'<div id="frame-overlay" style="z-index:'.concat(999,';"></div>')},x:function(){var e=Tt(t),n=e>window.innerWidth;return Math.max(0,n?0:Math.ceil((window.innerWidth-e)/2))},y:function(){var e=Tt(n),t=e>window.innerHeight;return Math.max(0,t?0:Math.ceil((window.innerHeight-e)/2))},updateHook:function(){this.el.style.fontSize="".concat(Lt.getScaledFontSize(),"%")},add:!0}}));var _t=DS,At=_t.pubSub,Dt=_t.events,It="click",Rt=function(){function e(t){for(var n in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t.el,this.view=t,this.model=t.model,t.children)this[n+"El"]=t.children[n].el;DS._.bindAll(this,"onFocus","onBlur","onLayoutChange"),this.el.addEventListener("focusin",this.onFocus),this.el.addEventListener("focusout",this.onBlur),At.on(Dt.frameModel.LAYOUT_CHANGED,this.onLayoutChange)}var t,n,r;return t=e,(n=[{key:"onClick",value:function(e){this.el.addEventListener(It,e.bind(this))}},{key:"onClickEl",value:function(e,t){e.addEventListener(It,t.bind(this))}},{key:"on",value:function(e,t){this.el.addEventListener(e,t.bind(this))}},{key:"getViewBox",value:function(){return this.view.getBox()}},{key:"onFocus",value:function(){var e=this.getViewBox(),t=e.x,n=e.y,r=e.w,i=e.h;DS.focusManager.setFocusRectOn(this.el,{left:t,top:n,width:r,height:i}),this.hasFocus=!0}},{key:"onBlur",value:function(){DS.focusManager.takeFocusOff(),this.hasFocus=!1}},{key:"teardown",value:function(){At.off(Dt.frameModel.LAYOUT_CHANGED,this.onLayoutChange)}},{key:"onLayoutChange",value:function(){}}])&&jt(t.prototype,n),r&&jt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Bt(e){return Bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bt(e)}function Mt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Bt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Bt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Bt(o)?o:String(o)),r)}var i,o}function Nt(e,t){return Nt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Nt(e,t)}function Ht(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Vt(e);if(t){var i=Vt(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Ft(this,n)}}function Ft(e,t){if(t&&("object"===Bt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Vt(e){return Vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Vt(e)}var Wt="frame",Ut=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nt(e,t)}(o,e);var t,n,r,i=Ht(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onScroll=function(e){e.preventDefault(),t.el.scrollTop=0,t.el.scrollLeft=0},t.el.addEventListener("scroll",t.onScroll),t}return t=o,(n=[{key:"teardown",value:function(){this.el.removeEventListener("scroll",this.onScroll)}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(){}}])&&Mt(t.prototype,n),r&&Mt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt);function Kt(e){return Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kt(e)}function zt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Kt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Kt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Kt(o)?o:String(o)),r)}var i,o}function Gt(e,t){return Gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Gt(e,t)}function qt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Yt(e);if(t){var i=Yt(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Qt(this,n)}}function Qt(e,t){if(t&&("object"===Kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Zt(e)}function Zt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Yt(e){return Yt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yt(e)}z.def(Wt,Ut,(function(){var e=z.model;return{attrs:{id:Wt,class:"cs-base cs-".concat(e.frame.default_layout," fn-").concat(e.frame.default_layout)},w:"100%",h:"100%"}}));var Xt,$t,Jt=DS,en=Jt.detection,tn=Jt.events,nn=Jt.pubSub,rn=function(){return document.getElementById("app-top")||document.body},on=DS.utils.pxify,an=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gt(e,t)}(o,e);var t,n,r,i=qt(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).visualNode=((n=document.createElement("div")).className="skipnav transparent",n.innerText=z.model.getString("acc_skipnavigation"),rn().appendChild(n),window.requestAnimationFrame((function(){$t=n.clientHeight,Xt=n.clientWidth,n.className="skipnav",null!=n.parentNode&&n.parentNode.removeChild(n)})),n),DS._.bindAll(Zt(t),"onButtonClick"),t.onClick(t.onButtonClick),t}return t=o,n=[{key:"onFocus",value:function(){if(!en.device.isMobile){DS.focusManager.takeFocusOff();var e=z.getTopNameSpace().slide,t=e.getBox(),n=t.x,r=t.y,i=t.w,o=t.h;Object.assign(this.visualNode.style,{top:on(r+o-$t-50),left:on(n+i-Xt-50)}),e.el.style.opacity=.6,rn().appendChild(this.visualNode),nn.trigger(tn.skipNav.FOCUSED)}}},{key:"onBlur",value:function(){var e=z.getTopNameSpace().slide,t=this.visualNode.parentNode;e.el.style.opacity=1,null!=t&&t.removeChild(this.visualNode)}},{key:"onButtonClick",value:function(e){this.el.focus(),setTimeout((function(){DS.focusManager.onSlideStarted(null,DS.windowManager.getCurrentWindowSlide())}),100)}}],n&&zt(t.prototype,n),r&&zt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),cn=an,ln="skipnav";function sn(e){return sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sn(e)}function un(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==sn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==sn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===sn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}z.def(ln,cn,(function(){var e=z.model,t=e.getString("acc_skipnavigation");return{tag:"button",attrs:{id:ln,"aria-label":t,tabindex:0},visible:e.sidebarOpts.sidebarEnabled,x:-100,y:-100,w:1,h:1,html:t}}));var fn=DS,dn=fn._,hn=fn.pubSub,pn=fn.events,yn=fn.detection,bn=fn.constants.MOBILE_UI_SIZE,vn="top ".concat(150,"ms ease-in-out");function mn(e){return mn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mn(e)}function gn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==mn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==mn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===mn(o)?o:String(o)),r)}var i,o}function wn(e,t){return wn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wn(e,t)}function Sn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=On(e);if(t){var i=On(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return kn(this,n)}}function kn(e,t){if(t&&("object"===mn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Cn(e)}function Cn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function On(e){return On=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},On(e)}var En=DS,xn=En.pubSub,Tn=En.events,Ln=En._,Pn=En.utils,jn=(En.flagManager,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wn(e,t)}(o,e);var t,n,r,i=Sn(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Ln.bindAll(Cn(t),"onSlideChange","onAriaToggle"),xn.on(Tn.slide.HAS_MOUNTED,t.onSlideChange),xn.on(Tn.slide.ARIA_TOGGLE,t.onAriaToggle),t.teardownPushableSlide=function(e){var t;if(!yn.deviceView.isPhone)return dn.noop;function n(){e.view.el.style.transition=null,e.view.el.style.top="0px"}function r(t){e.view.el.style.transition=vn;var n=.45*window.innerHeight-10,r=-(e.view.bottom()-n),i=e.view.y+r,o=0;i<bn&&yn.theme.isUnified?o=bn-i+10:i<0&&(o=-1*i+10),e.view.el.style.top="".concat(r+o,"px")}function i(){e.view.el.style.transition=vn,e.view.el.style.top="".concat(0,"px")}return hn.on((un(t={},pn.threeSixtyImage.UN_PUSH_LABEL,n),un(t,pn.threeSixtyImage.PUSH_UP_BY_LABEL,r),un(t,pn.threeSixtyImage.PUSH_DOWN_BY_LABEL,i),t)),function(){n(),hn.off(pn.threeSixtyImage.UN_PUSH_LABEL,n),hn.off(pn.threeSixtyImage.PUSH_UP_BY_LABEL,r),hn.off(pn.threeSixtyImage.PUSH_DOWN_BY_LABEL,i)}}(Cn(t)),t}return t=o,(n=[{key:"onAriaToggle",value:function(e){e.hidden?(this.el.setAttribute("aria-hidden",!0),this.el.setAttribute("tabindex",0)):(this.el.removeAttribute("aria-hidden"),this.el.removeAttribute("tabindex"))}},{key:"teardown",value:function(){xn.off(Tn.slide.HAS_MOUNTED,this.onSlideChange),this.teardownPushableSlide()}},{key:"onSlideChange",value:function(e){null!=this.labelEl&&this.view.nameSpace===e.props.windowId&&(this.labelEl.textContent="".concat(z.model.getString("slide"),": ").concat(Pn.stripTags(e.props.model.title())))}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(){}}])&&gn(t.prototype,n),r&&gn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt)),_n="slide";z.def(_n,jn,(function(e){var t=z.getNamespace(e),n=t.frame,r=t.sidebar,i=(t.title,z.model);return{attrs:{id:_n},winScale:function(){return DS.scaler.getScale()},origin:"0 0",w:i.slideWidth,h:i.slideHeight,x:function(){var e=0,t=0;return"left"===r.pos&&(t=r.w),e=r.visible?n.x+t+nt:n.x+n.w/2-this.w/2,e-1},y:function(){var e=n.y+nt;return z.model.hasTopLinks()&&(e+=24),(i.sidebarOpts.titleEnabled||i.sidebarOpts.timeEnabled)&&(e+=23),e},add:!0,html:'<div id="slide-label" data-ref="label" aria-live="polite"></div><main class="slide-container" data-ref="container" aria-live="off" tabindex="-1"></main>',noContent:!0,childViews:["captionContainer"],updateHook:function(){this.children.captionContainer.update()}}}));var An,Dn="logo",In=DS.utils.scaleVal;z.def(Dn,(function(e){var t=z.model,n=z.getNamespace(e).sidebar,r=t.sidebarOpts.html5_logo_url,i=t.sidebarOpts.logoPlaceholderText,o=200,a=150;return{id:Dn,attrs:{class:null!=i?"placeholder":"logo cs-logo"},x:function(){return nt},y:nt,w:o+rt,h:function(){return(null!=An&&r===An.imgSrc?An.imgH:a)+rt},html:function(){var e=this;if(null!=i){var t=document.createElement("div");t.className="placeholder-text",t.style.fontSize="2.5em",t.innerText=i,this.el.appendChild(t)}else{this.img=document.createElement("img"),this.img.onload=function(){o=Math.min(e.img.naturalWidth,200),a=e.img.naturalHeight/e.img.naturalWidth*o,An={imgW:o,imgH:a,imgSrc:r},e.img.width=In(o),e.img.onload=null,n.updateChildren(!0)};var c=DS.utils.resolveAssetUrl(r);this.img.src=c,this.el.appendChild(this.img)}},updateHook:function(){null!=this.img&&(this.img.width=In(o))},visible:t.sidebarOpts.logoEnabled}}));var Rn="sidebar";z.def(Rn,(function(e){var t=z.model,n=z.getNamespace(e).frame;return{tag:"section",attrs:{id:Rn,class:"cs-left","aria-label":"sidebar"},style:{zIndex:1},maxW:400,visible:t.sidebarOpts.sidebarEnabled,pos:t.sidebarOpts.sidebarPos,x:function(){var e=0;return"right"===this.pos&&(e=n.w-this.w),e},w:function(){return this.visible?240:0},h:"100%"}}));var Bn="topBar";z.def(Bn,(function(e){var t=z.getNamespace(e),n=t.sidebar,r=t.frame,i=t.title,o=z.model;return{tag:"section",noTabIndex:!0,attrs:{id:Bn,"aria-label":"top bar"},overflow:"visible",x:function(){return"left"===n.pos?n.right():0},w:function(){return r.w-n.w-nt},h:function(){var e=0;return i.visible||o.sidebarOpts.timeEnabled?e=58:o.hasTopLinks()&&(e=34),e},y:0}}));var Mn="title";function Nn(e){return Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nn(e)}function Hn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,c=[],l=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Fn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Fn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Vn(e,t,n){return(t=Un(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Wn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Un(r.key),r)}}function Un(e){var t=function(e,t){if("object"!==Nn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Nn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Nn(t)?t:String(t)}function Kn(e,t){return Kn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Kn(e,t)}function zn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qn(e);if(t){var i=Qn(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Gn(this,n)}}function Gn(e,t){if(t&&("object"===Nn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return qn(e)}function qn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qn(e){return Qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qn(e)}z.def(Mn,(function(e){var t=z.model,n=z.model.rtl,r=z.getNamespace(e),i=r.timer,o=r.linksLeft,a=r.linksRight;return{id:Mn,tag:"h1",attrs:{class:"presentation-title cs-title ".concat(n?"rtl":""),tabindex:-1},x:n?0:16,y:function(){return(t.hasTopLinks()?8:0)-1},calcTextSize:!0,w:function(){var e=n?5:-10;return this.parent.w-(t.sidebarOpts.timeEnabled?i.w:0)-this.x+e},h:function(){return this.parent.h-16},html:t.title.text,visible:t.sidebarOpts.titleEnabled,updateHook:function(){var e="remove";if(!((n?t.topTabsRight:t.topTabsLeft).length>0)){(n?o:a).calcChildrensWidth((function(e){return"TabPanel"!==e.ViewLogic.name}))+this.lastWidthByText<this.parent.w&&(e="add")}this.el.classList[e]("centered")}}}));var Zn=DS,Yn=Zn.detection,Xn=Zn.events,$n=Zn.pubSub,Jn=Zn.svgUtils,er=Zn._.bindAll,tr={remaining:function(e,t){return DS.utils.formatSecondsAsTime(t-e,!0)},totalelapsed:function(e,t){return[e,t].map((function(e){return DS.utils.formatSecondsAsTime(e,!0)})).join(" / ")},elapsed:function(e){return DS.utils.formatSecondsAsTime(e,!0)},none:function(){return""}},nr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kn(e,t)}(o,e);var t,n,r,i=zn(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),n=i.call(this,e),er(qn(n),"onShow","onHide","onTick"),$n.on((Vn(t={},Xn.timer.SHOW,n.onShow),Vn(t,Xn.timer.HIDE,n.onHide),t)),n.timeFormat=n.getTimeFormat(),n.isPieProgress=Yn.theme.isUnified,n}return t=o,n=[{key:"getTimeFormat",value:function(){if(!Yn.deviceView.isClassicMobile){var e=z.model.sidebarOpts.timeFormat;if(e&&null!=tr[e.toLowerCase()])return e.toLowerCase();if(!z.model.sidebarOpts.timeEnabled)return"none"}return"remaining"}},{key:"teardown",value:function(){var e;$n.off((Vn(e={},Xn.timer.SHOW,this.onShow),Vn(e,Xn.timer.HIDE,this.onHide),e))}},{key:"onTick",value:function(e,t,n){if(this.view.children.timerText.el.innerHTML=tr[this.timeFormat](t,n),!Yn.deviceView.isClassicMobile){var r=Jn.wheelPath(9,9,9,0,360*(1-e),this.isPieProgress);this.view.children.timerPath.el.setAttributeNS(null,"d",r)}}},{key:"onShow",value:function(e){null!=this.currentTimer&&this.onHide(),this.currentTimer=e,this.currentTimer.on("tick",this.onTick),this.toggleVisibility(!0),window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.SHOWN))}},{key:"onHide",value:function(){null!=this.currentTimer&&(this.currentTimer.off("tick",this.onTick),this.currentTimer=null,window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.HIDDEN))),this.toggleVisibility(!1)}},{key:"toggleVisibility",value:function(e){var t=Hn(e?["add","remove"]:["remove","add"],2),n=t[0],r=t[1];document.body.classList[n]("timer-shown"),this.el.classList[r]("hidden"),this.el.classList[n]("shown"),this.view.setVisibility(e),this.view.parent.updateChildren(!0)}},{key:"onFocus",value:function(){var e=this.view.children.timerText.el.getBoundingClientRect(),t=e.left,n=e.top,r=e.width,i=e.height;t-=8,n-=8,r+=37,i+=12,DS.focusManager.setFocusRectOn(this.el,{left:t,top:n,width:r,height:i})}}],n&&Wn(t.prototype,n),r&&Wn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),rr=nr,ir="timer";function or(e){return or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},or(e)}function ar(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,c=[],l=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return cr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return cr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function lr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==or(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==or(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===or(o)?o:String(o)),r)}var i,o}function sr(){return sr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=ur(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},sr.apply(this,arguments)}function ur(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=yr(e)););return e}function fr(e,t){return fr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},fr(e,t)}function dr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=yr(e);if(t){var i=yr(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return hr(this,n)}}function hr(e,t){if(t&&("object"===or(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return pr(e)}function pr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yr(e){return yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yr(e)}z.def(ir,rr,(function(e){return{noTabIndex:!0,attrs:{id:ir,"aria-label":"timer",class:"timer cs-timer"},overflow:"visible",x:function(){return z.getNamespace(e).topBar.w-210},y:6,w:200,h:it,visible:!1,html:'\n      <div class="timer-wheel cs-pie">\n        <svg\n          style="width: 18px; height: 18px; overflow: visible;"\n          width="18"\n          height="18"\n          xmlns:xlink="http://www.w3.org/1999/xlink"\n          focusable="false"\n        >\n        <circle cx="9" cy="9" r="9" fill="url(#timer-yellow-grad)" />\n        <path data-ref="timerPath"\n          d=""\n          fill="url(#timer-blue-grad)"\n          transform="rotate(-90 9 9)"\n          stroke="none" />\n\n          <circle cx="9" cy="9" r="10" fill="none" class="cs-innerstroke" stroke-width="1"/>\n          <circle cx="9" cy="9" r="9" fill="none" class="cs-innerborder" stroke-width="1"/>\n        </svg>\n      </div>\n      <div class="timer-text" data-ref=\'timerText\' tabindex=\'0\' data-tabindex=\'0\'></div>\n    '}}));var br=DS,vr=br._,mr=br.keyManager,gr=br.focusManager,wr=br.windowManager,Sr=br.pubSub,kr=br.events,Cr=br.detection,Or=br.globalEventHelper,Er=Or.addBodyListener,xr=Or.removeBodyListener,Tr=Or.addWindowListener,Lr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fr(e,t)}(o,e);var t,n,r,i=dr(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),vr.bindAll(pr(t),"onKeydown","onResizeRefresh"),t.active=[],Sr.on(kr.sidebar.ACTIVE_TAB_SET,t.onActiveTabSet.bind(pr(t))),Cr.theme.isUnified&&(t.removeResize=Tr("resize",t.onResizeRefresh)),t}return t=o,(n=[{key:"onResizeRefresh",value:function(){var e=this;clearTimeout(this.forceRefresh),this.forceRefresh=setTimeout((function(){e.view.update()}),200)}},{key:"onActiveTabSet",value:function(e){var t=this.view.children.find((function(t){return t.nameKey.includes(e)}));null!=t&&t.viewLogic.showPanel()}},{key:"onLayoutChange",value:function(e){var t=this;if(null!=z.model&&0!==this.view.children.length){var n=(z.model.sidebarTabs||[]).reduce((function(n,r){var i=r.name,o=e[i],a="".concat(i,"Tab");return(vr.isObject(o)?!0===o.enabled:!0===o)&&null!=t.view.children[a]&&n.push(t.view.children[a]),n}),[]);if(this.view.children.forEach((function(e){e.el.setAttribute("aria-selected",!1),e.el.tabIndex=-1})),0===this.active.length&&n.length>0||!n.includes(this.currentTab)){var r=ar(n,1)[0];this.currentTab=r}if(null!=this.currentTab){this.currentTab.el.setAttribute("aria-selected",!0);var i=wr.getCurrentWindow();null!=i&&null!=i.getId&&"_frame"!==i.getId()||(this.currentTab.el.tabIndex=0)}this.active=n}}},{key:"onFocus",value:function(e){this.isFocused||(this.isFocused=!0,this.getInitialActive().setCurrent(),Er("keydown",this.onKeydown))}},{key:"onBlur",value:function(e){this.el.contains(e.relatedTarget)||(this.isFocused=!1,sr(yr(o.prototype),"onBlur",this).call(this),xr("keydown",this.onKeydown))}},{key:"onKeydown",value:function(e){var t=this.currentTab;mr.isActionKey(e.which)?(this.activeTab=this.currentTab,this.activateTab()):mr.isRightKey(e.which)?this.currentTab=this.getNextTab(this.currentTab.model.idx):mr.isLeftKey(e.which)?this.currentTab=this.getPrevTab(this.currentTab.model.idx):mr.isHomeKey(e.which)?this.currentTab=this.getFirstTab():mr.isEndKey(e.which)&&(this.currentTab=this.getLastTab()),t!==this.currentTab&&(null!=t&&(t.el.tabIndex=-1),this.setCurrent())}},{key:"activateTab",value:function(){null!=this.activeTab&&(vr.forEach(this.view.children,(function(e){return e.el.setAttribute("aria-selected",!1)})),this.activeTab.el.setAttribute("aria-selected",!0),this.activeTab.viewLogic.showPanel())}},{key:"setAsActive",value:function(e){this.activeTab=this.currentTab=e}},{key:"tabDidChangeVisibility",value:function(){this.view.visible&&(null!=this.activeTab&&this.activeTab.visible||(this.currentTab=this.activeTab=vr.first(this.getLiveChildren())),this.activateTab())}},{key:"setCurrent",value:function(){var e=this.currentTab;if(null!=e){e.el.tabIndex=0,e.el.focus();var t=e.getBox(),n=t.x,r=t.y,i=t.w,o=t.h;gr.setFocusRectOn(e.el,{left:n,top:r,width:i,height:o})}}},{key:"getInitialActive",value:function(){return this.currentTab=this.activeTab=this.getLiveChildren().find((function(e){return e.el===document.activeElement})),this}},{key:"getNextTab",value:function(e){var t=this.getLiveChildren(),n=t.findIndex((function(t){return t.model.idx===e}))+1;return n===t.length&&(n=0),t[n]}},{key:"getPrevTab",value:function(e){var t=this.getLiveChildren(),n=t.findIndex((function(t){return t.model.idx===e}))-1;return-1===n&&(n=t.length-1),t[n]}},{key:"getFirstTab",value:function(){return this.view.children.find((function(e){return e.viewLogic.isLive()}))}},{key:"getLastTab",value:function(){return this.view.children.slice().reverse().find((function(e){return e.viewLogic.isLive()}))}},{key:"getLiveChildren",value:function(){return this.view.children.filter((function(e){return e.viewLogic.isLive()}))}},{key:"getSelectedTab",value:function(){return this.view.children.find((function(e){return!0===e.viewLogic.isActive}))}},{key:"teardown",value:function(){Sr.off(kr.sidebar.ACTIVE_TAB_SET),o.lastSelectedTab=this.getSelectedTab(),null!=this.removeResize&&this.removeResize()}}])&&lr(t.prototype,n),r&&lr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt);function Pr(e){return Pr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pr(e)}function jr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Pr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Pr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Pr(o)?o:String(o)),r)}var i,o}function _r(e,t){return _r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_r(e,t)}function Ar(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ir(e);if(t){var i=Ir(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Dr(this,n)}}function Dr(e,t){if(t&&("object"===Pr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ir(e){return Ir=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ir(e)}var Rr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_r(e,t)}(o,e);var t,n,r,i=Ar(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).searchEnabled=z.model.frame.controlOptions.controls.search,t.onClick(t.showPanel),t}return t=o,(n=[{key:"didChangeVisibility",value:function(e){this.view.parent.viewLogic.tabDidChangeVisibility()}},{key:"showPanel",value:function(e){if(!this.isActive){var t=this.view.parent,n=t.children;for(var r in n)n[r].viewLogic.hidePanel();this.el.classList.add("cs-selected"),this.el.setAttribute("aria-selected","true"),this.isActive=!0,t.viewLogic.setAsActive(this.view),DS.pubSub.trigger(DS.events.tab.SHOW,this.model.name)}}},{key:"hidePanel",value:function(){this.el.classList.remove("cs-selected"),this.el.setAttribute("aria-selected","false"),this.isActive=!1}},{key:"isLive",value:function(){var e=z.model.currControlLayout[this.model.name];return _.isObject(e)?e.enabled:e}}])&&jr(t.prototype,n),r&&jr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Br="tabs",Mr=DS,Nr=Mr._,Hr=Mr.utils,Fr=Hr.getPath,Vr=Hr.scaleVal,Wr=Hr.pxify,Ur=Mr.constants.refs.FRAME,Kr=(z.def(Br,Lr,(function(e){var t=z.getNamespace(e),n=t.sidebar,r=t.logo,i=z.model.rtl;return{attrs:{id:Br,role:"tablist",tabindex:-1,"aria-label":"sidebar-tabs"},overflow:"visible",x:9,y:function(){return(r?r.bottom():0)+nt-1},w:function(){return n.w-rt},h:function(){return Nr.get(this,"children[0].h",25)},updateHook:function(){var e=this,t=this.children.length;if(0!==t){var n=t<=2,r=this.children.reduce((function(e,t){return t.lastWidthByText+e}),0),o=this.w-8*(t-1),a=(this.w-r)/t,c=this.w/t;if(n&&o>r||!n&&this.w>r){var l=0;this.children.forEach((function(r,i){var o=n?8:0,c=Math.round(r.lastWidthByText+(n?8:a))-o;n||i!==t-1||(c=e.w-l),r.width=c,l+=c}))}else{this.children.slice().sort((function(e,t){return e.lastWidthByText-t.lastWidthByText})).forEach((function(e,n){var r=e.lastWidthByText;r<c?(e.width=r,c+=(c-r)/(t-(n+1))):e.width=c}))}}var s={};i&&(s.toLeft=!0,s.startPos=this.w),this.flowChildren(s)},childDef:function(){Kr(z.model)}}})),function(e){var t=!1;Fr(e,"sidebarTabs",[]).forEach((function(n,r){var i=function(e,t,n,r){var i=t.name,o=e.getString(i),a=e.currControlLayout[i],c=!r&&(Nr.isObject(a)?a.enabled:a),l=i+"Tab",s=z.def(l,Rr,{model:Object.assign(t,{idx:n}),tag:"button",attrs:{id:l,class:"tab cs-tabs ".concat(c?"cs-selected":""),role:"tab","aria-selected":c?"true":"false","aria-label":o,"aria-controls":"".concat(i,"-panel"),tabindex:0===n?0:-1},style:{borderTopLeftRadius:Wr(Vr(6)),borderTopRightRadius:Wr(Vr(6))},calcTextSize:!0,w:function(){return this.width||this.lastWidthByText+rt},padLeft:6,padRight:6,noUpdate:!0,h:function(){return this.lastHeightByText||26},html:o,updateHook:function(){this.el.style.borderTopLeftRadius=this.el.style.borderTopRightRadius=Wr(Vr(6))}});return s.init(),z.getNamespace(Ur).tabs.append(s),c}(e,n,r,t);t=i||t}))});function zr(e){return zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zr(e)}function Gr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==zr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===zr(o)?o:String(o)),r)}var i,o}function qr(){return qr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Qr(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},qr.apply(this,arguments)}function Qr(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Jr(e)););return e}function Zr(e,t){return Zr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Zr(e,t)}function Yr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Jr(e);if(t){var i=Jr(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Xr(this,n)}}function Xr(e,t){if(t&&("object"===zr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return $r(e)}function $r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jr(e){return Jr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Jr(e)}var ei=DS,ti=ei._,ni=ei.pubSub,ri=ei.detection,ii=ei.globalEventHelper,oi=(ii.addDocumentListener,ii.removeDocumentListener,ei.keyManager.isTabKey,ei.playerGlobals),ai=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zr(e,t)}(o,e);var t,n,r,i=Yr(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),ti.bindAll($r(t),"onShow"),ni.on(DS.events.tab.SHOW,t.onShow),t}return t=o,(n=[{key:"getFirstChild",value:function(){return ti.first(this.view.children)}},{key:"focusChild",value:function(){this.getFirstChild().viewLogic.focusSelf()}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(e){oi.presentation.isPreview()&&!window.globals.HAS_SLIDE||ri.deviceView.isDesktop&&null!=this.view.lnk&&this.isTargetOutsidePanel(e.relatedTarget)&&(qr(Jr(o.prototype),"onBlur",this).call(this,e),this.view.lnk.viewLogic.hidePanel())}},{key:"isTargetOutsidePanel",value:function(e){return null==e||!this.el.contains(e)&&ti.camelCase(e.id)!==this.view.lnk.nameKey&&ti.camelCase(e.parentElement.id)!==this.view.lnk.nameKey}},{key:"onShow",value:function(e){var t="".concat(e,"Panel");if(this.view.nameKey===t){var n=this.view.parent.children,r=this.getFirstChild();for(var i in n)i.indexOf("Panel")>-1&&n[i].viewLogic.onHide();this.el.style.display="block",null!=r.onPanelVisible&&r.onPanelVisible()}}},{key:"onHide",value:function(){this.el.style.display="none"}},{key:"isTopLink",value:function(){return null!=this.view.tabsId}},{key:"getTopTabsView",value:function(){return this.view.lnk.parent.parent}},{key:"teardown",value:function(){ni.off(DS.events.tab.SHOW,this.onShow)}}])&&Gr(t.prototype,n),r&&Gr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt);function ci(e){return ci="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ci(e)}function li(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function si(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ci(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ci(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ci(o)?o:String(o)),r)}var i,o}function ui(e,t){return ui=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ui(e,t)}function fi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=hi(e);if(t){var i=hi(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return di(this,n)}}function di(e,t){if(t&&("object"===ci(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function hi(e){return hi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},hi(e)}var pi=DS.detection,yi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ui(e,t)}(o,e);var t,n,r,i=fi(o);function o(){return li(this,o),i.apply(this,arguments)}return t=o,n=[{key:"focusSelf",value:function(){var e=this.view.children,t=e.search,n=e.outline;if(null!=t){var r=t.viewLogic.searchInputEl;pi.deviceView.isUnifiedMobile?(r.style.pointerEvents="none",setTimeout((function(){r.style.pointerEvents="all"}),100)):r.focus()}else n.viewLogic.focusSelf()}},{key:"onFocus",value:function(){}}],n&&si(t.prototype,n),r&&si(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),bi=yi;function vi(e){return vi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vi(e)}function mi(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==vi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==vi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===vi(o)?o:String(o)),r)}var i,o}function gi(e,t){return gi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},gi(e,t)}function wi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ci(e);if(t){var i=Ci(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Si(this,n)}}function Si(e,t){if(t&&("object"===vi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ki(e)}function ki(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ci(e){return Ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ci(e)}var Oi=DS,Ei=Oi.pubSub,xi=Oi.events,Ti=Oi.TweenLite,Li=Oi.focusManager,Pi=Oi._.bindAll,ji=Oi.dom.addClass,_i=Oi.detection,Ai=!1,Di=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gi(e,t)}(o,e);var t,n,r,i=wi(o);function o(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o);var n=(t=i.call(this,e)).view.children.searchForm.el,r=t.view.children.searchInput.el;return n.addEventListener("submit",t.onSearch.bind(ki(t))),_i.deviceView.isMobile&&(Pi(ki(t),"onMobileOutlineShown"),r.addEventListener("input",t.onSearch.bind(ki(t))),Ei.on(xi.mobile.OUTLINE_SHOWN,t.onMobileOutlineShown)),t.ensureEventSubscriptions(),t}return t=o,n=[{key:"teardown",value:function(){Ei.off(xi.mobile.OUTLINE_SHOWN,this.onMobileOutlineShown),Ei.off(xi.controlOptions.RESET,this.onControlOptionsReset,this),Ai=!1}},{key:"ensureEventSubscriptions",value:function(){Ai||(Ei.on(xi.controlOptions.RESET,this.onControlOptionsReset,this),Ai=!0)}},{key:"onControlOptionsReset",value:function(){this.view.updateHtml(),this.view.update()}},{key:"onMobileOutlineShown",value:function(){document.getElementById("outline-panel").appendChild(this.el),ji(document.body,"tab-shown")}},{key:"onFocus",value:function(e){Li.setFocusRectOn(this.view.el)}},{key:"onSearch",value:function(e){e.preventDefault();var t=this.view.parent.children.searchResults,n=this.view.children.bottomDiv,r=document.querySelector("#outline-content ul");Ti.to(r,.2,{alpha:0,onComplete:function(){t.el.style.opacity=0,Ti.to(t.el,.2,{alpha:1,onComplete:function(){return t.viewLogic.onAfterVisible()}}),n.el.style.opacity=0,t.setVisibility(!0)}}),t.viewLogic.performSearch(this.searchInputEl.value),ji(document.body,"search-results-active")}},{key:"updateVisibility",value:function(e){var t=this.view,n=t.searchResults,r=t.searchResults.visible,i=t.visible;this.view.setVisibility(e),n.setVisibility(e&&this.searchResultsOpen),i&&(this.searchResultsOpen=r)}}],n&&mi(t.prototype,n),r&&mi(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Ii=Di,Ri=function(){return"no icon"},Bi={next:function(e){return'<svg class="cs-icon" width="8" height="11" viewBox="0 0 8 11" focusable="false" data-ref="icon">\n    <use xlink:href="#next-icon" class="cs-icon-shadow" transform="translate(0, 1)" />\n    <polygon\n      id="next-icon"\n      transform="translate(0, 1)"\n      points="2.7,0 0,1.7 3,5 0.3,8.3 3,10 7,5" />\n  </svg>'},prev:function(e){return'<svg class="cs-icon" width="8" height="11" viewBox="0 0 8 11" focusable="false" data-ref="icon">\n    <use xlink:href="#prev-icon" class="cs-icon-shadow" transform="translate(0, 1)"></use>\n    <polygon\n      id="prev-icon"\n      transform="translate(0, 1)"\n      points="0,5 4,10 7,8.3 4,5 7,1.7 4.3,0"/>\n  </svg>'},replay:function(e){return'<svg id="icon-replay" class="cs-icon" width="18" height="16" viewBox="0 0 18 16" focusable="false" data-ref="icon">\n    <use xlink:href="#replay-icon" class="cs-icon-shadow" transform="translate(0, 1.5)"></use>\n    <path id="replay-icon" transform="translate(-3, 1)" d="M9.2,15c4.1,0,7.4-3.1,7.7-7c0-0.2,0-0.4,0-0.5C17,3.3,13.5,0,9.2,0C5.7,0,2.6,2.8,1.8,6.1H0l2.6,2.9l3-2.9H3.7c0.6-2.5,2.8-4.3,5.5-4.3c3.3,0,5.9,2.5,5.9,5.7c0,0.2,0,0.4,0,0.5c-0.3,2.9-2.8,5.2-5.9,5.2c-2,0-3.7-0.9-4.8-2.5l-1.8,1.1C4,13.9,6.7,15,9.2,15z"/>\n  </svg>'},play:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:14,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:11;return'<svg id="icon-play" class="cs-icon play-icon" width="'.concat(t,'" height="').concat(e,'" viewBox="0 0 11 14" focusable="false" data-ref="icon">\n    <use xlink:href="#play-icon" class="cs-icon-shadow" transform="translate(0, 1)" />\n    <path\n      id="play-icon"\n      transform="translate(3.5, 1)"\n      d="M1.3 0.1l8.3 5.2c0.5 0.3 0.5 1 0 1.3l-8.3 5.2c-0.5 0.3-1.3 0-1.3-0.7L0 0.8C0 0.3 0.7-0.2 1.3 0.1z" />\n  </svg>')},pause:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:14,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:12;return'<svg id="icon-pause" class="cs-icon pause-icon" width="'.concat(t,'" height="').concat(e,'" viewBox="0 0 12 14" focusable="false" data-ref="icon">\n    <use xlink:href="#pause-icon" class="cs-icon-shadow" transform="translate(0, 1)" />\n    <g id="pause-icon" transform="translate(2, 1)">\n       <rect x="0" width="4" height="14"/>\n       <rect x="8" width="4" height="14"/>\n    </g>\n  </svg>')},volume:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20,r=e>0?"":"hidden",i=e>=5?"":"hidden",o=10==e?"":"hidden";return'<svg class="cs-icon volume-icon" width="'.concat(t,'" height="').concat(n,'" viewBox="0 0 15 20" focusable="false" data-ref="icon">\n    <use xlink:href="#player-volume" class="cs-icon-shadow" transform="translate(0, 1)" />\n    <g id="player-volume" transform="translate(-1, 4)">\n      <polygon points="4.3,10.7 0,10.7 0,4.3 4.3,4.3 8.6,0 8.6,15"/>\n      <rect class="').concat(r,'" x="10" y="5" width="1" height="6"/>\n      <rect class="').concat(i,'" x="12" y="4" width="1" height="8"/>\n      <rect class="').concat(o,'" x="14" y="3" width="1" height="10"/>\n    </g>\n  </svg>')},captions:function(){return'<svg\n    class="cs-icon caption-icon"\n    data-ref="icon"\n    width="22px" height="18px" viewBox="0 0 22 18"\n    focusable="false">\n    <g stroke="none" stroke-width="1" fill-rule="evenodd" transform="translate(0, 1)">\n      <path d="M14.8517422,14 L20.008845,14 C21.1103261,14 22,13.1019465 22,11.9941413 L22,2.00585866 C22,0.897060126 21.1085295,0 20.008845,0 L1.991155,0 C0.889673948,0 0,0.898053512 0,2.00585866 L0,11.9941413 C0,13.1029399 0.891470458,14 1.991155,14 L8.09084766,14 L11.4712949,17.3804472 L14.8517422,14 Z M3,4 L13,4 L13,6 L3,6 L3,4 Z M14,4 L19,4 L19,6 L14,6 L14,4 Z M19,8 L8,8 L8,10 L19,10 L19,8 Z M7,8 L3,8 L3,10 L7,10 L7,8 Z"></path>\n    </g>\n  </svg>'},carrot:function(e){return'<svg\n    style="left:calc('.concat(e,');"\n    class="cs-icon cs-icon-carrot carrot"\n    width="20" height="20" viewBox="-8 -6 20 20"\n    focusable="false">\n    <use xlink:href="#icon-carrot" class="cs-shadow" transform="translate(1, 0)"></use>\n    <polygon id="icon-carrot" points="1,1.5 5,5 1,8.5" transform="translate(0, 2)"></polygon>\n  </svg>')},search:function(){return'\n  <svg class="cs-icon icon-search" width="14" height="14" viewBox="0 0 14 14" focusable="false" data-ref="icon">\n    <path transform="translate(1,3)" d="M13.8,12.5l-3.6-3.6c0,0-0.1-0.1-0.1-0.1c0.6-0.9,1-2,1-3.2C11.1,2.5,8.6,0,5.5,0S0,2.5,0,5.6c0,3.1,2.5,5.6,5.5,5.6c1.2,0,2.4-0.4,3.3-1.1c0,0,0,0.1,0.1,0.1l3.6,3.6c0.3,0.3,0.8,0.3,1.1,0l0.2-0.2\n  C14.1,13.3,14.1,12.8,13.8,12.5z M5.5,9.5c-2.2,0-3.9-1.8-3.9-4c0-2.2,1.8-4,3.9-4c2.2,0,3.9,1.8,3.9,4C9.5,7.8,7.7,9.5,5.5,9.5z"/>\n  </svg>\n  '},filter:function(){return'<svg class="cs-icon icon-gear" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n    <path id="icon-gear" transform="translate(0,3)" d="M11.1,9.8C11.1,9.8,11.1,9.8,11.1,9.8C11.1,9.8,11.1,9.7,11.1,9.8c0-0.1,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1c0,0,0,0,0,0c0-0.1,0.1-0.1,0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1,0.1-0.2c0,0,0,0,0,0c0.1-0.2,0.2-0.5,0.2-0.7l2-0.4V6.4l-2-0.4c0-0.3-0.1-0.5-0.2-0.7c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0,0,0-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.2-1.7l-0.9-0.9L9.7,2.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1-0.1c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0C8.3,2.1,8.1,2.1,7.8,2L7.4,0H6.2L5.9,2c-0.3,0-0.5,0.1-0.7,0.2c0,0,0,0,0,0C5,2.3,5,2.3,4.9,2.3c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0L2.3,1.6L1.4,2.5l1.2,1.7c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0C2.2,5,2.2,5,2.2,5.1c0,0,0,0,0,0c0,0.1,0,0.1-0.1,0.2c0,0,0,0,0,0C2,5.5,1.9,5.8,1.9,6l-2,0.4v1.2l2,0.4c0,0.3,0.1,0.5,0.2,0.7c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-1.2,1.7l0.9,0.9L4,11.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1c0,0,0,0,0,0c0.1,0,0.1,0.1,0.2,0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0.1c0,0,0,0,0,0c0.2,0.1,0.5,0.2,0.7,0.2l0.4,2h1.2l0.4-2c0.3,0,0.5-0.1,0.7-0.2c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.7,1.2l0.9-0.9L11.1,9.8C11,9.8,11,9.8,11.1,9.8C11,9.8,11.1,9.8,11.1,9.8z M6.8,9.2c-1.2,0-2.2-1-2.2-2.2c0-1.2,1-2.2,2.2-2.2C8,4.8,9,5.8,9,7C9,8.2,8,9.2,6.8,9.2z"/>\n  </svg>'},close:function(){return'<svg class="icon" width="10" height="10" viewBox="0 0 10 10" focusable="false" data-ref="icon">\n    <use xlink:href="#close-icon" class="cs-icon-shadow" transform="translate(0, .5)" />\' +\n    <path id="close-icon" d="M9,9.5L9,9.5c-0.5,0.5-1.3,0.5-1.8,0L4.9,7.3L2.6,9.6c-0.5,0.5-1.3,0.5-1.8,0l0,0c-0.5-0.5-0.5-1.3,0-1.8l2.3-2.3L0.8,3.3C0.3,2.8,0.3,2,0.9,1.4l0,0c0.5-0.5,1.3-0.5,1.8,0l2.3,2.3l2.3-2.3c0.5-0.5,1.3-0.5,1.8,0l0,0c0.5,0.5,0.5,1.3,0,1.8L6.7,5.5L9,7.7C9.5,8.2,9.5,9,9,9.5z"/>\n  </svg>'},clear:function(){return'<svg\n    data-ref="icon"\n    class="cs-icon icon-clear"\n    width="13" height="14" viewBox="0 0 13 14"\n    focusable="false">\n    <use xlink:href="#icon-clear" class="cs-icon-shadow" transform="translate(0, 1)" />\n    <path id="icon-clear" d="M6.5,0C2.9,0,0,2.9,0,6.5C0,10.1,2.9,13,6.5,13c3.6,0,6.5-2.9,6.5-6.5C13,2.9,10.1,0,6.5,0z M1.5,6.5c0-2.8,2.2-5,5-5c1.2,0,2.4,0.5,3.2,1.2L2.2,9.1C1.8,8.3,1.5,7.5,1.5,6.5z M6.5,11.5c-1.2,0-2.3-0.5-3.2-1.2L10.8,4c0.4,0.7,0.7,1.6,0.7,2.5C11.5,9.3,9.3,11.5,6.5,11.5z"/>\n  </svg>'},settings:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return'\n    <svg class="cs-icon" data-ref="settings" width="'.concat(16*e,'px" height="').concat(18*e,'px" viewBox="0 0 16 18" focusable="false">\n      <g transform="translate(0, 2)">\n        <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n      </g>\n    </svg>\n    ')},track:function(e,t,n,r,i,o){return'\n      <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="'.concat(24*e,'px" height="').concat(16*e,'px" viewBox="0 0 24 16" focusable="false">\n        <defs>\n            <rect id="').concat(o,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n            <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(o,'-trackFilter">\n                <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n                <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n                <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n            </filter>\n        </defs>\n        <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="').concat(r,'" fill-rule="evenodd" xlink:href="#').concat(o,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(o,'-trackFilter)" xlink:href="#').concat(o,'-track"></use>\n                <use stroke="').concat(i,'" stroke-width="1" xlink:href="#').concat(o,'-track"></use>\n                <circle fill="').concat(n,'" cx="7" cy="8" r="5"></circle>\n            </g>\n        </g>\n        <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="').concat(r,'" fill-rule="evenodd" xlink:href="#').concat(o,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(o,'-trackFilter)" xlink:href="#').concat(o,'-track"></use>\n                <use stroke="').concat(i,'" stroke-width="1" xlink:href="#').concat(o,'-track"></use>\n                <circle fill="').concat(t,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n            </g>\n        </g>        \n    </svg>\n    ')},downArrow:function(e){return'\n      <svg class="panel-down-arrow" data-ref="arrow" width="22px" height="11px" viewBox="0 0 22 11" focusable="false">\n        <path d="M 0 0 L 11 11 22 0" fill="'.concat(e,'"/>\n      </svg>\n    ')}},Mi=function(e){return Bi[e]||Ri},Ni="search",Hi=DS,Fi=Hi.utils,Vi=(Fi.pxify,Fi.scaleVal,Hi.playerGlobals),Wi=z.def(Ni,Ii,(function(){var e=z.model,t=z.getCurrentNameSpaceString(),n=function(){return fe.getColor(t,".cs-outline .cs-diva","border-color")},r=function(){return fe.getColor(t,".cs-outline .cs-divb","border-color")};return{attrs:{class:Ni,"aria-label":e.getString("acc_search_input"),tabIndex:-1},z:3,x:0,y:function(){return this.parent.y+this.parent.h-48},w:function(){return this.parent.w},h:48,html:function(){return'\n      <div class="search-ui">\n        <div data-ref="bottomDiv">\n          <hr class="cs-diva diva" style="border-color: '.concat(n(),'">\n          <hr class="cs-divb divb" style="border-color: ').concat(r(),'">\n        </div>\n        <form id="outline-search" data-ref="searchForm" class="search-input cs-outlinesearch cs-searchinput" tabindex="-1">\n          <input class="cs-input" data-ref="searchInput" type="search" placeholder="').concat(e.getString("search"),'" tabindex="0">\n          <button\n            class="cs-button btn-unstyled" style="background-repeat: no-repeat;"\n            data-ref="searchBtn"\n            tabindex="-1"\n          >').concat(Mi("search")(),"</button>\n        </form>\n      </div>")},updateHook:function(){Vi.presentation.isPreview()&&(this.children.bottomDiv.el.querySelector("hr.cs-diva").style.borderColor=n(),this.children.bottomDiv.el.querySelector("hr.cs-divb").style.borderColor=r()),this.viewLogic.ensureEventSubscriptions()}}}));function Ui(e){return Ui="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ui(e)}function Ki(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Ui(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ui(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Ui(o)?o:String(o)),r)}var i,o}function zi(){return zi="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Gi(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},zi.apply(this,arguments)}function Gi(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Xi(e)););return e}function qi(e,t){return qi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qi(e,t)}function Qi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Xi(e);if(t){var i=Xi(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Zi(this,n)}}function Zi(e,t){if(t&&("object"===Ui(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Yi(e)}function Yi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xi(e){return Xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Xi(e)}var $i=DS,Ji=$i.focusManager,eo=$i.keyManager,to=$i.detection,no=$i.globalEventHelper,ro=no.addDocumentListener,io=no.removeDocumentListener,oo=($i.utils.getPath,$i.dom),ao=oo.parentNodesOf,co=oo.addClass,lo=oo.removeClass,so=$i._,uo=so.first,fo=so.last,ho=so.bindAll,po=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qi(e,t)}(o,e);var t,n,r,i=Qi(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),ho(Yi(t),"onKeydown","addEvents","onClickItem"),t.addEvents(),t}return t=o,(n=[{key:"focusSelf",value:function(){this.hasListItems()&&this.getItems().find((function(e){return 0===e.tabIndex})).focus()}},{key:"addEvents",value:function(){var e=this;to.device.isMobile?(this.el.addEventListener("touchmove",(function(t){e.moved=!0})),this.onClick((function(t){e.moved||e.onClickItem(t),e.moved=!1}))):this.onClick(this.onClickItem)}},{key:"onClickItem",value:function(){}},{key:"onFocus",value:function(){!this.isFocused&&this.hasListItems()&&(this.isFocused=!0,this.currentItem=this.currentItem||uo(this.getItems()),co(this.currentItem,"hover"),eo.isShowFocus&&this.centerOnFocused(),Ji.setFocusRectOn(this.getFocusRectTarget()),ro("keydown",this.onKeydown))}},{key:"onBlur",value:function(e){this.el.contains(e.relatedTarget)||(zi(Xi(o.prototype),"onBlur",this).call(this,e),null!=this.currentItem&&(lo(this.currentItem,"hover"),this.currentItem.style.backgroundColor=""),io("keydown",this.onKeydown),this.isFocused=!1)}},{key:"onKeydown",value:function(e){var t=this.currentItem;eo.isActionKey(e.which)?(this.activateItem(e),eo.isSpaceKey(e.which)&&e.preventDefault()):eo.isDownishKey(e.which)?this.currentItem=this.getNextItem(this.getItemContent()):eo.isUpishKey(e.which)?this.currentItem=this.getPrevItem(this.getItemContent()):eo.isHomeKey(e.which)?this.currentItem=this.getFirstItem():eo.isEndKey(e.which)&&(this.currentItem=this.getLastItem()),t!==this.currentItem&&(e.preventDefault(),t.tabIndex=-1,lo(t,"hover"),this.focusOnCurrent())}},{key:"hasListItems",value:function(){return!1}},{key:"getFocusRectTarget",value:function(){return this.currentItem}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem&&(this.currentItem.tabIndex=0,co(this.currentItem,"hover"),this.currentItem.focus()),Ji.setFocusRectOn(this.getFocusRectTarget())}},{key:"activateItem",value:function(){this.onClickItem({target:this.currentItem.firstElementChild})}},{key:"getNextItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))+1;return n===this.getItems().length&&(n=0),this.getItems()[n]}},{key:"getPrevItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))-1;return-1===n&&(n=this.getItems().length-1),this.getItems()[n]}},{key:"getLastItem",value:function(){return fo(this.getItems())}},{key:"getFirstItem",value:function(){return uo(this.getItems())}},{key:"getItemContent",value:function(){return this.currentItem.textContent.trim()}},{key:"findIndexCb",value:function(e,t){return e.textContent.trim()===t}},{key:"getItems",value:function(){return[]}},{key:"getScrollEl",value:function(){return this.el}},{key:"getOffsetEl",value:function(){return this.el}},{key:"getOffsetTop",value:function(e){return function(e){return ao(e,(function(e){return"li"===e.nodeName.toLowerCase()})).reduce((function(e,t){return e+t.offsetTop}),0)}(e)}},{key:"getOffsetHeight",value:function(e){return e.offsetHeight}},{key:"centerOnFocused",value:function(){if(null!=this.currentItem){var e=this.getOffsetEl(),t=this.getOffsetHeight(e),n=this.getScrollEl().scrollTop,r=this.getOffsetTop(this.currentItem),i=r+this.getOffsetHeight(this.currentItem);i-n>t?e.scrollTop=i-t+10:r<n+10&&(e.scrollTop=r-10)}}}])&&Ki(t.prototype,n),r&&Ki(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),yo=function(){return"no icon"},bo={next:function(){return'\n  <svg class="cs-icon next-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="rotate(180, 5, 8)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>'},prev:function(){return'\n  <svg class="cs-icon prev-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="translate(0, 1)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>\n'},submit:function(){return'\n   <svg class="cs-icon check-icon" width="17px" height="18px" viewBox="0 0 17 16" focusable="false">\n\n  <path stroke="none" transform="translate(0, 1)" d="\n  M 17 1.4\n  L 15.6 0 5.7 9.9 1.4 5.65 0 7.05 5.65 12.75 5.7 12.75 17 1.4 Z"/>\n\n  </svg>'},replay:function(){return'<svg class="cs-icon" x="0px" y="0px" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 10.95 8.75\n      Q 11 9 11 9.25 10.95 11.15 9.7 12.4 8.4 13.7 6.5 13.75 4.6 13.7 3.3 12.4 2.05 11.15 2 9.25 2.05 7.3 3.3 6.05 4.398828125 4.998828125 6 4.75\n      L 6 6.9\n      Q 6.05 7.75 6.85 7.35\n      L 11.35 4.3\n      Q 11.7 4.05 11.7 3.75 11.7 3.45 11.35 3.2\n      L 6.85 0.15\n      Q 6.05 -0.3 6 0.6\n      L 6 2.75\n      Q 3.4517578125 3.001171875 1.8 4.75 0.05 6.6 0 9.25 0.05 12 1.9 13.85 3.75 15.65 6.5 15.75 9.25 15.65 11.1 13.85 12.95 12 13 9.25 13 9 13 8.75\n      L 10.95 8.75 Z"/>\n    </svg>'},play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="11" height="13" viewBox="0 0 11 13" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 0.851 13.011\n      C 0.381 13.295 0 13.068 0 12.526\n      L 0 0.771\n      C 0 0.219 0.378 0 0.854 0.288\n      L 10.507 6.132\n      C 10.979 6.417 10.981 6.878 10.504 7.168\n      L 6.307 9.708\n      L 0.851 13.011 Z" />\n  </svg>'},pause:function(){return'<svg id="icon-pause" class="cs-icon pause-icon" width="9" height="14" viewBox="0 0 9 14" focusable="false">\n    <rect x="0" width="3" height="14"/>\n    <rect x="6" width="3" height="14"/>\n  </svg>'},volume:function(e,t){var n=Math.min(1,e/5),r=Math.min(1,Math.max(0,e/5-.5));return'<svg class="cs-icon volume-icon '.concat(t?"volume-icon-selected":"",'" width="16px" height="14px" viewBox="0 0 16 14" focusable="false">\n      <rect x="0" y="4" width="3" height="6"></rect>\n      <polygon points="4 4 9 0 9 14 4 10"></polygon>\n      <g transform="translate(10, 0)">\n        <mask id="vol-mask" fill="white">\n          <rect id="path-1" x="0" y="0" width="8" height="14" style="fill: white;"></rect>\n        </mask>\n        <circle strokeWidth="1.5" style="opacity: ').concat(r,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="6.5"></circle>\n        <circle strokeWidth="1.5" style="opacity: ').concat(n,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="3.5"></circle>\n      </g>\n    </g>\n  </svg>')},captionsOn:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <path fill="#FFFFFF" stroke="none" d="M 19 2 Q 19 1.15 18.4 0.6 17.85 0 17 0 L 2 0 Q 1.15 0 0.6 0.6 0 1.15 0 2 L 0 12 Q 0 12.85 0.6 13.4 1.15 14 2 14 L 7.6 14 9.5 15.9 11.4 14 17 14 Q 17.85 14 18.4 13.4 19 12.85 19 12 L 19 2 M 15.7 4.2 L 15.25 4.85 Q 15.15 4.9 15.1 5 15 5.05 14.85 5.05 14.75 5.05 14.6 4.95 14.5 4.9 14.3 4.8 14.15 4.65 13.9 4.6 13.65 4.5 13.3 4.5 12.85 4.5 12.5 4.7 12.15 4.85 11.9 5.15 11.7 5.45 11.6 5.9 11.45 6.35 11.45 6.9 11.5 7.45 11.6 7.9 11.7 8.35 11.95 8.65 12.2 8.95 12.5 9.15 12.85 9.3 13.25 9.3 13.65 9.3 13.9 9.2 14.2 9.1 14.35 8.95 14.5 8.85 14.65 8.75 14.8 8.65 14.95 8.65 15.15 8.65 15.25 8.8 L 15.75 9.4 Q 15.45 9.75 15.15 10 14.8 10.2 14.45 10.35 14.05 10.5 13.7 10.55 13.3 10.6 12.95 10.6 12.25 10.6 11.65 10.35 11.1 10.1 10.65 9.65 10.2 9.15 9.95 8.45 9.7 7.75 9.7 6.9 9.7 6.1 9.95 5.4 10.15 4.75 10.6 4.25 11.05 3.75 11.7 3.5 12.35 3.2 13.2 3.2 14 3.2 14.6 3.45 15.2 3.7 15.7 4.2 M 5.85 4.7 Q 5.5 4.85 5.25 5.15 5.05 5.45 4.95 5.9 4.8 6.35 4.8 6.9 4.85 7.45 4.95 7.9 5.05 8.35 5.3 8.65 5.55 8.95 5.85 9.15 6.2 9.3 6.6 9.3 7 9.3 7.25 9.2 7.55 9.1 7.7 8.95 7.85 8.85 8 8.75 8.15 8.65 8.3 8.65 8.5 8.65 8.6 8.8 L 9.1 9.4 Q 8.8 9.75 8.5 10 8.15 10.2 7.8 10.35 7.4 10.5 7.05 10.55 6.65 10.6 6.3 10.6 5.6 10.6 5 10.35 4.45 10.1 4 9.65 3.55 9.15 3.3 8.45 3.05 7.75 3.05 6.9 3.05 6.1 3.3 5.4 3.5 4.75 3.95 4.25 4.4 3.75 5.05 3.5 5.7 3.2 6.55 3.2 7.35 3.2 7.95 3.45 8.55 3.7 9.05 4.2 L 8.6 4.85 Q 8.5 4.9 8.45 5 8.35 5.05 8.2 5.05 8.1 5.05 7.95 4.95 7.85 4.9 7.65 4.8 7.5 4.65 7.25 4.6 7 4.5 6.65 4.5 6.2 4.5 5.85 4.7 Z"/>\n          </svg>'},captionsOff:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <g>\n              <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n              <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n            </g>\n          </svg>'},captions:function(){return'\n    <svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n      <g>\n        <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n        <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n      </g>\n    </svg>'},carrot:function(e){return'\n    <svg style="left:calc('.concat(e,');" class="cs-icon cs-icon-carrot carrot"width="30" height="30" viewBox="0 0 30 30" focusable="false">\n      <g transform="translate(8, 8)">\n        <polygon style="fill:currentColor !important" points="1,1.5 5,5 1,8.5"/>\n      </g>\n  </svg>')},search:function(){return'\n    <svg class="search-icon" width="13px" height="15px" viewBox="0 0 13 15" focusable="false"\n      <g fill="none" fill-rule="evenodd">\n        <g stroke-width="2">\n          <circle cx="5.6" cy="5.6" r="4.6"/>\n          <path d="M8 9l4 5"/>\n        </g>\n      </g>\n    </svg>\n    '},searchClear:function(){return'\n    <svg class="cs-icon icon" width="11px" height="11px" viewBox="0 0 11 11">\n    <g id="Desktop-Color-Contrast" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="Search" transform="translate(-290.000000, -69.000000)" fill="fill:currentColor !important" fill-rule="nonzero">\n            <g id="search" transform="translate(13.000000, 59.000000)">\n                <polygon id="ic_close" points="286.777666 10 282.500215 14.2779053 278.222334 10 277 11.2222382 281.277881 15.5002869 277 19.7779053 278.222334 21 282.500215 16.7222382 286.777666 21 288 19.7779053 283.722119 15.5002869 288 11.2222382"></polygon>\n            </g>\n        </g>\n    </g>\n    </svg>\n    '},filter:function(){return'<svg class="cs-icon icon-gear" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n    <path id="icon-gear" transform="translate(0,3)" d="M11.1,9.8C11.1,9.8,11.1,9.8,11.1,9.8C11.1,9.8,11.1,9.7,11.1,9.8c0-0.1,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1c0,0,0,0,0,0c0-0.1,0.1-0.1,0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1,0.1-0.2c0,0,0,0,0,0c0.1-0.2,0.2-0.5,0.2-0.7l2-0.4V6.4l-2-0.4c0-0.3-0.1-0.5-0.2-0.7c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0,0,0-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.2-1.7l-0.9-0.9L9.7,2.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1-0.1c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0C8.3,2.1,8.1,2.1,7.8,2L7.4,0H6.2L5.9,2c-0.3,0-0.5,0.1-0.7,0.2c0,0,0,0,0,0C5,2.3,5,2.3,4.9,2.3c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0L2.3,1.6L1.4,2.5l1.2,1.7c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0C2.2,5,2.2,5,2.2,5.1c0,0,0,0,0,0c0,0.1,0,0.1-0.1,0.2c0,0,0,0,0,0C2,5.5,1.9,5.8,1.9,6l-2,0.4v1.2l2,0.4c0,0.3,0.1,0.5,0.2,0.7c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-1.2,1.7l0.9,0.9L4,11.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1c0,0,0,0,0,0c0.1,0,0.1,0.1,0.2,0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0.1c0,0,0,0,0,0c0.2,0.1,0.5,0.2,0.7,0.2l0.4,2h1.2l0.4-2c0.3,0,0.5-0.1,0.7-0.2c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.7,1.2l0.9-0.9L11.1,9.8C11,9.8,11,9.8,11.1,9.8C11,9.8,11.1,9.8,11.1,9.8z M6.8,9.2c-1.2,0-2.2-1-2.2-2.2c0-1.2,1-2.2,2.2-2.2C8,4.8,9,5.8,9,7C9,8.2,8,9.2,6.8,9.2z"/>\n  </svg>'},close:function(){return'\n    <svg class="cs-icon icon-close" width="20" height="20" viewBox="0 0 36 36" focusable="false">\n      <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n    </svg>'},clear:function(){return'<svg class="cs-icon icon-clear" width="13" height="14" viewBox="0 0 13 14" focusable="false">\n    <use xlink:href="#icon-clear" fill="rgba(240, 240, 240, 1)" transform="translate(0, 1)" />\n    <path id="icon-clear" transform="translate(3,3)" d="M6.5,0C2.9,0,0,2.9,0,6.5C0,10.1,2.9,13,6.5,13c3.6,0,6.5-2.9,6.5-6.5C13,2.9,10.1,0,6.5,0z M1.5,6.5c0-2.8,2.2-5,5-5c1.2,0,2.4,0.5,3.2,1.2L2.2,9.1C1.8,8.3,1.5,7.5,1.5,6.5z M6.5,11.5c-1.2,0-2.3-0.5-3.2-1.2L10.8,4c0.4,0.7,0.7,1.6,0.7,2.5C11.5,9.3,9.3,11.5,6.5,11.5z"/>\n  </svg>'},hamburger:function(){return'\n    <svg class="cs-icon" width="30px" height="12px" viewBox="0 10 30 12" focusable="false">\n      <path transform="translate(0, 1)" d="M0,15 L17,15 L17,17 L0,17 L0,15 Z M0,11 L17,11 L17,13 L0,13 L0,11 Z M0,19 L17,19 L17,21 L0,21 L0,19 Z" ></path>\n    </svg>\n  '},file:function(){return'\n    <svg width="20px" height="27px" viewBox="0 0 40 50" role="presentation" focusable="false">\n      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">\n        <g>\n          <path class="file-icon-bg" d="M2.00804893,0 C0.899034128,0 0,0.889064278 0,1.99091407 L0,48.0090859 C0,49.1086374 0.892756032,50 1.99862555,50 L37.2170607,50 C38.3208711,50 39.2156863,49.1011186 39.2156863,47.993136 L39.2156863,13.6363636 L26.1437908,0 L2.00804893,0 Z"></path>\n          <path class="file-icon-fold" d="M26.1437908,0 L26.1437908,11.7296861 C26.1437908,12.8319383 27.0422752,13.7254902 28.1433598,13.7254902 L39.2156863,13.7254902"></path>\n        </g>\n      </g>\n    </svg>'},link:function(e){return'\n  <svg class="link-icon" preserveAspectRatio="none" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" role="presentation" focusable="false">\n      <path fill="'.concat(e,'" stroke="none" d="\n            M 1.45 8.5\n            Q 0.0453125 10.0015625 0 11.9\n            L 0 12.15\n            Q 0.0453125 14.0484375 1.45 15.55\n            L 1.5 15.6\n            Q 3.0015625 17.0046875 4.85 17.05\n            L 5.1 17.05\n            Q 7.0150390625 17.0046875 8.5 15.6\n            L 10.65 13.45\n            Q 10.95 13.15 10.95 12.75 10.95 12.35 10.65 12.05 10.3689453125 11.7689453125 10 11.75\n            L 9.95 11.75\n            Q 9.55 11.75 9.2 12.05\n            L 7.1 14.15\n            Q 6.2 15.05 5 15.05 3.8 15.05 2.9 14.15 2 13.25 2 12.05 2 10.8826171875 2.85 9.95\n            L 5 7.8\n            Q 5.3 7.5 5.3 7.1\n            L 5.3 7.05\n            Q 5.2810546875 6.6810546875 5 6.4 4.7 6.1 4.3 6.1 3.9 6.1 3.55 6.4\n            L 1.45 8.5\n            M 12.05 5\n            Q 11.75 4.7 11.35 4.7 10.95 4.7 10.65 5\n            L 5 10.65\n            Q 4.7 10.95 4.7 11.35 4.7 11.75 5 12.05 5.3 12.35 5.7 12.35 6.1 12.35 6.4 12.05\n            L 12.05 6.4\n            Q 12.35 6.1 12.35 5.7 12.35 5.3 12.05 5\n            M 15.6 1.5\n            L 15.55 1.45\n            Q 14 0 12.05 0\n            L 12 0\n            Q 10.05 0 8.5 1.45\n            L 6.4 3.55\n            Q 6.1 3.9 6.1 4.3 6.1 4.7 6.4 5 6.7 5.3 7.1 5.3 7.5 5.3 7.8 5\n            L 9.95 2.85\n            Q 10.8826171875 2 12.05 2 13.25 2 14.15 2.9 15.05 3.8 15.05 5 15.05 6.2 14.15 7.1\n            L 12.05 9.2\n            Q 11.75 9.55 11.75 9.95 11.75 10.35 12.05 10.65 12.35 10.95 12.75 10.95 13.15 10.95 13.45 10.65\n            L 15.6 8.5\n            Q 17.05 6.96875 17.05 5 17.05 3.05 15.6 1.5 Z"/>\n          </svg>')},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},playbackSpeed:function(){return'\n    <svg class="cs-icon" width="15" height="15" viewBox="0 0 15 15" focusable="false">\n      <path d="M5.9 4.0L10.4 7.4L5.9 10.8V4.0ZM1.5 8.2H0.0C0.1 9.6 0.6 10.9 1.5 12.0L2.6 11.0C2.0 10.1 1.6 9.2 1.5 8.2H1.5ZM15 7.4H14.9C14.9 5.6 14.3 3.8 13.0 2.4C11.8 1.0 10.0 0.1 8.2 0.0V1.5C10.1 1.7 11.8 2.9 12.8 4.7C13.7 6.4 13.7 8.5 12.8 10.2C11.8 12.0 10.1 13.1 8.2 13.4V14.9C10.0 14.7 11.8 13.8 13.0 12.5C14.3 11.1 14.9 9.3 14.9 7.4L15 7.4ZM3.6 12.1L2.5 13.1C3.7 14.1 5.1 14.8 6.7 14.9V13.4V13.4C5.5 13.3 4.5 12.8 3.6 12.1V12.1ZM2.6 3.9L1.5 2.8C0.6 3.9 0.1 5.3 0 6.7H1.5H1.5C1.6 5.7 2.0 4.7 2.6 3.9H2.6ZM6.7 1.5V0.0C5.1 0.1 3.7 0.7 2.5 1.7L3.6 2.8C4.5 2.1 5.5 1.6 6.7 1.5V1.5Z" stroke="none" />\n    </svg>\n  '},track:function(e,t){return'\n    <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n      <defs>\n        <rect id="'.concat(t,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n        <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(t,'-trackFilter">\n          <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n          <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n          <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n          <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n        </filter>\n      </defs>\n      <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle class="thumb" stroke-width="0" cx="8" cy="8" r="6"></circle>\n        </g>\n      </g>\n      <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle fill="').concat(e,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n        </g>\n      </g>\n    </svg>\n  ')},downArrow:function(e,t){return'\n    <div style="height: 100%; width: 100%; background-color: '.concat(e,"; border-right: 1px solid; border-bottom: 1px solid; border-color: ").concat(t,'; border-bottom-right-radius: 3px; transform: rotate(45deg);" />\n    ')},checkmark:function(){return'<svg  class="cs-icon check-icon" focusable="false" width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n          <polygon style="fill:currentColor !important" points="12.04 4 13.45 5.41 6.25 12.62 2 8.36 3.41 6.95 6.25 9.79"></polygon>\n      </g>\n    </svg>'},lock:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 Z"></path>\n        </g>\n    </g>\n</svg>'},lockedViewed:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M7.11248548,6.17405922 C7.38175483,6.47268397 7.38175483,6.92847965 7.0966461,7.21138731 L4.53066757,9.75755627 C4.38811321,9.8990101 4.19804072,9.96187847 4.02380761,9.96187847 C3.83373513,9.96187847 3.65950202,9.8990101 3.51694766,9.75755627 L2.34544071,8.45018243 C2.06033199,8.16727477 2.06033199,7.71147909 2.34544071,7.42857143 C2.63054944,7.14566377 3.08989127,7.14566377 3.375,7.42857143 L4.02380761,8.21728122 L6.08292619,6.17405922 C6.36803491,5.89115155 6.82737675,5.89115155 7.11248548,6.17405922 Z M4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 Z"></path>\n        </g>\n    </g>\n</svg>'}},vo={play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="14" height="16" viewBox="0 0 14 16" focusable="false">\n    <path d="M1.4 15.4C0.8 15.8 0 15.3 0 14.5L0 1.4C0 0.6 0.8 0.1 1.4 0.5L12.9 7.1C13.5 7.5 13.5 8.4 12.9 8.8L8.0 11.6L1.4 15.4Z" stroke="none" />\n  </svg>'}},mo=function(e){return DS.detection.env.isPerpetual?bo[e]||yo:vo[e]||bo[e]||yo};function go(e){return go="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},go(e)}function wo(e){return function(e){if(Array.isArray(e))return Co(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ko(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function So(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,c=[],l=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||ko(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ko(e,t){if(e){if("string"==typeof e)return Co(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Co(e,t):void 0}}function Co(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Oo(e,t){return Oo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Oo(e,t)}function Eo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Lo(e);if(t){var i=Lo(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return xo(this,n)}}function xo(e,t){if(t&&("object"===go(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return To(e)}function To(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lo(e){return Lo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Lo(e)}function Po(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==go(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==go(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===go(o)?o:String(o)),r)}var i,o}function jo(e,t,n){return t&&Po(e.prototype,t),n&&Po(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ao=function(e){return et(e.slideid)},Do=DS,Io=Do.windowManager,Ro=Do.focusManager,Bo=Do.TweenLite,Mo=Do.events,No=Do.pubSub,Ho=Do.keyManager,Fo=Do.resolver,Vo=Do.dom,Wo=(Vo.hasClass,Vo.removeClass),Uo=Vo.addClass,Ko=Vo.getParentWithClass,zo=Do._,Go=zo.bindAll,qo=(zo.first,Do.utils),Qo=qo.stripTags,Zo=(qo.stripPlayer,qo.prefixWithPlayer),Yo=Do.globalEventHelper,Xo=Yo.addDocumentListener,$o=(Yo.removeDocumentListener,Do.detection),Jo=$o.theme,ea=$o.os,ta=jo((function e(t,n,r){_o(this,e),this.slideId=t,this.title=n,this.text=r})),na=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Oo(e,t)}(n,e);var t=Eo(n);function n(e){var r;_o(this,n),r=t.call(this,e),Go(To(r),"traverseLinks","performSearch","onSlideChanged","onClear","onReset","loadSlideBankSearchData"),r.animationDuration=.2;var i=z.model.frame.notesData||[];return r.notesBySlideId=i.map((function(e){return new ta(Zo(e.slideId),DS.utils.getPath(Fo.resolvePath(e.slideId,DS.presentation),"title"),Qo(e.content).trim())})),r.slideTextData=z.model.frame.navData.search||[],r.slideTextBySlideId=r.slideTextData.filter((function(e){return!e.slidebank})).map((function(e){return new ta(Zo(e.slideid),DS.utils.getPath(Fo.resolvePath(e.slideid,DS.presentation),"title"),Qo(e.Text).trim())})),r.menuOptions=z.model.frame.controlOptions.menuOptions,r.wrapListItems=r.menuOptions.wrapListItems,r.links=z.model.frame.navData.outline.links,r.hasSlideBankSlides=r.slideTextData.some((function(e){return e.slidebank})),r.hasSlideBankSlides&&No.on(Mo.player.RESET,r.onReset),null!=r.clearEl&&r.onClickEl(r.clearEl,r.onClear),No.on(Mo.search.CLEAR,r.onClear),r.onClickEl(r.searchFilterEl,r.onToggleSearchOptions),r.onClick(r.onClickLink),No.on(Mo.window.MAIN_CHANGED,r.onSlideChanged),r}return jo(n,[{key:"loadSlideBankSearchData",value:function(){this.slideBankTextBySlideId=this.slideTextData.filter((function(e){return e.slidebank})).reduce((function(e,t){var n=Fo.getSlideBankSlideInstance(t.slideid);return null!==n&&e.push(new ta(n.absoluteId,n.title(),Qo(t.Text))),e}),[])}},{key:"teardown",value:function(){No.off(Mo.window.MAIN_CHANGED,this.onSlideChanged),window.globals.HAS_SLIDE&&No.off(Mo.search.CLEAR,this.onClear)}},{key:"onSlideChanged",value:function(e){var t=this.resultsEl.querySelector(".cs-selected"),n=this.resultsEl.querySelector('[data-slide-id="'.concat(e.absoluteId,'"]'));null!=t&&Wo(t,"cs-selected"),null!=n&&(Uo(n,"cs-selected"),Uo(n,"cs-viewed"),this.updateAriaLabel(n))}},{key:"onReset",value:function(){this.onClear(),this.loadSlideBankSearchData()}},{key:"onFocus",value:function(e){e.relatedTarget;var t=e.target;if(this.el.contains(t)){if(t===this.searchFilterEl||t===this.clearEl)return Ro.setFocusRectOn(t),this.isFocused=!1,!1;if(t===this.notesCheckEl||t===this.slideCheckEl)return Ro.setFocusRectOn(t.parentElement),this.isFocused=!1,!1;if(!this.isFocused&&(!Jo.isUnified||Ho.isShowFocus)){var n=So(this.getItems(),1)[0];this.isFocused=!0,null!=n&&(this.currentItem=n,this.lastSelected!==this.currentItem&&this.focusOnCurrent(),Xo("keydown",this.onKeydown))}}return!1}},{key:"getIsSearchVisible",value:function(){return this.searchOptionsEl.classList.contains("visible")}},{key:"setCheckboxesVisible",value:function(e){this.notesCheckEl.hidden=e,this.slideCheckEl.hidden=e}},{key:"onToggleSearchOptions",value:function(e){var t=this,n=this.getIsSearchVisible(),r=n?"remove":"add";this.searchOptionsEl.classList[r]("visible"),n?(clearTimeout(this.checkBoxTimeout),this.checkBoxTimeout=setTimeout((function(){t.setCheckboxesVisible(n)}),200)):this.setCheckboxesVisible(n)}},{key:"onClickLink",value:function(e){if("locked"!==this.menuOptions.flow){var t=Ko(e.target,"listitem"),n=t.dataset.slideId;null==n||"restricted"===this.menuOptions.flow&&!e.target.classList.contains("cs-viewed")||(Uo(t,"cs-viewed"),this.updateAriaLabel(t),No.trigger(Mo.request.NEXT_SLIDE,n))}}},{key:"updateAriaLabel",value:function(e){Jo.isUnified&&wo(e.querySelector(".outline-states").children).forEach((function(t){if("none"===window.getComputedStyle(t).display);else{var n=t.getAttribute("aria-label"),r=e.getAttribute("data-slide-title");e.children[0].textContent=r+" "+n}}))}},{key:"traverseLinks",value:function(e){for(var t=0;t<e.length;t++){var n=e[t];Ao(n)||this.searchResults.has(n.slideid)||(this.noSearchTerm||n.displaytext.toLowerCase().indexOf(this.term)>=0)&&this.searchResults.set(n.slideid,n.displaytext),null!=n.links&&this.traverseLinks(n.links)}}},{key:"getDefaultSate",value:function(e){var t=Io.getCurrentWindow().getCurrentSlide().absoluteId===e||Fo.resolvePath(e).viewed,n=this.menuOptions.flow;if(t)switch(n){case"free":case"restricted":return z.model.getString("acc_visited");case"locked":return"".concat(z.model.getString("acc_visited"),", ").concat(z.model.getString("acc_locked"))}else switch(n){case"free":return"";case"restricted":case"locked":return z.model.getString("acc_locked")}}},{key:"performSearch",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(window.globals.HAS_SLIDE){this.term=t.toLowerCase(),this.items=null,this.noSearchTerm=""===t,this.searchResults=new Map,this.resultsEl.innerHTML="",this.slideTextEnabled=this.slideCheckEl.checked,this.notesEnabled=this.notesCheckEl.checked;var n=z.model.frame.navData.links;null!=n&&this.traverseLinks(n),this.hasSlideBankSlides&&void 0===this.slideBankTextBySlideId&&this.loadSlideBankSearchData();var r=function(t){t.forEach((function(t){(t.text.toLowerCase().indexOf(e.term)>=0||t.title.toLowerCase().indexOf(e.term)>=0)&&e.searchResults.set(t.slideId,t.title)}))},i=!this.noSearchTerm;this.slideTextEnabled&&i&&r(this.slideTextBySlideId),this.hasSlideBankSlides&&i&&r(this.slideBankTextBySlideId),this.notesEnabled&&i&&r(this.notesBySlideId);ea.isIOS;var o=this.wrapListItems?"":"no-wrap",a=Io.getCurrentWindow().getCurrentSlide().absoluteId,c=0;this.searchResults.forEach((function(t,n){0;var r=a===n?"cs-selected":"",i=document.createElement("li"),l=Fo.resolvePath(n).viewed?"cs-viewed":"",s=e.menuOptions.flow,u=e.getDefaultSate(n),f='\n      <div role="menuitem" class="cs-listitem listitem '.concat(r," ").concat(o," ").concat(l," ").concat("free"!==s&&"cs-"+s,'"\n           data-slide-id="').concat(n,'"\n           data-slide-title="').concat(t,'"\n           tabindex="').concat(c,'"\n           role = "treeitem">\n          <span style="position: absolute; opacity: 0;">').concat(t," ").concat(u,"</span>");Jo.isUnified?f+='\n           <span class="linkText" aria-hidden="true">\n             '.concat(t,'\n           </span>\n           <span class="outline-states" aria-hidden="true">\n             <span class="visitedIcon" aria-label="').concat(z.model.getString("acc_visited"),'">\n               ').concat(mo("checkmark")(),'\n             </span>\n             <span class="lockedIcon" aria-label="').concat(z.model.getString("acc_locked"),'">\n               ').concat(mo("lock")(),'\n             </span>\n             <span class="lockedViewedIcon" aria-label="').concat(z.model.getString("acc_visited"),", ").concat(z.model.getString("acc_locked"),'">\n               ').concat(mo("lockedViewed")(),"\n             </span>\n           </span>\n        </div>\n      "):f+="\n             ".concat(t,"\n        </div>"),i.innerHTML=f,e.resultsEl.appendChild(i),c=-1}))}}},{key:"onClear",value:function(){var e=this,t=this.view.parent.children,n=t.outline,r=t.search;this.outlineUl=this.outlineUl||document.querySelector("#outline-content ul"),n.setVisibility(!0),Bo.to(this.el,this.animationDuration,{alpha:0,onComplete:function(){Bo.to(e.outlineUl,e.animationDuration,{alpha:1}),e.view.setVisibility(!1),r.children.bottomDiv.el.style.opacity=1,Ho.isShowFocus&&n.el.focus()}}),this.term="",r.children.searchInput.el.value="",Wo(document.body,"search-results-active"),No.trigger(Mo.search.UPDATE_PANEL)}},{key:"hasListItems",value:function(){return!_.isEmpty(this.getItems())}},{key:"getItems",value:function(){return this.items=this.items||wo(this.el.querySelectorAll(".cs-listitem")),this.items}},{key:"activateItem",value:function(){this["locked"!==this.menuOptions.flow?"onClickLink":"onCarrotClick"]({target:this.currentItem})}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"onAfterVisible",value:function(){this.view.parent.children.outline.setVisibility(!1)}}]),n}(po),ra=na,ia="searchResults",oa=z.def(ia,ra,(function(e){var t=z.model;t.rtl;return{attrs:{id:"searchResults-content",tabindex:-1},z:2,x:0,y:0,w:"100%",h:function(){return this.parent.h},visible:!1,html:'\n      <span class="cs-outline search-content" data-ref="content">\n        <span class="flex-static-auto">\n          <h4 data-ref="title" class="cs-heading search-heading panel-section-heading">\n            '.concat(t.getString("search_results"),'\n          </h4>\n          <button class="btn-unstyled search-filter cs-search-filter" tabindex="0" data-ref="searchFilter">\n            <span>').concat(t.getString("filter"),"</span>\n            ").concat(Mi("filter")(),'\n          </button>\n        </span>\n        <hr class="cs-div cs-diva" />\n        <div class="search-options flex-static-auto" data-ref="searchOptions">\n          <p>').concat(t.getString("search_in"),'</p>\n          <label>\n            <input data-ref="notesCheck" type="checkbox" checked hidden>\n            <span>').concat(t.getString("transcript_chk"),'</span>\n          </label>\n          <label>\n            <input data-ref="slideCheck" type="checkbox" checked hidden>\n            <span>').concat(t.getString("slide_text_chk"),'</span>\n          </label>\n          <hr class="cs-div cs-diva" />\n        </div>\n        <div class="search-results is-scrollable" tabindex="0" data-ref="searchResults">\n          <ul data-ref="results"></ul>\n        </div>\n        <hr class="cs-diva diva full-width-hr" />\n        <hr class="cs-divb divb full-width-hr" />\n        <button data-ref="clear" class="btn search-clear cs-button flex-static-auto" tabindex="0">\n          ').concat(Mi("clear")(),"\n          <span>").concat(t.getString("clear"),"</span>\n        </button>\n      </span>\n    ")}}));function aa(e){return aa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},aa(e)}function ca(e){return function(e){if(Array.isArray(e))return la(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return la(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return la(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function la(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function sa(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==aa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==aa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===aa(o)?o:String(o)),r)}var i,o}function ua(){return ua="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=fa(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},ua.apply(this,arguments)}function fa(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ba(e)););return e}function da(e,t){return da=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},da(e,t)}function ha(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ba(e);if(t){var i=ba(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return pa(this,n)}}function pa(e,t){if(t&&("object"===aa(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ya(e)}function ya(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ba(e)}var va=DS,ma=va.detection,ga=va.pubSub,wa=va._,Sa=va.events,ka=va.keyManager,Ca=va.resolver,Oa=va.windowManager,Ea=va.utils.prefixWithPlayer,xa=va.focusManager.setFocusRectOn,Ta=va.globalEventHelper.addDocumentListener,La=va.dom,Pa=La.parentNodesOf,ja=La.getParentWithClass,_a=La.hasClass,Aa=La.addClass,Da=La.removeClass,Ia=function(e){return Pa(e,(function(e){return"li"===e.nodeName.toLowerCase()}))},Ra=function(e){return Ia(e).slice(1).some((function(e){return _a(e,"item-collapsed")}))},Ba=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&da(e,t)}(o,e);var t,n,r,i=ha(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),wa.bindAll(ya(t),"updateVisitedSlides","onSlideChanged","onSelectFirstSlideLink","addSlideToVisited","visuallyUpdateLinks","collapseLastItem","onClickLink","onCarrotClick","onClickItem","centerOnFocused","setDrawSlides","mouseDown","mouseUp"),t.visitedSlides=new Set,t.menuOptions=z.model.frame.controlOptions.menuOptions,ga.once(Sa.resume.SET_DATA,t.updateVisitedSlides),ga.on(Sa.window.MAIN_CHANGED,t.onSlideChanged),ga.on(Sa.mobile.OUTLINE_SHOWN,t.centerOnFocused),ga.on(Sa.navData.SELECT_FIRST_SLIDE_LINK,t.onSelectFirstSlideLink),t.removeDocListeners=wa.flow(Ta("mousedown",t.mouseDown),Ta("mouseup",t.mouseUp)),null!=DS.presentation.getDrawPromise&&DS.presentation.getDrawPromise().then(t.setDrawSlides),t.isTopTabChild=z.model.topTabs.some((function(e){return"outline"===e.name})),ga.on(Sa.navData.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,n=[{key:"teardown",value:function(){ga.off(Sa.window.MAIN_CHANGED,this.onSlideChanged),ga.off(Sa.mobile.OUTLINE_SHOWN,this.centerOnFocused),this.removeDocListeners()}},{key:"focusSelf",value:function(){this.getItems().find((function(e){return 0===e.parentNode.tabIndex})).parentNode.focus()}},{key:"onFocus",value:function(){var e=this;this.isMouseDown||(null!=this.view.parent.children.search&&ma.theme.isUnified?setTimeout((function(){e.el.contains(document.activeElement)&&ua(ba(o.prototype),"onFocus",e).call(e)}),500):ua(ba(o.prototype),"onFocus",this).call(this))}},{key:"mouseDown",value:function(){this.isMouseDown=!0}},{key:"mouseUp",value:function(){this.isMouseDown=!1}},{key:"onClickItem",value:function(e){var t=this;if(ma.theme.isUnified){var n=ja(e.target,"listitem");_a(e.target,"carrot")&&(t.onCarrotClick(n),1)||n&&_a(n.parentNode,"item-collapsible")&&(t.onCarrotClick(n),!_a(n,"is-promoted-slide"))||"locked"!==t.menuOptions.flow&&t.onClickLink(n)}else("locked"!==this.menuOptions.flow?this.onClickLink:this.onCarrotClick)(e.target)}},{key:"visuallyUpdateLinks",value:function(){var e=this;this.visitedSlides.forEach((function(t){var n=e.view.children[t];null!=n&&(ma.theme.isUnified?e.updateViewedState(n.el):Aa(n.el,"cs-viewed"))})),ma.theme.isUnified&&(this.setComplete("div.is-promoted-slide"),this.setComplete("div.is-scene",1))}},{key:"setComplete",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.el.querySelectorAll(e);Array.from(r).forEach((function(e){Array.from(e.parentNode.querySelectorAll("div")).slice(n).every((function(e){return _a(e,"cs-viewed")}))&&(Aa(e,"cs-complete"),t.updateAriaLabel(e))}))}},{key:"updateVisitedSlides",value:function(e,t){null!=e&&(e.forEach(this.addSlideToVisited),this.visuallyUpdateLinks())}},{key:"getNextItem",value:function(e){var t,n=this,r=this.getItems(),i=r.findIndex((function(t){return n.findIndexCb(t,e)}));do{(i+=1)===r.length&&(i=0),t=r[i]}while(Ra(t));return t}},{key:"getPrevItem",value:function(e){var t,n=this,r=this.getItems(),i=r.findIndex((function(t){return n.findIndexCb(t,e)}));do{-1==(i-=1)&&(i=r.length-1),t=r[i]}while(Ra(t));return t}},{key:"addSlideToVisited",value:function(e){var t=e.absoluteId,n=e.getScene().absoluteId;this.visitedSlides.add(n).add(t)}},{key:"isCurrentLinkParent",value:function(e,t){return e.el===this.view.children[t].el&&e.el.getAttribute("data-has-links")&&!e.el.getAttribute("data-is-scene")}},{key:"onSelectFirstSlideLink",value:function(){var e=this.view.el.querySelector("ul > li > div:not(.is-scene)");if(null!=e){var t=e.getAttribute("data-ref");this.selectLink(e,t)}}},{key:"onSlideChanged",value:function(e){if(this.addSlideToVisited(e),!this.currentItem||ja(this.currentItem,"listitem").getAttribute("data-ref")!=e.absoluteId){var t=this.view.children[e.absoluteId];null!=t&&this.selectLink(t.el,e.absoluteId)}}},{key:"selectLink",value:function(e,t){var n=this;this.onClickLink(e,!0),Pa(e,(function(r){r.classList.contains("item-collapsed")&&(n.isCurrentLinkParent(e,t)||(Da(r,"item-collapsed"),r.setAttribute("aria-expanded",!0)))}),(function(e){return n.el===e}))}},{key:"collapsibleParents",value:function(e){var t=this;return Pa(e,(function(e){return e.classList.contains("item-collapsible")}),(function(e){return t.el===e}))}},{key:"collapseLastItem",value:function(e){var t=this;null!=this.lastExpandedEls&&this.menuOptions.autocollapse&&this.lastExpandedEls.forEach((function(n){n.contains(e)||_a(n,"item-collapsed")||t.toggleScene(n)}))}},{key:"toggleScene",value:function(e){var t,n=_a(e,"item-collapsed");if(t=n?"remove":"add",e.classList[t]("item-collapsed"),e.setAttribute("aria-expanded",n),ma.theme.isUnified){var r=Array.from(e.querySelectorAll("div.listitem")),i=r[0];!function(e,t){if(!n){var r=e.slice(1).filter((function(e){return!_a(e,"is-promoted-slide")&&!_a(e,"is-scene")}));r.some((function(e){return _a(e,"cs-viewed")}))&&Da(t,"cs-unvisited"),r.every((function(e){return _a(e,"cs-viewed")}))&&Aa(t,"cs-complete")}}(r,i),this.updateAriaLabel(i)}ga.trigger(Sa.menuLinksListItem.TOGGLE)}},{key:"updateAriaLabel",value:function(e){var t=[].slice.call(e.querySelector(".outline-states").children),n=e.getAttribute("data-slide-title"),r=e.children[0];t.some((function(e){if("none"!==window.getComputedStyle(e).display){var t=e.getAttribute("aria-label");return r.textContent=n+" "+t,!0}}))||(r.textContent=n)}},{key:"onCarrotClick",value:function(e){if(null!==e){var t=ja(e,"item-collapsible");this.toggleScene(t)}}},{key:"onClickLink",value:function(e,t){var n=this;if(e)if(ma.theme.isUnified){var r=e.getAttribute("data-ref"),i=null!=r;if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(r)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,Da(this.currentItem,"hover")),this.currentItem=e,null!=r&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),this.lastExpandedEls.forEach((function(e){var t=e.firstElementChild;r.includes(t.getAttribute("data-ref"))&&(Aa(t,"cs-viewed"),n.updateAriaLabel(t))}))),_a(e,"listitem")){this.updateViewedState(e);var o=this.el.querySelector(".cs-selected");_a(e,"is-scene")||(null!=o&&(Da(o,"cs-selected"),this.updateAriaLabel(o)),Aa(e,"cs-selected"),window.requestAnimationFrame((function(){return n.updateAriaLabel(e)})))}else this.onCarrotClick(e);if(i&&!t){var a=Oa.getCurrentWindowSlide().absoluteId;r!=a&&(ga.trigger(Sa.request.NEXT_SLIDE,r),ma.theme.isUnified&&ga.trigger(Sa.topEllipsesPanel.HIDE)),ma.deviceView.isMobile&&(ga.trigger(Sa.tab.HIDE),(ma.deviceView.isPhone||ma.theme.isClassic)&&ga.trigger(Sa.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&xa(this.currentItem)}}else this.onClickLinkOld(e,t)}},{key:"onClickLinkOld",value:function(e,t){var n=e.getAttribute("data-ref"),r=null!=n,i=et(n);if("UL"!==e.nodeName)if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(n)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,Da(this.currentItem,"hover")),this.currentItem=e,(i||_a(e.parentNode,"item-collapsible"))&&this.toggleScene(e.parentNode),null!=n&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),Array.from(this.lastExpandedEls).forEach((function(e){var t=e.firstElementChild;n.includes(t.getAttribute("data-ref"))&&Aa(t,"cs-viewed")}))),_a(e,"listitem")){Aa(e,"cs-viewed");var o=this.el.querySelector(".cs-selected");null!=o&&Da(o,"cs-selected"),Aa(e,"cs-selected")}else this.onCarrotClick(e);if(r&&!t){var a=Oa.getCurrentWindowSlide().absoluteId;i||n==a||(ga.trigger(Sa.request.NEXT_SLIDE,n),ma.theme.isUnified&&ga.trigger(Sa.topEllipsesPanel.HIDE)),ma.deviceView.isMobile&&(ga.trigger(Sa.tab.HIDE),(ma.deviceView.isPhone||ma.theme.isClassic)&&ga.trigger(Sa.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&xa(this.currentItem)}else _a(e,"carrot")&&this.onCarrotClick(e)}},{key:"updateViewedState",value:function(e){Aa(e,"cs-viewed");var t=Ia(e).pop();if(!_a(e,"is-scene")&&null!=t){var n=t.querySelector("div.listitem");Da(n,"cs-unvisited")}this.updateAriaLabel(e)}},{key:"isExpanded",value:function(){return!!this.currentItem.dataset.hasLinks&&_a(this.parentElement,"item-collapsed")}},{key:"onKeydown",value:function(e){var t=this.currentItem;if(ka.isActionKey(e.which))this.activateItem(),ka.isSpaceKey(e.which)&&e.preventDefault();else if(ka.isRightKey(e.which)&&t.dataset.hasLinks)_a(this.currentItem.parentNode,"item-collapsed")?this.onCarrotClick(this.currentItem.firstElementChild):this.currentItem=this.getNextItem(this.getItemContent());else if(ka.isDownKey(e.which))this.currentItem=this.getNextItem(this.getItemContent());else if(ka.isUpKey(e.which))this.currentItem=this.getPrevItem(this.getItemContent());else if(ka.isLeftKey(e.which))if(t.dataset.hasLinks&&!_a(t.parentNode,"item-collapsed"))this.onCarrotClick(this.currentItem.firstElementChild);else{var n=ja(this.currentItem,"item-collapsible");null!=n&&(this.currentItem=n.querySelector(".cs-listitem"))}else ka.isHomeKey(e.which)?this.currentItem=this.getFirstItem():ka.isEndKey(e.which)&&(this.currentItem=this.getLastItem());t!==this.currentItem&&(e.preventDefault(),Da(t,"hover"),t.parentNode.tabIndex=-1,this.focusOnCurrent())}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem.parentNode&&(this.currentItem.parentNode.tabIndex=0,Aa(this.currentItem,"hover"),this.currentItem.parentNode.focus()),xa(this.currentItem)}},{key:"hasListItems",value:function(){return!wa.isEmpty(z.model.frame.navData.outline.links)}},{key:"getItems",value:function(){return this.links=this.links||ca(this.el.querySelectorAll(".cs-listitem")),this.links}},{key:"activateItem",value:function(){var e="locked"!==this.menuOptions.flow&&null==this.currentItem.dataset.isScene,t=e?"onClickLink":"onCarrotClick",n=e?this.currentItem:this.currentItem.querySelector(".carrot");this[t](n)}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"getTreeRootEl",value:function(){return this.el.querySelector("ul")}},{key:"getOffsetEl",value:function(){return ma.deviceView.isMobile?this.el.parentNode:this.el}},{key:"getOffsetTop",value:function(e){return ma.deviceView.isMobile?wa.first(Pa(e,(function(e){return"li"===e.nodeName.toLowerCase()}))).offsetTop:ua(ba(o.prototype),"getOffsetTop",this).call(this,e)}},{key:"scrollToCurrent",value:function(){var e=this;ma.deviceView.isMobile&&(this.currentItem=this.getItems().find((function(t){return t.getAttribute("data-ref")===e.currentSlideId}))),ua(ba(o.prototype),"scrollToCurrent",this).call(this)}},{key:"setDrawSlides",value:function(){var e=this;this.view.draws.forEach((function(t){var n=t.link,r=t.links,i=Ca.resolvePath(Ea(n.drawid)),o=function(){var t=n.spliceNum||1,o=i.slides();r.splice.apply(r,[n.index,t].concat(ca(o.map(e.createNewLink)))),n.spliceNum=o.length,e.view.updateHtml(),e.view.initChildRefs()};i.on(Sa.draw.RESET_COMPLETE,o),null!=i.slides()&&o()}))}},{key:"createNewLink",value:function(e){var t={kind:"slidelink",expand:!1,type:"slide"};return t.slideid=e.absoluteId,t.slidetitle=t.displaytext=e.get("title"),t}}],n&&sa(t.prototype,n),r&&sa(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(po),Ma=Ba,Na="rtl",Ha=function(){return z.model.rtl?Na:""},Fa=function(e){z.model.rtl&&!e.contains(Na)?e.add(Na):!z.model.rtl&&e.contains(Na)&&e.remove(Na)},Va=DS.utils,Wa=Va.scaleVal,Ua=Va.pxify,Ka="outline",za=function(e){for(var t="";e;)t=e.index+1+"."+t,e=null!=e.parent&&"slidelink"===e.parent.kind&&e.parent;return t+" "},Ga=z.def(Ka,Ma,(function(){var e=z.model;return{tag:"nav",attrs:{id:"outline-content",class:"is-scrollable cs-outline ".concat(Ha()),tabindex:-1},w:"100%",h:function(){var e=this.parent,t=e.h,n=e.children.search;return t-(null!=n&&n.visible?48:0)},overflow:"auto",draws:[],html:function(){var t=this,n=e.frame.navData.outline.links||[],r=e.frame.controlOptions.menuOptions,i=r.wrapListItems,o=r.autonumber,a=r.tooltips;this.el.depth=0;var c=this.el;return c.innerHTML="",function n(r,c,l){var s=document.createElement("ul");s.depth=c.depth+1,s.tabIndex=-1,null!=l?(s.setAttribute("aria-labelledby",c.firstElementChild.id),s.setAttribute("role","group")):(s.setAttribute("aria-label",e.getString("outline")),s.setAttribute("role","tree")),c.appendChild(s);for(var u=0,f=r.length;u<f;u++){var d=document.createElement("li"),h=r[u];h.parent=l,h.index=u,d.depth=s.depth,d.tabIndex=1===d.depth&&0===u?0:-1,d.setAttribute("role","treeitem"),s.appendChild(d);var p=null!=h.links,y=15*d.depth****,b=y-18,v=h.slideid;null==v&&t.draws.push({link:h,links:r,i:u});var m=et(v),g=e.rtl;d.innerHTML='\n            <div\n              id="outline-'.concat(d.depth,"-").concat(u,'"\n              class="cs-listitem listitem ').concat(i?"":"no-wrap"," ").concat(m?"is-scene":"",'"\n              data-origpad="').concat(y,'"\n              data-ref="').concat(v,'"\n              data-slide-title="').concat(r[u].displaytext,'"\n              ').concat(m?'data-is-scene="true"':"","\n              ").concat(p?'data-has-links="true"':"",'\n              tabIndex="-1"\n              title="').concat(a?h.displaytext:"",'"\n              >\n               ').concat(p?Mi("carrot")(g?"94% - ".concat(b,"px"):"".concat(b,"px")):"","\n               ").concat(o?za(h):""," ").concat(h.displaytext,"\n            </div>\n          "),p&&(d.setAttribute("aria-expanded",h.expand),d.classList.add("item-collapsible"),h.expand||d.classList.add("item-collapsed"),n(h.links,d,h))}}(n,c),c.innerHTML},updateHook:function(){_.toArray(this.el.querySelectorAll(".listitem")).forEach((function(t){var n=parseFloat(t.getAttribute("data-origpad"));t.dataset.isScene&&(1,n+=2),t.style[e.rtl?"paddingRight":"paddingLeft"]=Ua(Wa(n)),t.style.marginBottom=Ua(Wa(1))})),Fa(this.el.classList)}}}));function qa(e){return function(e){if(Array.isArray(e))return Qa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Qa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Qa(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Za="outlineSearch",Ya=z.def(Za,bi,(function(){var e=z.model.frame,t=e.controlOptions.controls.search||DS.presentation.isPreview()&&!window.globals.HAS_SLIDE;return{attrs:{id:"".concat(Za,"-content"),class:"outline-search ".concat(Ha()),tabindex:-1},w:function(){return this.parent.childWidth||this.parent.w},h:function(){return this.parent.childHeight||this.parent.h},model:e,childViews:[].concat(qa(t?[Wi,oa]:[]),[Ga]),updateHook:function(){this.children.forEach((function(e){return e.update()})),Fa(this.el.classList)}}}));function Xa(e){return Xa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xa(e)}function $a(e){return function(e){if(Array.isArray(e))return Ja(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ja(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ja(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ja(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ec(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Xa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Xa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Xa(o)?o:String(o)),r)}var i,o}function tc(e,t){return tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},tc(e,t)}function nc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ic(e);if(t){var i=ic(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return rc(this,n)}}function rc(e,t){if(t&&("object"===Xa(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function ic(e){return ic=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ic(e)}var oc=DS,ac=(oc._,oc.focusManager),cc=oc.detection,lc=oc.pubSub,sc=oc.events,uc=oc.dom,fc=uc.addClass,dc=uc.removeClass,hc=(oc.keyManager.isTabKey,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tc(e,t)}(o,e);var t,n,r,i=nc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).view.isUnified&&(t.getScrollEl=function(){return t.termsEl},t.getOffsetTop=function(e){return e.offsetTop},t.getOffsetHeight=function(e){return e.offsetHeight+e.nextElementSibling.clientHeight}),t.hasDefinition=cc.deviceView.isDesktop&&!t.view.isUnified,lc.on(sc.glossary.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,(n=[{key:"onClickItem",value:function(e){var t=e.target;if(this.hasDefinition&&null!=t&&"p"!==t.nodeName.toLowerCase()){var n=null!=this.activeItem;n&&(this.activeItem.firstElementChild.setAttribute("aria-expanded",!1),this.activeItem.style.backgroundColor="",this.closeItem(this.activeItem)),n&&this.lastSelected==t||(t.setAttribute("aria-expanded",!0),this.openItem(t.parentElement)),this.lastSelected=t}}},{key:"getNextItem",value:function(e){var t,n=this.getItems(),r=this.getItems().indexOf(this.currentItem);do{(r+=1)===n.length&&(r=0),t=n[r]}while("none"===t.parentElement.style.display);return t}},{key:"getPrevItem",value:function(e){var t,n=this.getItems(),r=this.getItems().indexOf(this.currentItem);do{-1==(r-=1)&&(r=n.length-1),t=n[r]}while("none"===t.parentElement.style.display);return t}},{key:"hasListItems",value:function(){return!DS._.isEmpty(this.model.frame.glossaryData)}},{key:"getItems",value:function(){return this.links=this.links||$a(this.el.querySelectorAll(".term")),this.links}},{key:"openItem",value:function(e){fc(e,"cs-selected"),e.nextElementSibling.style.display="block",fc(e.nextElementSibling,"open"),this.activeItem=e}},{key:"closeItem",value:function(e){var t=this;dc(e,"cs-selected"),dc(e.nextElementSibling,"open"),this.activeItem=null,TweenLite.to(e,.2,{opacity:1,onComplete:function(){e.nextElementSibling.style.display="none",ac.setFocusRectOn(t.currentItem)}})}}])&&ec(t.prototype,n),r&&ec(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(po));function pc(e){return pc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pc(e)}function yc(e){return function(e){if(Array.isArray(e))return bc(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return bc(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return bc(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bc(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function vc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==pc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==pc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===pc(o)?o:String(o)),r)}var i,o}function mc(e,t){return mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},mc(e,t)}function gc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Sc(e);if(t){var i=Sc(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return wc(this,n)}}function wc(e,t){if(t&&("object"===pc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Sc(e){return Sc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Sc(e)}var kc=DS,Cc=(kc.detection,kc.pubSub),Oc=kc.events,Ec=kc.keyManager.isSpaceKey,xc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mc(e,t)}(o,e);var t,n,r,i=gc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Cc.on(Oc.resources.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,(n=[{key:"onClickItem",value:function(){}},{key:"hasListItems",value:function(){return(this.model.resourceData.resources||[]).length>0}},{key:"getItems",value:function(){return this.items=this.items||yc(this.el.querySelectorAll(".resource a")),this.items}},{key:"activateItem",value:function(e){Ec(e.keyCode)&&DS.windowOpen.open({url:this.currentItem.dataset.url})}},{key:"getOffsetTop",value:function(e){return this.currentItem.offsetTop}},{key:"getFocusRectTarget",value:function(){return this.currentItem.parentNode}}])&&vc(t.prototype,n),r&&vc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(hc),Tc=DS.utils,Lc=Tc.getPath,Pc=Tc.pxify,jc=Tc.scaleVal,_c="resources",Ac=z.def(_c,xc,(function(){var e=z.model.frame;return{attrs:{id:"".concat(_c,"-content"),class:"cs-resource ".concat(Ha()," is-scrollable"),tabindex:-1,"aria-labelledby":"resources-head"},w:function(){return this.parent.childWidth||this.parent.w},h:function(){return this.parent.childHeight||this.parent.h},overflow:"",model:e,html:function(){return'\n      <h4 class="resources-title cs-heading panel-section-heading html-reset" tabindex="-1" id="resources-head" data-ref="head">\n        '.concat(Lc(e,"resourceData.description"),'\n      </h4>\n      <hr class="cs-separator"></hr>\n      <div class="resources-content panel-content" data-ref="content">\n        <ul>\n          ').concat(Lc(e,"resourceData.resources",[]).reduce((function(e,t,n){var r="javascript:DS.windowOpen.open({ url: '".concat(t.url,"' })");return"".concat(e,'\n              <li class="cs-listitem resource" tabindex="-1"\n                data-url="').concat(t.url,'"\n                id="resource-').concat(n,'"\n                role="presentation">\n                  <a href="').concat(r,'" data-url="').concat(t.url,'" tabindex="').concat(0===n?0:-1,'">\n                    <img src="').concat(t.image,'" role="presentation" tabindex="-1" />\n                    ').concat(t.title,"\n                  </a>\n              </li>")}),""),"\n        </ul>\n      </div>")},updateHook:function(){var e=this.children.head.el.querySelector("font");null!=e&&(this.origSize=this.origSize||parseFloat(e.style.fontSize),e.style.fontSize=Pc(jc(this.origSize,!1))),Fa(this.el.classList)}}})),Dc=DS.utils.getPath,Ic="glossary",Rc=z.def(Ic,hc,(function(){var e=z.model,t=z.getCurrentNameSpaceString(),n=function(e){return fe.getColor(t,".cs-glossary .cs-div".concat(e),"border-color")},r=n("a"),i=n("b"),o=n("c"),a="box-shadow: 0 -1px 0 0 ".concat(o,", 0 -2px 0 0 ").concat(i,", 0 1px 0 0 ").concat(r),c=fe.getColor(t,".cs-glossary .cs-term.cs-selected .cs-choice-list-trigger","background"),l=fe.getColor(t,".cs-glossary .cs-term .cs-choice-list-trigger:focus","background");return{attrs:{id:"".concat(Ic,"-content"),class:"cs-glossary ".concat(Ha()),tabindex:-1},w:function(){return this.parent.childWidth||this.parent.w},h:function(){return this.parent.childHeight||this.parent.h},selectedColor:c,hoverColor:l,model:e,html:function(){return'<h4 class="panel-section-heading glossary-title cs-heading" style="'.concat(a,'" tabindex="-1">\n        ').concat(e.getString("terms"),'\n      </h4>\n\n      <div data-ref="terms" class="glossary-content cs-scrolling-panel scrolling-panel">\n        ').concat(Dc(e,"frame.glossaryData",[]).map((function(e,t){var n=e.content.split(/\r\n/g).filter((function(e){return e.trim().length>0}));return'\n            <dt class="term cs-term"  tabindex="'.concat(0===t?0:-1,'" id="term-').concat(t,'">\n              <button\n                class="cs-choice-list-trigger"\n                aria-controls="term-').concat(t,'"\n                aria-expanded="false"\n                tabindex="-1"\n              >\n                  ').concat(e.title,'\n              </button>\n            </dt>\n            <dd class="cs-editor definition"  tabindex="-1" id="term-').concat(t,'" style="display: none">\n              ').concat(n.map((function(e){return'<p class="term" tabindex="-1">'.concat(e,"</p>")})).join(""),"\n            </dd>\n          ")})).join(""),"\n      </div>")},updateHook:function(){Fa(this.el.classList)}}}));function Bc(e){return Bc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bc(e)}function Mc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Bc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Bc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Bc(o)?o:String(o)),r)}var i,o}function Nc(e,t){return Nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Nc(e,t)}function Hc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Wc(e);if(t){var i=Wc(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Fc(this,n)}}function Fc(e,t){if(t&&("object"===Bc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Vc(e)}function Vc(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wc(e){return Wc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Wc(e)}var Uc=/color:#ffffff/gi,Kc=DS,zc=Kc.detection,Gc=Kc.events,qc=Kc.focusManager,Qc=Kc.pubSub,Zc=(Kc.utils,Kc.slideObjectUtils),Yc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nc(e,t)}(o,e);var t,n,r,i=Hc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Qc.on(Gc.window.MAIN_CHANGED,t.onSlideChanged.bind(Vc(t))),zc.deviceView.isClassicMobile?Qc.on(Gc.mobile.NOTES_SHOWN,t.onHamburgerToggle.bind(Vc(t),!1)):zc.deviceView.isUnifiedMobile&&Qc.on(Gc.hamburger.TOGGLE,t.onHamburgerToggle.bind(Vc(t))),t.forceScrollToTop=!1,t}return t=o,(n=[{key:"onHamburgerToggle",value:function(e){this.forceScrollToTop&&!e&&(this.getScrollElement().scrollTop=0,this.forceScrollToTop=!1)}},{key:"onFocus",value:function(){this.el.focus(),qc.setFocusRectOn(this.el.parentNode)}},{key:"focusSelf",value:function(){this.onFocus()}},{key:"handleTab",value:function(){return this.el.parentElement.focus(),!0}},{key:"onSlideChanged",value:function(e){var t=(this.model.notesData||[]).find((function(t){return e.absoluteId.includes(t.slideId)}));null!=this.titleEl&&(this.titleEl.innerHTML=e.get("title"));var n=null==t?"":t.content;zc.deviceView.isClassicMobile&&(n=n.replace(Uc,"color:#515557")),this.contentEl.innerHTML=n,Qc.trigger(Gc.transcript.CHANGED),Zc.activeMobileMenuItem(n,"no-transcript"),this.scrollToTop()}},{key:"scrollToTop",value:function(){zc.deviceView.isDesktop?this.view.el.scrollTop=0:zc.theme.isClassic?0===this.getScrollElement().scrollTop?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0:z.getNamespace(this.view.nameSpace).sidebar.collapsed?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0}},{key:"getScrollElement",value:function(){return zc.deviceView.isClassicMobile?this.view.parent.el:this.view.el}},{key:"getViewBox",value:function(){return this.view.parent.getBox()}},{key:"teardown",value:function(){Qc.off(Gc.window.MAIN_CHANGED,this.onSlideChanged.bind(this)),zc.deviceView.isClassicMobile?Qc.off(Gc.mobile.NOTES_SHOWN,this.onHamburgerToggle.bind(this,!1)):zc.deviceView.isUnifiedMobile&&Qc.off(Gc.hamburger.TOGGLE,this.onHamburgerToggle.bind(this))}}])&&Mc(t.prototype,n),r&&Mc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Xc="transcript",$c=DS.utils,Jc=$c.pxify,el=$c.scaleVal,tl="data-origsize",nl="data-origline",rl={outline:Ya,resources:Ac,glossary:Rc,transcript:z.def(Xc,Yc,(function(){var e=z.model,t=e.rtl?"rtl":"";return{attrs:{id:"transcript-content",class:"cs-transcript ".concat(t," is-scrollable"),tabindex:0},w:function(){return this.parent.childWidth||this.parent.w},h:function(){return(this.parent.childHeight||this.parent.h)-nt},overflow:"",html:"\n      <h4 data-ref=\"title\" class='note-title cs-heading panel-section-heading'></h4>\n      <hr class='cs-div'></hr>\n      <div data-ref=\"content\" class='note-content'></div>\n    ",updateHook:function(){if("no scale"!==window.globals.scale){var e=this.children.content.el.querySelectorAll("span[style]");_.forEach(e,(function(e){var t=parseFloat(e.getAttribute(tl)),n=parseFloat(e.getAttribute(nl));isNaN(t)&&(t=parseFloat(e.style.fontSize),e.setAttribute(tl,t),n=parseFloat(e.style.lineHeight),e.setAttribute(nl,n)),e.style.fontSize=Jc(el(t,!1)),e.style.lineHeight=Jc(el(n,!1))}))}},model:e.frame}}))},il=DS.utils,ol=il.getPath,al=(il.scaleVal,DS.constants.refs.FRAME),cl="sidebarPanels",ll=(z.def(cl,(function(e){var t=z.getNamespace(e),n=t.sidebar,r=t.tabs,i=t.logo;return{attrs:{id:cl},x:9,z:1,y:function(){return r.bottom()},w:function(){return n.w-rt},h:function(){var e=i?i.h+nt:0;return n.h-rt-r.h-e},childDef:function(){ll(z.model,e)},updateHook:function(){this.children.forEach((function(e){return e.update()}))}}})),function(e,t){ol(e,"sidebarTabs",[]).forEach((function(e,n){!function(e,t,n,r){var i=t.name,o=i+"Panel",a=0===n,c=z.def(o,ai,{attrs:{id:o,class:"cs-menu cs-panel is-scrollable panel","aria-labelledby":i+"-tab",role:"tabpanel",tabindex:-1},visibility:"no-reflow",style:{display:a},x:0,y:0,overflow:function(){return null},w:"100%",h:"100%",html:"",updateHook:function(){this.children.forEach((function(e){return e.update()}))}});c.init(),z.getNamespace(al).sidebarPanels.append(c);var l=rl[i];null!=l&&(l.nameSpace=r,l.init(),c.append(l))}(0,e,n,t)}))}),sl="topTabs";function ul(e){return ul="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ul(e)}function fl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ul(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ul(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ul(o)?o:String(o)),r)}var i,o}function dl(e,t){return dl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},dl(e,t)}function hl(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=yl(e);if(t){var i=yl(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return pl(this,n)}}function pl(e,t){if(t&&("object"===ul(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function yl(e){return yl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yl(e)}z.def(sl,(function(){return{attrs:{id:sl,role:"navigation"},overflow:"visible",w:"100%",h:"100%"}}));var bl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dl(e,t)}(o,e);var t,n,r,i=hl(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickLink),t}return t=o,(n=[{key:"onClickLink",value:function(){var e=this,t=this.model.properties.data;DS.pubSub.trigger(DS.events.customlink.EVENT,t),this.view.parent.children.forEach((function(t){t.viewLogic!==e&&t.viewLogic.showing&&t.viewLogic.hidePanel()}))}},{key:"isLive",value:function(){return!0}}])&&fl(t.prototype,n),r&&fl(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt);function vl(e){return vl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vl(e)}function ml(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==vl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==vl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===vl(o)?o:String(o)),r)}var i,o}function gl(){return gl="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=wl(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},gl.apply(this,arguments)}function wl(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=El(e)););return e}function Sl(e,t){return Sl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Sl(e,t)}function kl(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=El(e);if(t){var i=El(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Cl(this,n)}}function Cl(e,t){if(t&&("object"===vl(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ol(e)}function Ol(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function El(e){return El=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},El(e)}var xl=DS.dom.isWithin,Tl=DS.globalEventHelper,Ll=Tl.addDocumentListener,Pl=Tl.removeDocumentListener,jl=DS,_l=jl.pubSub,Al=jl.events,Dl=jl._,Il=jl.playerGlobals,Rl=jl.dom,Bl=Rl.addClass,Ml=Rl.removeClass,Nl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Sl(e,t)}(o,e);var t,n,r,i=kl(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Dl.bindAll(Ol(t),"hidePanel","onCheckShouldHide"),t.onClick(t.onClickLink),t.showing=!1,_l.on(Al.sidebar.ACTIVE_TAB_SET,t.onActiveTabSet.bind(Ol(t))),t}return t=o,(n=[{key:"onActiveTabSet",value:function(e){this.view.parent.children.find((function(t){return t.nameKey.includes(e)}))&&this.hidePanel(),this.view.nameKey.includes(e)&&this.showPanel()}},{key:"onClickLink",value:function(e){this.showing?this.hidePanel():this.showPanel()}},{key:"showPanel",value:function(){this.el.style.border="1px solid ".concat(this.view.selectedBorderColor),this.el.setAttribute("aria-expanded",!0),Bl(this.el,"cs-selected"),Bl(this.el,"active"),this.view.panel.el.style.display="block",this.view.panel.viewLogic.focusChild(),Ll("mouseup",this.onCheckShouldHide),_l.on(Al.controlLayout.CHANGED,this.hidePanel),this.showing=!0}},{key:"hidePanel",value:function(){this.el.style.border="1px solid rgba(0, 0, 0, 0)",this.el.setAttribute("aria-expanded",!1),Ml(this.el,"cs-selected"),Ml(this.el,"active"),this.view.panel.el.style.display="none",Pl("mouseup",this.onCheckShouldHide),_l.off(Al.controlLayout.CHANGED,this.hidePanel),this.showing=!1}},{key:"onBlur",value:function(e){Il.presentation.isPreview()&&!window.globals.HAS_SLIDE||this.onCheckShouldHide({target:e.relatedTarget}),gl(El(o.prototype),"onBlur",this).call(this,e)}},{key:"onCheckShouldHide",value:function(e){null!=e.target&&(xl(e.target,"panel")||this.el.contains(e.target))||this.hidePanel()}},{key:"isLive",value:function(){var e=z.model.currControlLayout[this.model.name];return Dl.isObject(e)?e.enabled:e}},{key:"teardown",value:function(){_l.off(Al.controlLayout.CHANGED,this.hidePanel),_l.off(Al.sidebar.ACTIVE_TAB_SET)}}])&&ml(t.prototype,n),r&&ml(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Hl=DS.utils.getPath,Fl=DS.constants.refs.FRAME,Vl="separator-style",Wl=function(){return"\n    .top-tab:before {\n      box-shadow: 0px 0px 0px 1px ".concat(fe.getColor(DS.constants.refs.FRAME,".cs-separator .cs-diva","background-color",".cs-base"),",\n        1px 0px 0px 1px ").concat(fe.getColor(DS.constants.refs.FRAME,".cs-separator .cs-divb","background-color",".cs-base"),";\n    }\n  ")},Ul=!1,Kl=function(e){var t,n=e.id,r=z.def(n,(function(r){z.getNamespace(r).title,z.model;return t=n,{attrs:{id:n,tabindex:-1},overflow:"visible",w:"50%",x:e.x,y:0,h:"100%",z:5,childDef:function(){!function(){if(!Ul){Ul=!0;var e=document.createElement("style");e.id=Vl,document.body.appendChild(e),e.innerHTML=Wl()}}(),o(z.model,r)},updateHook:e.updateHook}})),i=function(n,i,o,a){var c,l,s,u,f="customlink"===o.name;f?(l=bl,c=o.properties.title,s="link".concat(e.dir).concat(a),u="custom-link"):(l=Nl,c=i.getString(o.name),s=o.name+"Link",u="panel-link");var d=fe.getColor(n,".cs-topmenu-item.active .cs-tab","border-top-color",".cs-base"),h={id:s,class:"topmenu-item cs-topmenu-item cs-tabs top-tab ".concat(u),tabindex:0};f||(h["aria-controls"]="".concat(s,"Panel"),h["aria-expanded"]=!1);var p=z.def(s,l,{selectedBorderColor:d,attrs:h,model:Object.assign(o,{idx:a}),isTopLink:!0,calcTextSize:!0,noUpdate:!0,contentStyle:{top:"8%"},tag:"button",html:function(){return'<span class="cs-tab">'.concat(c,"</span>")},wPad:14,overflow:"visible",y:function(){return this.parent.h-this.h-9},w:"fit-to-text-w",h:"fit-to-text-h",z:2,border:"1px solid rgba(0, 0, 0, 0)",minH:23,updateHook:function(){if(_.isFunction(this.viewLogic.scaleProps)&&this.viewLogic.scaleProps(),DS.playerGlobals.presentation.isPreview()&&!window.globals.HAS_SLIDE){var e=document.querySelector("style#".concat(Vl));null!=e&&(e.innerHTML=Wl())}}});p.init(),z.getNamespace(Fl)[e.id].append(p),f||function(n,i,o,a){var c=a.name+"Panel",l="linksRight"===e.id,s=z.getNamespace(n),u=s.topBar,f=(s.frame,s.sidebar,s.slide,s.wrapper,s.linksLeft,fe.getColor(n,".cs-topmenu-item.active .cs-panel","border-top-color",".cs-base")),d=z.def(c,ai,{attrs:{id:c,class:"cs-topmenu-item topmenu-item active cs-menu cs-panel panel topmenu-panel-align-".concat(l?"right":"left"),tabIndex:-1},style:{borderColor:f},w:302,h:438,z:1,visible:!1,lnk:i,childWidth:300,childHeight:436,tabsId:t,overflow:"visible",x:function(){var e=i.x;return e+r.x>u.w/2&&(e=e+i.w-this.w),e},y:function(){return i.y+i.h},updateHook:function(){this.updateChildren(!0)}});d.init(),z.getNamespace(Fl)[e.id].append(d),i.panel=d;var h=rl[a.name];null!=h&&(h.init(),d.append(h))}(n,p,0,o)},o=function(t,n){Hl(t,e.linkListName,[]).forEach((function(e,r){i(n,t,e,r)}))}};Kl({id:"linksLeft",dir:"Left",linkListName:"topTabsLeft",x:0,updateHook:function(){this.flowChildren({hook:function(e){null!=e.panel&&e.panel.update()},pad:-3,startPos:11})}});Kl({id:"linksRight",dir:"Right",linkListName:"topTabsRight",x:function(){return z.getNamespace(DS.constants.refs.FRAME).linksLeft.right()},updateHook:function(){this.flowChildren({toLeft:!0,startPos:null!=this.nameSpace?z.getNamespace(this.nameSpace).linksRight.w-2:0,pad:-3,reverse:!0,hook:function(e){null!=e.panel&&e.panel.update()}})}});var zl="bottomBar";z.def(zl,(function(e){var t=z.getNamespace(e),n=t.frame,r=t.topBar,i=z.model.rtl;return{tag:"section",attrs:{id:zl},x:function(){return r.x-1},y:function(){return n.h-54},w:function(){return r.w},h:it,overflow:"visible",updateHook:function(){if(this.hasAllChildren()){this.updateChildren(),this.el.style.pointerEvents=z.model.bottomBarOpts.bottomBarEnabled?"all":"none";var e=this.children.playbackControls.children.seek;if(e.width=0,this.children.playbackControls.visible&&e.visible){var t=this.calcChildrensWidth()+(this.children.length+1)*nt;e.width=r.w-t}this.flowChildren({alignChild:!0,bounds:{t:0,b:this.h,l:0,r:this.w},rtl:i,pad:nt})}},childVisibilityChangedHook:function(){this.update()}}}));var Gl="navControls";z.def(Gl,(function(){var e=z.model.rtl;return{tag:"nav",attrs:{id:Gl,"aria-label":"slide navigation"},w:function(){return this.width||0},overflow:"visible",h:it,updateHook:function(){this.flowChildren({rtl:e,startPos:-10,pad:nt,fullUpdate:!0,sizeToChildren:!0})},parentAlign:e?"l":"r"}}));var ql="miscControls";function Ql(e){return Ql="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ql(e)}function Zl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Ql(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ql(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Ql(o)?o:String(o)),r)}var i,o}z.def(ql,(function(){var e=z.model.rtl;return{attrs:{id:ql,"aria-label":"misc controls"},w:function(){return this.width||0},h:it,overflow:"visible",updateHook:function(){this.flowChildren({fullUpdate:!0,pad:nt,startPos:-10,sizeToChildren:!0,rtl:e})},childVisibilityChangedHook:function(){this.update()},parentAlign:"l"}}));var Yl=DS,Xl=Yl._,$l=Yl.pubSub,Jl=Yl.events,es=Yl.dom.toggleClasses,ts=(Yl.shortcutManager,function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.view=t,Xl.bindAll(this,"onSlideChanged","onTimelineChanged","onPlaybackChanged"),$l.on(Jl.slide.STARTED,this.onSlideChanged)}var t,n,r;return t=e,(n=[{key:"teardown",value:function(){$l.off(Jl.slide.STARTED,this.onSlideChanged)}},{key:"onTimelineChanged",value:function(e,t){e!==this.currTimeline&&($l.trigger(Jl.playbackControls.TIMELINE_CHANGED,e,t),null!=this.currTimeline&&(this.currTimeline.off(Jl.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.off(Jl.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.off(Jl.timeline.ENDED,this.onPlaybackChanged)),this.currTimeline=e,this.currTimeline.on(Jl.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.on(Jl.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.on(Jl.timeline.ENDED,this.onPlaybackChanged),this.onPlaybackChanged())}},{key:"onPlaybackChanged",value:function(){var e=this.view.children,t=e.reset,n=e.playPause,r=null!=this.currTimeline&&"playing"===this.currTimeline.playbackState();es(document.body,"timeline-playing","timeline-paused",r),es(document.body,"has-reset","no-reset",this.view.visible&&null!=t&&t.visible),null!=n&&n.viewLogic.onPlaybackChanged()}},{key:"onSlideChanged",value:function(e,t,n){this.view.el.tabIndex=0,this.view.el.tabIndex=-1,this.view.nameSpace===n&&(null!=this.currSlide&&this.currSlide.off(Jl.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.currSlide=t,this.currSlide.on(Jl.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.onTimelineChanged(t.currentTimeline(),t))}}])&&Zl(t.prototype,n),r&&Zl(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()),ns="playbackControls";function rs(e){return rs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rs(e)}function is(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==rs(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==rs(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===rs(o)?o:String(o)),r)}var i,o}function os(){return os="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=as(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},os.apply(this,arguments)}function as(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=fs(e)););return e}function cs(e,t){return cs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},cs(e,t)}function ls(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=fs(e);if(t){var i=fs(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return ss(this,n)}}function ss(e,t){if(t&&("object"===rs(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return us(e)}function us(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fs(e){return fs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},fs(e)}z.def(ns,ts,(function(e){return{attrs:{id:ns,"aria-label":"playback controls",class:"cs-seekcontrol",tabindex:-1},visibility:"no-reflow",x:0,w:function(){return this.visible&&this.width||0},h:it,noUpdate:!0,updateHook:function(){this.el.style.background="none",this.flowChildren({fullUpdate:!0,sizeToChildren:!0})},parentAlign:"l"}}));var ds=DS,hs=ds.keyManager,ps=ds.dom,ys=ps.addClass,bs=ps.removeClass,vs=ds.utils,ms=(vs.scaleVal,vs.pxify,ds.globalEventHelper),gs=ms.addBodyListener,ws=ms.removeBodyListener,Ss=ds.appState,ks=10,Cs=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cs(e,t)}(o,e);var t,n,r,i=ls(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).isOpen=!1,t.view.volumeStrength=DS.constants.DEFAULT_VOLUME*ks,DS._.bindAll(us(t),"onShow","onHide","onVolumeChanged","onInputChanged","toggleMute","onKeydown","onScroll"),t.volumeRangeEl.addEventListener("input",t.onInputChanged),t.el.addEventListener("mouseover",t.onShow),t.el.addEventListener("scroll",t.onScroll),t.buttonEl.addEventListener("click",t.toggleMute),DS.pubSub.on(DS.events.volume.CHANGED,t.onVolumeChanged),Ss.setVolume(t.view.volumeStrength/ks),t}return t=o,(n=[{key:"onVolumeChanged",value:function(e){e=DS.utils.clamp(0,ks,e*ks),this.view.volumeStrength=e,this.view.updateButton();var t=0===e;this.buttonEl.setAttribute("aria-pressed",t),this.volumeRangeEl.valueAsNumber!==e&&(this.volumeRangeEl.value=e)}},{key:"onHide",value:function(e){this.isOpen=!1,bs(this.el,"open"),this.view.el.removeEventListener("mouseleave",this.onHide),ws("keydown",this.onKeydown),this.view.update()}},{key:"onShow",value:function(){this.isOpen||(this.isOpen=!0,ys(this.el,"open"),this.view.el.addEventListener("mouseleave",this.onHide),gs("keydown",this.onKeydown),this.view.update())}},{key:"onInputChanged",value:function(e){var t=e.target.valueAsNumber;Ss.setVolume(t/ks)}},{key:"toggleMute",value:function(e){DS.appState.onToggleVolume()}},{key:"onFocus",value:function(e){this.onShow(),DS.focusManager.setFocusRectOn(e.target)}},{key:"onBlur",value:function(e){null!=e&&this.isOpen&&!this.el.contains(e.relatedTarget)&&(this.onHide(),os(fs(o.prototype),"onBlur",this).call(this))}},{key:"onKeydown",value:function(e){var t=e.which,n=this.volumeRangeEl.valueAsNumber,r=function(t){Ss.setVolume(t/ks),e.preventDefault()};document.activeElement!==this.volumeRangeEl&&(hs.isDownishKey(t)||hs.isPageDownKey(t)?r(n-1):hs.isUpishKey(t)||hs.isPageUpKey(t)?r(n+1):hs.isEndKey(t)?r(ks):hs.isHomeKey(t)&&(this.previousVolume=n,r(0)))}},{key:"onScroll",value:function(e){e.target.scrollTop=0}},{key:"teardown",value:function(){DS.pubSub.off(DS.events.volume.CHANGED,this.onVolumeChanged)}}])&&is(t.prototype,n),r&&is(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Os="volume",Es=DS,xs=(Es.scaler,Es.utils),Ts=xs.pxify,Ls=xs.scaleVal;function Ps(e){return Ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ps(e)}function js(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Hs(r.key),r)}}function _s(){return _s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=As(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},_s.apply(this,arguments)}function As(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Ms(e)););return e}function Ds(e,t){return Ds=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ds(e,t)}function Is(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ms(e);if(t){var i=Ms(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Rs(this,n)}}function Rs(e,t){if(t&&("object"===Ps(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Bs(e)}function Bs(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ms(e){return Ms=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ms(e)}function Ns(e,t,n){return(t=Hs(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Hs(e){var t=function(e,t){if("object"!==Ps(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ps(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ps(t)?t:String(t)}z.def(Os,Cs,(function(e){var t=z.model;z.model.rtl;return{noUpdate:!0,z:1e3,attrs:{id:Os,class:"btn cs-volume cs-button volume-panel"},style:{position:"absolute"},w:31,h:function(){return this.viewLogic.isOpen?106:30},y:function(){return this.el.style.top=Ts(Ls(this.viewLogic.isOpen?-68:8,!1)),0},html:function(){return'<button\n      data-ref="button"\n      class="cs-button btn"\n      aria-label="'.concat(t.getString("acc_volume"),'"\n      aria-pressed="false"\n      tabIndex="0"\n      >\n      ').concat(Mi("volume")(this.volumeStrength,Ls(15),Ls(20)),'\n      </button>\n      <div data-ref="sliderBar" class="slider-bar">\n        <input\n          type="range"\n          class="cs-volume"\n          data-ref="volumeRange"\n          aria-label="').concat(t.getString("acc_volume"),'"\n          min="0"\n          max="10"\n          step="1"\n          value="').concat(this.volumeStrength,'"\n          tabIndex="0"\n          aria-orientation="vertical" />\n      </div>')},updateHook:function(){var e=this.el.querySelector("svg");null!=e&&(e.height.baseVal.value=Ls(20),e.width.baseVal.value=Ls(15))},methods:{updateButton:function(){this.children.button.el.innerHTML=Mi("volume")(this.volumeStrength,Ls(15),Ls(20)),this.el.style.background=this.accessibleTextOn?"#777":""}}}}));var Fs=DS,Vs=Fs.dom,Ws=Vs.addClass,Us=Vs.removeClass,Ks=Fs.appState,zs=Fs.scaler,Gs=Fs.shortcutManager,qs=Fs.events,Qs=Fs.focusManager,Zs=Fs.pubSub,Ys=Fs.utils.fullScreen,Xs=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ds(e,t)}(o,e);var t,n,r,i=Is(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),Ns(Bs(t=i.call(this,e)),"toggleBackgroundAudio",(function(){DS.courseAudio.toggleEnabled(),t.updateToggle(t.backgroundAudioSwitchEl,DS.courseAudio.enabled())})),Ns(Bs(t),"onCaptionsEnabled",(function(){t.view.updateHook()})),DS._.bindAll(Bs(t),"onAccessibleTextChanged","onZoomModeChanged","onKeyboardShortcutsChanged"),t.onClickEl(t.settingsBtnEl,t.togglePanel),null!=t.shortcutsSwitchEl&&(t.onClickEl(t.shortcutsSwitchEl,t.toggleShortcuts),DS.pubSub.on(qs.player.ENABLE_KEYBOARD_SHORTCUTS,t.onKeyboardShortcutsChanged)),null!=t.captionsButtonEl&&(t.onClickEl(t.captionsButtonEl,t.toggleCaptions),Zs.on(qs.captions.ENABLED,t.onCaptionsEnabled)),null!=t.resetButtonEl&&t.onClickEl(t.resetButtonEl,t.resetTimeline),null!=t.acctextSwitchEl&&(t.onClickEl(t.acctextSwitchEl,t.toggleAccessibleText),Ks.on(qs.player.ACCESSIBLE_TEXT_CHANGED,t.onAccessibleTextChanged)),null!=t.zoomSwitchEl&&(t.onClickEl(t.zoomSwitchEl,t.toggleZoomMode),Zs.on(qs.window.ZOOM_MODE_CHANGED,t.onZoomModeChanged)),t.handleFullScreenChange=t.handleFullScreenChange.bind(Bs(t)),null!=t.fullScreenToggleButtonEl&&(t.onClickEl(t.fullScreenToggleButtonEl,t.toggleFullScreen),t.removeFullScreenListener=Ys.addChangeListener(t.handleFullScreenChange)),null!=t.backgroundAudioSwitchEl&&t.onClickEl(t.backgroundAudioSwitchEl,t.toggleBackgroundAudio),t}return t=o,(n=[{key:"toggleFullScreen",value:function(){Ks.toggleFullScreen()}},{key:"handleFullScreenChange",value:function(){Ys.getEl()===Ks.getPresoEl()?Ws(this.el,"full-screen"):Us(this.el,"full-screen")}},{key:"togglePanel",value:function(){this.isOpen?this.hidePanel():this.showPanel()}},{key:"showPanel",value:function(){this.isOpen=!0,Ws(this.el,"open"),this.view.updatePanelPosition(),this.settingsBtnEl.setAttribute("aria-expanded",!0),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!0)}},{key:"hidePanel",value:function(){this.isOpen=!1,Us(this.el,"open"),this.settingsBtnEl.setAttribute("aria-expanded",!1),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!1)}},{key:"onBlur",value:function(e){null!=e&&this.isOpen&&!this.el.contains(e.relatedTarget)&&(this.hidePanel(),_s(Ms(o.prototype),"onBlur",this).call(this))}},{key:"updateToggle",value:function(e,t){t?(Ws(e,"toggle-on"),Us(e,"toggle-off")):(Ws(e,"toggle-off"),Us(e,"toggle-on")),e.querySelector("button").setAttribute("aria-checked",t)}},{key:"onAccessibleTextChanged",value:function(e){this.accTextOn=e,this.updateToggle(this.acctextSwitchEl,this.accTextOn)}},{key:"onKeyboardShortcutsChanged",value:function(e){this.updateToggle(this.shortcutsSwitchEl,e)}},{key:"toggleAccessibleText",value:function(){this.accTextOn=!this.accTextOn,this.updateToggle(this.acctextSwitchEl,this.accTextOn),Ks.onToggleAccessibleText(this.accTextOn)}},{key:"onZoomModeChanged",value:function(){this.updateToggle(this.zoomSwitchEl,zs.zoomMode)}},{key:"toggleZoomMode",value:function(){zs.enableZoomMode(!zs.zoomMode),this.updateToggle(this.zoomSwitchEl,zs.zoomMode),Qs.setFocusRectOn(this.zoomSwitchEl.querySelector("button"))}},{key:"toggleShortcuts",value:function(){Gs.enableShortcuts(!Gs.enabled),this.updateToggle(this.shortcutsSwitchEl,Gs.enabled)}},{key:"toggleCaptions",value:function(){z.getNamespace("_frame").captions.viewLogic.toggleCaptions(),this.view.updateHook()}},{key:"resetTimeline",value:function(){z.getNamespace("_frame").reset.viewLogic.resetTimeline()}},{key:"teardown",value:function(){Ks.off(qs.player.ACCESSIBLE_TEXT_CHANGED,this.onAccessibleTextChanged),Zs.off(qs.window.ZOOM_MODE_CHANGED,this.onZoomModeChanged),Zs.off(qs.captions.ENABLED,this.onCaptionsEnabled),null!=this.removeFullScreenListener&&this.removeFullScreenListener()}},{key:"onFocus",value:function(e){var t=e.target;if(this.el.contains(t))return Qs.setFocusRectOn(t),this.isFocused=!1,!1}}])&&js(t.prototype,n),r&&js(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt);function $s(e){return function(e){if(Array.isArray(e))return Js(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Js(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Js(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Js(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var eu=DS,tu=eu.utils,nu=tu.pxify,ru=tu.scaleVal,iu=eu.scaler,ou="settings";function au(e){return au="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},au(e)}function cu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==au(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==au(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===au(o)?o:String(o)),r)}var i,o}function lu(e,t){return lu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},lu(e,t)}function su(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=du(e);if(t){var i=du(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return uu(this,n)}}function uu(e,t){if(t&&("object"===au(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return fu(e)}function fu(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function du(e){return du=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},du(e)}z.def(ou,Xs,(function(e){var t=z.model,n=(z.model.rtl,fe.getColor(e,".cs-button","color")),r=fe.getColor(e,".cs-checkbox .cs-box.checked.after","color"),i=fe.getColor(e,".cs-checkbox .cs-box","border-color"),o=fe.getColor(e,".cs-topmenu-item.active .cs-panel","border-top-color",".cs-base"),a=fe.getColor(e,".cs-menu","background-color"),c=z.getNamespace(e).bottomBar;return{noUpdate:!0,attrs:{id:ou,class:"btn cs-button cs-settings"},overflow:"visible",w:30,parentAlign:"l",h:function(){return 30},x:function(){return this.left||0},y:"vertical-center",methods:{getControlConfigs:function(){return[].concat($s(DS.frameModel.hasModernText?[{name:"acctext",labelId:"accessible_text",isOn:function(){return DS.appState.accessibleTextOn()}}]:[]),[{name:"shortcuts",labelId:"keyboardshortcuts_lower",isOn:function(){return DS.shortcutManager.enabled}}])},getToggleControls:function(){return this.getControlConfigs().map((function(e){var o=e.name,c=e.labelId,l=(0,e.isOn)();return'\n            <div class="switch '.concat(l?"toggle-on":"toggle-off",'" data-ref="').concat(o,'Switch">\n              <label class="switch-label" id="').concat(o,'-label">\n                ').concat(t.getString(c),'\n              </label>\n              <button class="switch-toggle" id="').concat(o,'-switch" tabindex="0" role="switch" aria-checked="').concat(l,'" aria-labelledby="').concat(o,'-label">\n                ').concat(Mi("track")(iu.getScale(),r,i,a,n,o),"\n              </button>\n            </div>\n          ")})).join("")},updatePanelPosition:function(){if(null!=c){var e=this.children.settingsPanel.el;e.style.left=0;var t=e.getBoundingClientRect(),n=c.el.getBoundingClientRect(),r=0;t.right>n.right&&(r=n.right-(t.right+ru(nt))),t.left<n.left&&(r=n.left-t.left+ru(nt)),e.style.left=nu(r)}},scaleControls:function(){var e=this.children.settingsPanel.el,t=ru(18),n=ru(8);e.style.bottom=nu(ru(39)),e.style.padding="".concat(n,"px ").concat(t,"px ").concat(n,"px ").concat(t,"px");var r=this.children.downArrow.el,i=r.querySelector("svg");r.style.bottom=nu(ru(41)),r.style.left=nu(ru(2)),i.width.baseVal.value=ru(22),i.height.baseVal.value=ru(11);var o=e.querySelectorAll(".switch");_.forEach(o,(function(e){e.style.margin="".concat(ru(15),"px 0 ").concat(ru(15),"px 0");var t=e.querySelector(".switch-toggle > svg");t.width.baseVal.value=ru(24),t.height.baseVal.value=ru(16)}));var a=this.el.querySelector("svg");a.width.baseVal.value=ru(16),a.height.baseVal.value=ru(18)}},updateHook:function(){this.scaleControls(),this.updatePanelPosition()},html:function(){return'\n        <button data-ref="settingsBtn" aria-expanded="false" class="cs-button" aria-label="'.concat(t.getString("acc_settings"),'" tabIndex="0">\n          ').concat(Mi("settings")(iu.getScale()),'\n        </button>\n        <div data-ref="settingsPanel" class="settings-panel cs-menu" style="background:').concat(a,"; border: 1px solid ").concat(o,';" tabindex="-1">\n          <div class="settings-content">\n            ').concat(this.getToggleControls(),'\n          </div>\n        </div>\n        <div data-ref="downArrow" class="panel-down-arrow">\n          ').concat(Mi("downArrow")(a),"\n        </div>\n      ")}}}));var hu=DS,pu=hu.pubSub,yu=hu.captionsManager,bu=hu.events.captions,vu=bu.SHOW_BUTTON,mu=bu.HIDE_BUTTON,gu=bu.ENABLED,wu=bu.ENABLE,Su=hu.detection.theme.isUnified,ku=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lu(e,t)}(o,e);var t,n,r,i=su(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickCaptions),t.toggle=!1,pu.on(vu,(function(e){return t.onVisibilityChanged(!0)})),pu.on(mu,(function(e){return t.onVisibilityChanged(!1)})),pu.on(gu,t.onCaptionsEnabled.bind(fu(t))),t}return t=o,(n=[{key:"onVisibilityChanged",value:function(e){this.view.setVisibility(e,!0),this.view.childVisibilityChanged(),this.toggle=yu.isCaptionEnabled(),this.updateBtn()}},{key:"onCaptionsEnabled",value:function(e){this.toggle=e,this.updateBtn()}},{key:"updateBtn",value:function(){var e=this.toggle?"add":"remove";this.view.el.classList[e]("cs-tabs","cs-selected"),this.view.el.setAttribute("aria-pressed",this.toggle),Su&&this.view.updateHtml()}},{key:"onClickCaptions",value:function(e){this.toggleCaptions()}},{key:"toggleCaptions",value:function(){this.toggle=!this.toggle,pu.trigger(wu,this.toggle),this.updateBtn()}},{key:"teardown",value:function(){pu.off(vu),pu.off(mu),pu.off(gu)}}])&&cu(t.prototype,n),r&&cu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Cu="captions",Ou=DS.utils;Ou.pxify,Ou.scaleVal;function Eu(e){return Eu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Eu(e)}function xu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Du(r.key),r)}}function Tu(e,t){return Tu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Tu(e,t)}function Lu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=_u(e);if(t){var i=_u(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Pu(this,n)}}function Pu(e,t){if(t&&("object"===Eu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ju(e)}function ju(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _u(e){return _u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_u(e)}function Au(e,t,n){return(t=Du(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Du(e){var t=function(e,t){if("object"!==Eu(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Eu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Eu(t)?t:String(t)}z.def(Cu,ku,(function(){var e=z.model;return{tag:"button",attrs:{id:Cu,class:"cs-button btn content-center","aria-label":e.getString("acc_closed_captions"),"aria-pressed":!1,tabindex:0},minW:40,minH:30,y:"vertical-center",visible:!1,html:function(){return Mi("captions")()}}}));var Iu=DS,Ru=Iu._,Bu=Iu.events,Mu=Iu.pubSub,Nu=Iu.detection,Hu=Iu.dom.tappedClass,Fu=Iu.keyManager,Vu=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tu(e,t)}(o,e);var t,n,r,i=Lu(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),Au(ju(t=i.call(this,e)),"onTap",(function(e){Hu(t.el)})),Au(ju(t),"onClickBtn",(function(e){1!==t.currTimeline.progress()?t.currTimeline.togglePlayback():Nu.theme.isUnified&&t.currTimeline.reset()})),Au(ju(t),"onKeydown",(function(e){var t=e.which;Fu.isSeekKey(t)&&Mu.trigger(Bu.player.SEEK,e)})),Au(ju(t),"onPlaybackChanged",(function(){t.updateToggle(),t.updateView()})),Ru.bindAll(ju(t),"onTimelineChanged"),Mu.on(Bu.player.TOGGLE_PLAYBACK,t.onClickBtn),Mu.on(Bu.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),Nu.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t.el.addEventListener("keydown",t.onKeydown),t.view.toggle=!0,Nu.deviceView.isMobile&&Mu.on(Bu.currTimeline.TICK,t.onTick.bind(ju(t))),t}return t=o,(n=[{key:"teardown",value:function(){Mu.off(Bu.currTimeline.TICK,this.onTick.bind(this))}},{key:"onTick",value:function(e){null!=this.circleProgress&&this.circleProgress.setAttribute("d",DS.svgUtils.wheelPath(17,17,17,0,360*e)),this.lastProgress=e}},{key:"updateView",value:function(){this.view.updateHtml(),this.circleProgress=this.el.querySelector(".circle-progress path"),this.onTick(this.lastProgress)}},{key:"updateToggle",value:function(){this.view.toggle="playing"===this.currTimeline.playbackState(),this.view.el.setAttribute("aria-pressed",!this.view.toggle)}},{key:"onTimelineChanged",value:function(e){this.currTimeline=e,this.view.toggle=this.currTimeline.isPlaying(),this.updateView()}}])&&xu(t.prototype,n),r&&xu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Wu="playPause",Uu=DS.utils,Ku=(Uu.encode,Uu.scaleVal);function zu(e){return zu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zu(e)}function Gu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==zu(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===zu(o)?o:String(o)),r)}var i,o}function qu(e,t){return qu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qu(e,t)}function Qu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Xu(e);if(t){var i=Xu(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Zu(this,n)}}function Zu(e,t){if(t&&("object"===zu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Yu(e)}function Yu(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xu(e){return Xu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Xu(e)}z.def(Wu,Vu,(function(){var e=z.model;return{tag:"button",attrs:{id:Wu,class:"content-center cs-button","aria-pressed":!1,"aria-label":"".concat(e.getString("acc_play"),"/").concat(e.getString("acc_pause")),tabindex:0},style:{background:"transparent",border:"none"},html:function(){return this.toggle?Mi("pause")(Ku(14),Ku(12)):Mi("play")(Ku(14),Ku(11))},y:"vertical-center",minW:30,minH:30,z:1,updateHook:function(){this.el.setAttribute("aria-pressed",!this.toggle);var e=this.el.querySelector("svg");e.height.baseVal.value=Ku(14),e.width.baseVal.value=Ku(this.toggle?12:11)}}}));var $u=DS,Ju=$u._,ef=$u.detection,tf=$u.utils,nf=$u.keyManager,rf=$u.dom,of=rf.addClass,af=rf.removeClass,cf=$u.globalEventHelper.addWindowListener,lf=$u.events,sf=$u.pubSub,uf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qu(e,t)}(o,e);var t,n,r,i=Qu(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Ju.bindAll(Yu(t),"onTick","onSeek","onMouseDown","onMouseUp","checkSeekable","onKeydown","onTimelineChanged","onChange"),sf.on(lf.slide.STARTED,t.checkSeekable),sf.on(lf.player.SEEK,t.onKeydown),sf.on(lf.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),t.isUserControlled=!z.model.frame.controlOptions.controls.readonly||z.model.frame.controlOptions.controls.readonlyOnce,t.bindListeners(),t.isUp=!0,t}return t=o,(n=[{key:"bindListeners",value:function(){if(this.isUserControlled){var e=ef.deviceView.isClassicDesktop?this.seekEl:this.view.el;ef.device.isMobile?(e.addEventListener("touchstart",this.onMouseDown),this.seekEvent="touchmove",this.endEvent="touchend"):(e.addEventListener("mousedown",this.onMouseDown),e.addEventListener("keydown",this.onKeydown),this.seekEvent="mousemove",this.endEvent="mouseup"),this.progressBarEl.addEventListener("change",this.onChange)}}},{key:"isSeekable",value:function(){if(null==this.currSlide)return!1;var e=z.model.frame.controlOptions.controls,t=e.readonly,n=e.readonlyOnce;return!(t&&!n||n&&!this.currSlide.currentTimelineCompletedOnce())}},{key:"getSeekValue",value:function(e){var t=this.view.getBox();return tf.clamp(0,1,(e-t.x)/t.w)}},{key:"getEventX",value:function(e){return ef.device.isMobile?e.touches[0]&&e.touches[0].pageX:e.pageX}},{key:"onTimelineChanged",value:function(e,t){null!=this.currTimeline&&(this.currTimeline.off(lf.timeline.TICK,this.onTick),this.currTimeline.off(lf.timeline.COMPLETE,this.checkSeekable)),this.currSlide=t,this.currTimeline=e,this.currTimeline.on(lf.timeline.TICK,this.onTick),this.currTimeline.on(lf.timeline.COMPLETE,this.checkSeekable);var n=this.currTimeline.progress();this.duration=tf.toSeconds(this.currTimeline.timelineDuration()),this.progressBarEl.max=Math.round(1e3*this.duration),this.progressBarEl.step=100,isNaN(n)&&(n=0),this.onTick(n),this.checkSeekable()}},{key:"checkSeekable",value:function(){this.isSeekable()?(af(this.el,"read-only"),this.progressBarEl.disabled=!1):(of(this.el,"read-only"),this.progressBarEl.disabled=!0)}},{key:"onTick",value:function(e){var t=100*e;this.progressBarFillEl.style.width="".concat(t,"%"),this.progressBarEl.setAttribute("aria-valuetext","".concat(Math.round(t),"%")),this.progressBarEl.value=this.currTimeline.timeline?this.currTimeline.timeline.currentTime:0,sf.trigger(lf.currTimeline.TICK,e)}},{key:"onSeek",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.seeking=!0,!0!==this.isUp&&this.currTimeline.isPlaying()&&(this.currTimeline.pause(!0),this.currTimeline.on(lf.timeline.AFTER_SEEK_UPDATE,(function e(){t.currTimeline.play(),t.currTimeline.off(lf.timeline.AFTER_SEEK_UPDATE,e)})));var n=this.getEventX(e);null!=n&&this.currTimeline.progress(this.getSeekValue(n)),this.isUp=!1}},{key:"seekBy",value:function(e,t){var n=this;e.preventDefault();var r=(this.currTimeline.elapsedTime()+t)/this.currTimeline.duration();this.currTimeline.onSeekStart(),this.currTimeline.progress(r),this.currTimeline.onSeekEnd(),this.currTimeline.isPlaying()&&(this.currTimeline.pause(),setTimeout((function(){return n.currTimeline.play()}),125))}},{key:"isEnded",value:function(){return this.currTimeline.progress()>=1}},{key:"onChange",value:function(e){this.currTimeline.progress(this.progressBarEl.value/this.progressBarEl.max)}},{key:"onMouseDown",value:function(e){this.isUp=!1,this.removeEndListener=cf(this.endEvent,this.onMouseUp),this.removeSeekListener=cf(this.seekEvent,this.onSeek),this.currTimeline.onSeekStart(),this.onSeek(e)}},{key:"onMouseUp",value:function(e){this.onSeek(e),this.removeEndListener(),this.removeSeekListener(),this.isUp=!0,this.currTimeline.onSeekEnd()}},{key:"onKeydown",value:function(e){var t=e.which;nf.isActionKey(t)?sf.trigger(lf.player.TOGGLE_PLAYBACK):this.isSeekable()&&(nf.isDownishKey(t)?this.seekBy(e,-100):nf.isPageDownKey(t)?this.seekBy(e,-1e3):nf.isUpishKey(t)?this.seekBy(e,100):nf.isPageUpKey(t)&&this.seekBy(e,1e3))}}])&&Gu(t.prototype,n),r&&Gu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),ff="seek",df=DS,hf=df.utils,pf=hf.pxify,yf=hf.scaleVal,bf=df.vendorOverrides.fixStyleString;function vf(e){return vf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vf(e)}function mf(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==vf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==vf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===vf(o)?o:String(o)),r)}var i,o}function gf(e,t){return gf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},gf(e,t)}function wf(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Cf(e);if(t){var i=Cf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Sf(this,n)}}function Sf(e,t){if(t&&("object"===vf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return kf(e)}function kf(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cf(e){return Cf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Cf(e)}z.def(ff,uf,(function(){var e=z.model,t=z.model.frame.controlOptions.controls,n=t.readonly,r=z.getCurrentNameSpaceString(),i=function(){return bf("#seek:before {\n      border: 1px solid ".concat(fe.getColor(r,".cs-seekcontrol","border-top-color"),";\n      background-image: ").concat(fe.getColor(r,".cs-seekcontrol","background-image").replace("(, ","(180deg,"),";\n      background-repeat: no-repeat !important;\n      border-radius: ").concat(pf(yf(7)),";\n      height: ").concat(pf(yf(30))," !important;\n      width: calc(100% + ").concat(pf(yf(60)),");\n      left: -").concat(pf(yf(30)),";\n    }"))};return{attrs:{id:ff,tabindex:-1,class:"progress-bar cs-seekcontrol ".concat(n?"read-only":"")},y:"vertical-center",overflow:"visible",noUpdate:!0,w:function(){return this.width||0},h:30,html:'\n      <style data-ref="styleContainer" class="style-container">\n        '.concat(i(),'\n      </style>\n\n      <div class="cs-seekbar-inner progress-bar-inner slide-lockable" data-ref="seekInner">\n        <div data-ref="seek" class="cs-seek progress-bar-seek" style="height: ').concat(pf(yf(18)),'; position: absolute; top: 50%; transform: translate(0%, -50%);">\n          <input\n            tabIndex="0"\n            type="range"\n            aria-hidden="').concat(!t.seekbar,'"\n            aria-label="').concat(e.getString("acc_slide_progress"),'"\n            data-ref="progressBar"\n          >\n          <div class="cs-fill progress-bar-fill" style="width: 0px" data-ref="progressBarFill"></div>\n        </div>\n      </div>\n    '),updateHook:function(){this.children.seek.el.style.height=pf(yf(18)),this.children.seekInner.el.style.top=pf(yf(6)),this.children.styleContainer.el.innerText=i()}}}));var Of=DS,Ef=Of._,xf=Of.events,Tf=Of.pubSub,Lf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gf(e,t)}(o,e);var t,n,r,i=wf(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Ef.bindAll(kf(t),"onClickBtn","onTimelineChanged"),t.onClick(t.onClickBtn),Tf.on(xf.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),t}return t=o,(n=[{key:"onClickBtn",value:function(){this.resetTimeline()}},{key:"resetTimeline",value:function(){this.currTimeline.reset()}},{key:"onTimelineChanged",value:function(e){this.currTimeline=e}}])&&mf(t.prototype,n),r&&mf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Pf="reset",jf=DS.utils;jf.encode,jf.scaleVal;function _f(e){return _f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_f(e)}function Af(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==_f(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==_f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===_f(o)?o:String(o)),r)}var i,o}function Df(e,t){return Df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Df(e,t)}function If(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Bf(e);if(t){var i=Bf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Rf(this,n)}}function Rf(e,t){if(t&&("object"===_f(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Bf(e){return Bf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Bf(e)}z.def(Pf,Lf,(function(){return{tag:"button",attrs:{id:Pf,class:"content-center cs-button","aria-label":z.model.getString("acc_replay"),tabindex:0},style:{background:"transparent",border:"none"},html:function(){return Mi("replay")()},y:"vertical-center",z:1,minW:30,minH:30}}));var Mf=DS,Nf=Mf.detection,Hf=Mf.dom,Ff=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Df(e,t)}(o,e);var t,n,r,i=If(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Nf.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t}return t=o,(n=[{key:"onTap",value:function(e){Hf.tappedClass(this.el)}},{key:"onClickBtn",value:function(e){var t=this.view,n=t.enabled,r=t.nameKey;n&&DS.pubSub.trigger(DS.events.presentation.ON_OBJECT_EVENT,r+"_pressed")}},{key:"onLayoutChange",value:function(e){var t=this,n=this.view.nameKey;this.hasFocus&&(e[n]?window.requestAnimationFrame((function(){return t.onFocus()})):this.onBlur())}}])&&Af(t.prototype,n),r&&Af(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Vf="prev",Wf=DS.utils;Wf.pxify,Wf.scaleVal;z.def(Vf,Ff,(function(){var e=z.model,t=z.model,n=t.dir,r=t.rtl;return{tag:"button",attrs:{id:Vf,class:"cs-button btn",tabindex:0,"aria-label":e.getString(r?"acc_next":"acc_previous")},html:n([Mi(r?"next":"prev")(),e.getString("prev")]).join("\n"),methods:{shortcutActivated:function(){this.visible&&this.viewLogic.onClickBtn()}},calcTextSize:!0,w:"fit-to-text-w",y:"vertical-center",padLeft:r?0:8,padRight:r?8:0,minW:71,minH:30}}));var Uf="next",Kf=DS.utils;Kf.pxify,Kf.scaleVal;z.def(Uf,Ff,(function(){var e=z.model,t=z.model,n=t.dir,r=t.rtl;return{tag:"button",attrs:{id:Uf,class:"cs-button btn","aria-label":e.getString(r?"acc_previous":"acc_next"),tabindex:0},html:n([e.getString("next"),Mi(r?"prev":"next")()]).join("\n"),methods:{shortcutActivated:function(){this.visible&&this.viewLogic.onClickBtn()}},calcTextSize:!0,padRight:r?0:8,padLeft:r?8:0,w:"fit-to-text-w",y:"vertical-center",minW:71,minH:30}}));var zf="submit";function Gf(e){return Gf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gf(e)}function qf(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Gf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Gf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Gf(o)?o:String(o)),r)}var i,o}function Qf(e,t){return Qf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qf(e,t)}function Zf(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=$f(e);if(t){var i=$f(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Yf(this,n)}}function Yf(e,t){if(t&&("object"===Gf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Xf(e)}function Xf(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $f(e){return $f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$f(e)}z.def(zf,Ff,(function(){var e=z.model;return{tag:"button",attrs:{id:zf,class:"cs-button btn","aria-label":e.getString("acc_submit"),tabindex:0},html:"".concat(z.model.getString(zf)),methods:{shortcutActivated:function(){this.visible&&this.viewLogic.onClickBtn()}},overflow:"visible",w:"fit-to-text-w",y:"vertical-center",calcTextSize:!0,minW:71,minH:30,noUpdate:!0}}));var Jf=DS,ed=Jf._,td=Jf.detection,nd=Jf.pubSub,rd=Jf.events,id=Jf.focusManager,od=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qf(e,t)}(o,e);var t,n,r,i=Zf(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),ed.bindAll(Xf(t),"onStart","onClickCallback"),nd.once(rd.request.START,t.onStart),t}return t=o,(n=[{key:"onStart",value:function(e){this.rejectDeferred=e,this.el.classList.add("opaque"),this.playIconEl.addEventListener("click",this.onClickCallback),this.toggleVisibility(!0),td.env.is360||this.playIconEl.focus(),nd.trigger(rd.startOverlay.READY)}},{key:"onFocus",value:function(){id.setFocusRectOn(this.playIconEl)}},{key:"onClickCallback",value:function(){var e=this;this.view.children.playIcon.el.removeEventListener("click",this.onClickCallback),nd.trigger(rd.loader.UNMUTE),nd.trigger(rd.course.AUDIO_START),td.deviceView.isMobile?nd.once(rd.slide.STARTED,(function(){e.el.classList.remove("opaque")})):this.el.classList.remove("opaque"),this.rejectDeferred(),this.toggleVisibility(!1)}},{key:"toggleVisibility",value:function(e){this.el.classList.add(e?"shown":"hidden"),this.el.classList.remove(e?"hidden":"shown")}}])&&qf(t.prototype,n),r&&qf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),ad=DS.stringTabler;z.def("startoverlay",od,(function(){return{attrs:{class:"start-overlay hidden","aria-modal":"true",role:"dialog","aria-label":"".concat(ad.getString(DS.constants.strings.PROJECT_TITLE)," ").concat(ad.getString("desktop_start"))},x:0,y:0,w:"100%",h:"100%",add:!0,html:function(){return"<button\n        aria-label=".concat(ad.getString("desktop_start"),'\n        data-ref="playIcon"\n        tabindex="0"\n        data-leavealone="0">\n        <div class="cs-button">\n          <svg viewBox="0 0 100 100" width="100" height="100">\n            <path d="M 81.94 50.00 L 31.03 20.61 L 31.03 79.39" fill="currentColor" />\n          </svg>\n        </div>\n      </div>')}}}));function cd(e){return cd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cd(e)}function ld(e,t,n){return(t=ud(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function sd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ud(r.key),r)}}function ud(e){var t=function(e,t){if("object"!==cd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==cd(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===cd(t)?t:String(t)}function fd(e,t){return fd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},fd(e,t)}function dd(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=yd(e);if(t){var i=yd(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return hd(this,n)}}function hd(e,t){if(t&&("object"===cd(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return pd(e)}function pd(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yd(e){return yd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yd(e)}z.def("captionContainer",(function(){return{attrs:{class:"caption-container"},z:2,w:"100%",h:"100%",add:!0}}));var bd=DS,vd=bd._,md=bd.pubSub,gd=bd.dom,wd=bd.detection,Sd=bd.events,kd=bd.utils.pxify,Cd=bd.constants.DESKTOP_LOADER_DELAY*(wd.browser.isIE?2:1),Od=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fd(e,t)}(o,e);var t,n,r,i=dd(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),n=i.call(this,e),vd.bindAll(pd(n),"onShow","onHide","onMouseMove","onCtxMenu"),md.on((ld(t={},Sd.app.SHOW_LOADER,n.onShow),ld(t,Sd.app.HIDE_LOADER,n.onHide),t)),n.view.el.addEventListener("contextmenu",n.onCtxMenu),n}return t=o,(n=[{key:"onShow",value:function(){var e=this;this.spinnerTimeout=setTimeout((function(){document.body.addEventListener("mousemove",e.onMouseMove),e.updateSpinnerLoc(),e.view.el.style.cursor="none",e.view.setVisibility(!0)}),Cd)}},{key:"onHide",value:function(){clearTimeout(this.spinnerTimeout),document.body.removeEventListener("mousemove",this.onMouseMove),this.view.el.style.cursor="",this.view.setVisibility(!1)}},{key:"onMouseMove",value:function(){this.updateSpinnerLoc()}},{key:"onCtxMenu",value:function(e){e.preventDefault()}},{key:"updateSpinnerLoc",value:function(){this.spinnerEl.style.left=kd(gd.mouseX),this.spinnerEl.style.top=kd(gd.mouseY)}},{key:"teardown",value:function(){md.off("app:showLoader",this.onShow),md.off("app:hideLoader",this.onHide),this.view.el.removeEventListener("contextmenu",this.onCtxMenu)}}])&&sd(t.prototype,n),r&&sd(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),Ed="slideLoaderOverlay";function xd(e){return function(e){if(Array.isArray(e))return Td(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Td(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Td(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Td(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}z.def(Ed,Od,(function(){z.model;return{attrs:{id:Ed},w:null,h:null,html:'<div class="slide-loader" data-ref="spinner"></div>'}}));var Ld=DS.constants.refs.FRAME,Pd=DS,jd=Pd.pubSub,_d=Pd.events,Ad=Pd.scaler;function Dd(e){return Dd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dd(e)}function Id(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Dd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Dd(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Dd(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Rd="lightBoxWrapper",Bd=DS,Md=Bd.utils.scaleVal,Nd=Bd.scaler;z.def(Rd,(function(e){var t,n=z.model,r=n.frame.fontscale,i=n.preso.display().windows().find({id:e});return Id(t={attrs:{id:Rd,class:"cs-base cs-".concat(n.frame.default_layout," fn-").concat(n.frame.default_layout)},style:{fontSize:"".concat(Md(r),"%")}},"style",{transformOrigin:"0px 0px",transform:"translate(-50%, -50%)",background:"black"}),Id(t,"dimScale",(function(){return Nd.getScale()})),Id(t,"overflow","visible"),Id(t,"w",i.get("width")),Id(t,"h",i.get("height")),Id(t,"x",(function(){return(window.innerWidth-Md(this.w))/2})),Id(t,"y",(function(){return(window.innerHeight-Md(this.h))/2})),Id(t,"updateHook",(function(){this.el.style.fontSize="".concat(Md(r),"%")})),Id(t,"add",!0),t}));var Hd=DS.constants.LIGHTBOX_SCALAR,Fd="lightBoxSlide";z.def(Fd,jn,(function(){var e=z.model;return{attrs:{class:Fd,role:"dialog","aria-modal":!0},origin:"0 0",w:e.slideWidth*Hd,h:e.slideHeight*Hd,x:function(){return(this.parent.w-this.w)/2},y:function(){return this.x},z:1,bgColor:"black",add:!0,html:'<div id="slide-label-lightbox" data-ref="label" aria-live="polite"></div><main data-ref="container" class="slide-container" tabindex="-1"></main>',childViews:["captionContainer"],updateHook:function(){this.children.captionContainer.update()},winScale:function(){return DS.scaler.getScale()*Hd}}}));var Vd="lightBox",Wd=DS.utils,Ud=Wd.scaleVal,Kd=Wd.pxify;function zd(e){return zd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zd(e)}function Gd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==zd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zd(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===zd(o)?o:String(o)),r)}var i,o}function qd(e,t){return qd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qd(e,t)}function Qd(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Yd(e);if(t){var i=Yd(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Zd(this,n)}}function Zd(e,t){if(t&&("object"===zd(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Yd(e){return Yd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yd(e)}z.def(Vd,(function(){return{attrs:{class:"".concat(Vd," cs-base")},x:0,y:0,w:"100%",h:"100%",border:"".concat(Kd(Ud(4))," solid black"),offsets:{l:4,t:4},overflow:"visible",contentStyle:{width:"100%",height:"100%"},updateHook:function(){this.el.style.borderWidth=Kd(Ud(4))}}}));var Xd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qd(e,t)}(o,e);var t,n,r,i=Qd(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickClose),t}return t=o,(n=[{key:"onClickClose",value:function(e){var t=this.model.windowId;DS.pubSub.trigger(DS.events.window.CLOSING,t)}}])&&Gd(t.prototype,n),r&&Gd(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Rt),$d="lightBoxClose";DS.utils.scaleVal;z.def($d,Xd,(function(e){var t={windowId:e};return{tag:"button",attrs:{id:$d,tabindex:0,"aria-label":DS.stringTabler.getString("close")},html:Mi("close"),y:function(){return-this.h/2+2},x:function(){return this.parent.w-this.w/2-2},model:t,w:20,h:20}}));var Jd=DS.scaler,eh=function(e){return e/Jd.getScale()};z.def("visibleOverlay",(function(){return{attrs:{class:"visible-overlay"},x:0,y:0,w:function(){return eh(window.innerWidth)},h:function(){return eh(window.innerHeight)},position:"fixed",bgColor:"rgba(0, 0, 0, 0.4)",add:!0}}));var th="LightboxWnd",nh="lightBoxBottom";z.def(nh,(function(){return{tag:"nav",attrs:{id:nh},x:-4,y:function(){return this.parent.h-this.h-4},w:"100%",h:it,updateHook:function(){if(this.hasAllChildren()){this.flowChildren({pad:nt,startPos:this.w,toLeft:!0});var e=this.children.captions;e.x=nt,e.update()}}}}));var rh="LightboxControlsWnd",ih="printWrapper";z.def(ih,(function(e){return{attrs:{id:ih,class:"print-window"},w:0,h:0,style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},scale:1,x:0,y:0,add:!0}}));var oh=DS.constants.printSettings,ah="printSlide";z.def(ah,jn,(function(e){var t=z.model.slideWidth;return{attrs:{id:ah,class:"cs-window window-slide ".concat(e,"-slide"),tabindex:-1},style:{overflow:"visible",transformOrigin:"0 0",background:"transparent",display:"none"},origin:"0 0",winScale:function(){return this.w/t},w:function(){var e=DS.detection.os,t=e.isAndroid,n=e.isIOS;return t?oh.android.pageW:n?oh.ios.pageW:oh.desktop.pageW},h:0,x:0,y:0,z:1,html:'<div data-ref="container" class="slide-container"></div>'}}));var ch="PrintWindow",lh="messageWindowWrapper",sh=DS,uh=sh.scaler,fh=(sh.utils.scaleVal,function(e){return e/uh.getScale()});z.def(lh,(function(e){var t=z.model,n=t.preso.display().windows().find({id:e}),r=z.getNamespace(DS.constants.refs.FRAME),i=r.slide,o=r.wrapper;return{attrs:{id:lh,class:"cs-".concat(t.frame.default_layout," fn-").concat(t.frame.default_layout)},w:n.get("width"),h:n.get("height"),style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},dimScale:function(){return uh.getScale()},x:function(){return fh(o.x)+(i.x+(i.w-this.w)/2)},y:function(){return fh(o.y)+(i.y+(i.h-this.h)/2)},add:!0}}));var dh="messageWindowSlide";z.def(dh,jn,(function(){return{attrs:{id:dh,class:"cs-window","aria-labelledby":"slide-label-message",role:"alertdialog","aria-modal":!0,tabindex:-1},origin:"0 0",w:"100%",h:"100%",x:0,y:0,z:1,html:'<div id="slide-label-message" data-ref="label" role="presentation"></div><div data-ref="container" class="slide-container"></div>',winScale:function(){return DS.scaler.getScale()}}}));z.def("overlay",(function(){return{attrs:{class:"overlay overlay-message"},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},position:"fixed",add:!0}}));var hh="MessageWnd",ph=DS.scaler,yh="shortcutWrapper";z.def(yh,(function(e){var t=z.model,n=z.getNamespace(DS.constants.refs.FRAME),r=(n.slide,n.wrapper);return{attrs:{id:yh,class:"cs-base cs-".concat(t.frame.default_layout," fn-").concat(t.frame.default_layout)},w:function(){return.7*r.w},h:function(){return.7*r.h},style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},x:function(){return r.x/ph.getScale()+(r.w-this.w)/2},y:function(){return r.y/ph.getScale()+(r.h-this.h)/2},add:!0}}));var bh=function(){return"no icon"},vh={track:function(e,t){return'\n      <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16">\n        <defs>\n            <rect id="'.concat(t,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n            <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(t,'-trackFilter">\n                <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n                <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n                <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n            </filter>\n        </defs>\n        <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#D1D1D1" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n                <use fill="white" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n                <use stroke="#AFAFAF" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n                <circle fill="#585858" stroke-width="0" cx="8" cy="8" r="5"></circle>\n            </g>\n        </g>\n        <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#DBDBDB" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n                <use fill="white" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n                <use stroke="#AFAFAF" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n                <circle fill="').concat(e,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n            </g>\n        </g>\n      </svg>\n    ')}};function mh(e){return mh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mh(e)}function gh(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==mh(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==mh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===mh(o)?o:String(o)),r)}var i,o}function wh(){return wh="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Sh(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},wh.apply(this,arguments)}function Sh(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=xh(e)););return e}function kh(e,t){return kh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},kh(e,t)}function Ch(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=xh(e);if(t){var i=xh(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Oh(this,n)}}function Oh(e,t){if(t&&("object"===mh(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Eh(e)}function Eh(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xh(e){return xh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xh(e)}var Th="shortcutSlide",Lh=DS,Ph=Lh._,jh=Lh.shortcutManager,_h=Lh.keyManager,Ah=Lh.pubSub,Dh=Lh.events,Ih=Dh.window.CLOSING,Rh=Dh.player.ENABLE_KEYBOARD_SHORTCUTS,Bh=Lh.MicroScrollBar,Mh=Lh.focusManager,Nh=(Lh.flagManager,Lh.constants),Hh=Lh.globalEventHelper.addDocumentListener;var Fh=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kh(e,t)}(o,e);var t,n,r,i=Ch(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Ph.bindAll(Eh(t),"onKeyboardShortcutsChanged","toggleKeyboardShortcuts","onFocus","onKeydown"),Ah.on(Ih,t.closeWindow),t.removeKeydownListener=Hh("keydown",t.onKeydown),null!=t.keyboardShortcutsSwitchEl&&(t.onClickEl(t.keyboardShortcutsSwitchEl,t.toggleKeyboardShortcuts),Ah.on(Rh,t.onKeyboardShortcutsChanged)),t}return t=o,(n=[{key:"onKeydown",value:function(e){_h.isKey(e.which,Nh.keys.ESCAPE)&&this.closeWindow("ShortcutWnd")}},{key:"closeWindow",value:function(e){"ShortcutWnd"===e&&jh.closeShortcutWindow()}},{key:"onKeyboardShortcutsChanged",value:function(e){e?(this.keyboardShortcutsSwitchEl.classList.add("toggle-on"),this.keyboardShortcutsSwitchEl.classList.remove("toggle-off"),this.shortcutsTableEl.classList.remove("disabled")):(this.keyboardShortcutsSwitchEl.classList.add("toggle-off"),this.keyboardShortcutsSwitchEl.classList.remove("toggle-on"),this.shortcutsTableEl.classList.add("disabled")),this.keyboardShortcutsSwitchEl.querySelector("button").setAttribute("aria-checked",e)}},{key:"toggleKeyboardShortcuts",value:function(){jh.enableShortcuts(!jh.enabled)}},{key:"onFocus",value:function(e){var t=e.target;this.el.contains(t)&&Mh.setFocusRectOn(t)}},{key:"teardown",value:function(){Ah.off(Ih,this.closeWindow),null!=this.keyboardShortcutsSwitchEl&&Ah.off(Rh,this.onKeyboardShortcutsChanged),this.removeKeydownListener(),wh(xh(o.prototype),"teardown",this).call(this)}}])&&gh(t.prototype,n),r&&gh(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(jn);z.def(Th,Fh,(function(){var e=z.model;return{attrs:{id:Th,class:"shortcut-slide","aria-labelledby":"slide-label-message",role:"alertdialog","aria-modal":!0,tabindex:-1},style:{overflow:"scroll"},origin:"0 0",w:"100%",h:"100%",x:0,y:0,z:1,html:function(){var t=DS.detection.theme.isUnified?[Nh.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme"]:[Nh.refs.FRAME,".cs-duration stop:last-child","stop-color",void 0,".cs-duration stop"],n=fe.getColor.apply(fe,t),r='\n        <div class="switch '.concat(jh.enabled?"toggle-on":"toggle-off",'" data-ref="keyboardShortcutsSwitch">\n          <label id="shortcuts-label" class="switch-label">').concat(e.getString("enable_keyboardshortcuts"),'</label>\n          <button class="switch-toggle" tabindex="0" role="switch" aria-checked="').concat(jh.enabled,'" aria-labelledby="shortcuts-label">\n            ').concat((vh["track"]||bh)(n,"shortcuts"),"\n          </button>\n        </div>"),i="<h1>".concat(e.getString("keyboardshortcuts")).concat(r,"</h1>"),o=jh.getShortcutList().map((function(t){var n=t.name,r=t.keyInfo;return'<tr class="'.concat("?"===r.key?"":"disableable",'"><td class="action">').concat(n,'</td><td class="key">').concat(function(e,t){return(e.ctrl?t.getString("ctrl_key")+"+":"")+(e.alt?t.getString("alt_key")+"+":"")+(e.shift?t.getString("shift_key")+"+":"")+(e.keyName||e.key)}(r,e),"</td></tr>")})).join(""),a='<tr><th class="action">'.concat(e.getString("action"),'</th><th class="key">').concat(e.getString("shortcut"),"</th></tr>");return[i,'<table data-ref="shortcutsTable">'.concat(a).concat(o,"</table>")].join("")},childDef:function(){null==this.scrollbar&&(this.scrollbar=new Bh(this.el,"shortcut-slide"))}}}));var Vh=DS.scaler,Wh="shortcutClose";z.def(Wh,Xd,(function(e){var t={windowId:e};return{tag:"button",attrs:{id:Wh,class:"close-btn-floating",tabindex:0,"z-index":999,"aria-label":z.model.getString("close")},style:{overflow:"visible"},html:function(){return'<svg class="cs-icon icon-close" width="'.concat(20*Vh.getScale(),'" height="').concat(20*Vh.getScale(),'" viewBox="0 0 36 36" focusable="false">\n                <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n              </svg>')},y:35,x:function(){var e=z.getNamespace("_frame").wrapper;return Math.max(window.innerWidth/Vh.getScale(),e.w)-(this.w+35)},model:t,w:20,h:20}}));var Uh,Kh="ShortcutWnd";function zh(e){return zh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zh(e)}function Gh(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==zh(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===zh(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t){$e(e),function(e){var t=window.globalProvideData;if(window.globalProvideData=function(e,n){"frame"===e&&(be=JSON.parse(n),Ke.register("frame",be.flags,Ve),pe.hasData=!0,We.trigger(Le,pe),window.globalProvideData=t)},We.on(Pe,(function(e){new Ce,ye=new s(be,e),z.setModel(ye),Ge.initialize(ye),qe.initialize((function(e){return ye.getString(e)})),DS.frameModel=ye,DS.views=z,We.trigger(Ae)})),window.globals.useJson){var n=e.replace(".js",".json"),r=new XMLHttpRequest;return r.overrideMimeType("application/json"),r.onreadystatechange=function(){4===r.readyState&&200===r.status&&window.globalProvideData("frame",r.responseText.replace(/\\'/g,"'").replace(/\\"/g,'"'))},r.open("GET",n,!0),void r.send(null)}DS.loadScript(e)}(t)}((Gh(Uh={},Ld,(function(e){var t=e.topTabsLeft,n=e.topTabsRight,r=e.sidebarOpts,i=r.timeEnabled,o=r.logoEnabled,a=r.html5_logo_url,c=r.logoPlaceholderText,l=e.frame.controlOptions.controls,s=l.closed_captions,u=l.settings,f=z.tree(Ld,[{wrapper:[{frame:["slide",e.frame.skip_nav_enabled?"skipnav":null,{bottomBar:[{playbackControls:["playPause","seek","reset"]},{miscControls:["volume"].concat(xd(s?["captions"]:[]),xd(u?["settings"]:[]))},{navControls:["prev","next","submit"]}]},{sidebar:[].concat(xd(o&&(a||c)?["logo"]:[]),["tabs","sidebarPanels"])},{topBar:["title",{topTabs:["linksLeft","linksRight"]}].concat(xd(i?["timer"]:[]))}]},"startoverlay"]},window.globals.HAS_SLIDE?"slideLoaderOverlay":null]),d=function(e){return function(t){return t.name===e}},h=function(e){return!t.some(d(e))&&!n.some(d(e))},p=function(t,n){if(n===Ld){var r=function(e,t){var n=e.sidebarOpts.sidebarEnabled;return n&&(n=tt(e.sidebarTabs,t)),n||e.sidebarOpts.logoEnabled}(e,t),i=tt([].concat(xd(e.topTabsRight),xd(e.topTabsLeft)),t),o={playbackControls:t.seekbar,next:t.next,prev:t.previous,submit:t.submit,skipnav:r||i,volume:t.volume,glossaryTab:t.glossary,glossaryPanel:t.glossary&&h("glossary"),glossaryLink:t.glossary,resourcesTab:t.resources,resourcesPanel:t.resources&&h("resources"),resourcesLink:t.resources,outlineTab:t.outline.enabled,outlinePanel:t.outline.enabled&&h("outline"),outlineLink:t.outline.enabled,search:t.outline.search,transcriptTab:t.transcript,transcriptPanel:t.transcript&&h("transcript"),transcriptLink:t.transcript,sidebar:r,bottomBar:e.bottomBarOpts.bottomBarEnabled};z.resetStates(Ld),z.updateVisibility(o,Ld),z.update(f)}};jd.on(_d.frameModel.LAYOUT_CHANGED,p);var y=function(e){var t=e.kind,n=e.name,r=e.visible,i=e.enable,o=e.affectTabStop,a="toggle_window_control_visible"===t||"toggle_window_control"===t;"previous"===n&&(n="prev");var c=z["enable_window_control"===t||"toggle_window_control"===t?"getTopNameSpace":"getFrameNameSpace"]()[n];null!=c&&(a?"toggle_window_control_visible"===t?(c.setVisibility(!c.visible),c.childVisibilityChanged()):c.setEnabled(!c.enabled,o):"set_window_control_visible"===t?(c.setVisibility(r),c.childVisibilityChanged()):c.setEnabled(i,o))};jd.on(_d.navcontrols.CHANGED,y);var b=new yt(e);b.update();var v=Ad.isShowAll()?function(){b.update()}:_.noop;return{all:function(){z.update(f),v()},resize:function(){z.update(f),v()},rerender:function(){z.update(f,!0),v(),z.nameSpaces[Ld].wrapper.updateChildren(!0),DS.renderEngine.createWindowFor()},destroy:function(){jd.off(_d.navcontrols.CHANGED,y),jd.off(_d.frameModel.LAYOUT_CHANGED,p),z.nameSpaces[Ld].topLevelElements.forEach((function(e){return e.destroy()}))}}})),Gh(Uh,th,(function(e){var t=z.tree(th,["visibleOverlay",{lightBoxWrapper:["lightBoxSlide","lightBox","lightBoxClose"]}]),n=z.getNamespace(th);return n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper,{all:function(){return z.update(t)},resize:function(){n.isAttached&&z.update(t)}}})),Gh(Uh,rh,(function(e){var t=z.tree(rh,["visibleOverlay",{lightBoxWrapper:["lightBoxSlide",{lightBox:[{lightBoxBottom:["submit","next","prev","captions"]}]},"lightBoxClose"]}]),n=z.getNamespace(rh);n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper;var r=function(e,n){if(n===rh){var r={next:e.next,prev:e.previous,submit:e.submit};z.resetStates(rh),z.updateVisibility(r,rh),z.update(t)}};return DS.pubSub.on(DS.events.frameModel.LAYOUT_CHANGED,r),r(e.currControlLayout),{all:function(){return z.update(t)},resize:function(){n.isAttached&&z.update(t)}}})),Gh(Uh,ch,(function(e){z.tree(ch,[{printWrapper:["printSlide"]}]);var t=z.getNamespace(ch);return t.slide=t.printSlide,t.wrapper=t.printWrapper,{all:function(){},resize:function(){},pinchZoom:function(){}}})),Gh(Uh,hh,(function(e){var t=z.tree(hh,["overlay",{messageWindowWrapper:["messageWindowSlide"]}]),n=z.getNamespace(hh);return n.slide=n.messageWindowSlide,n.wrapper=n.messageWindowWrapper,{all:function(){return z.update(t)},resize:function(){n.isAttached&&z.update(t)}}})),Gh(Uh,Kh,(function(e){var t=z.tree(Kh,["visibleOverlay",{shortcutWrapper:["shortcutSlide"]},"shortcutClose"]),n=z.getNamespace(Kh);return n.slide=n.shortcutSlide,n.wrapper=n.shortcutWrapper,{all:function(){return z.update(t)},resize:function(){n.isAttached&&z.update(t,!0)}}})),Uh),"html5/data/js/frame.js")}();
