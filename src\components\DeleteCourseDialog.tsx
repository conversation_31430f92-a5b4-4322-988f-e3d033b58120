import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Course } from '@/data/mockData';
import { AlertTriangle, Trash2 } from 'lucide-react';

interface DeleteCourseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  course: Course | null;
  onConfirm: (courseId: string) => void;
}

const DeleteCourseDialog: React.FC<DeleteCourseDialogProps> = ({
  isOpen,
  onClose,
  course,
  onConfirm,
}) => {
  const handleConfirm = () => {
    if (course) {
      onConfirm(course.id);
    }
    onClose();
  };

  if (!course) return null;

  const totalLessons = course.modules.reduce(
    (acc, module) => acc + module.lessons.length,
    0
  );

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="animate-scale-in">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="w-5 h-5" />
            Confirmar Exclusão
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-3">
            <p>
              Tem certeza que deseja excluir o curso{' '}
              <span className="font-semibold">"{course.title}"</span>?
            </p>
            
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <p className="text-sm font-medium">Informações do curso:</p>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Instrutor: {course.instructor}</li>
                <li>• Módulos: {course.modules.length}</li>
                <li>• Lições: {totalLessons}</li>
                <li>• Alunos matriculados: {course.students.length}</li>
              </ul>
            </div>

            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-destructive">
                    Atenção: Esta ação não pode ser desfeita!
                  </p>
                  <p className="text-sm text-destructive/80">
                    Todos os dados do curso, incluindo módulos, lições e progresso dos alunos serão perdidos permanentemente.
                  </p>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel className="focus-ring">
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-ring"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Excluir Curso
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteCourseDialog;
