﻿/*! ds-bootstrap - v1.0.0.30234 - 2023-04-7 8:37pm UTC
* Copyright (c) 2023 ; Not Licensed */!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(o=i.key,a=void 0,a=function(t,n){if("object"!==e(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,n||"default");if("object"!==e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(o,"string"),"symbol"===e(a)?a:String(a)),i)}var o,a}var n=DS,r=n._,i=n.pubSub,o=n.events,a=n.constants,l=[],s=function(){function e(t,n){var l=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.frame=t,this.frame.blocked=!1,this.preso=n,this.setupControlOptions(),this.layouts={},this.dockSettings={dockedState:a.docked.NONE,width:0},this.setLayout(this.frame.default_layout,a.refs.FRAME),this.resourceDescription=this.frame.resourceData.description;var s=n.getFirstSlide();for(var c in this.slideWidth=s.get("width"),this.slideHeight=s.get("height"),this.temp=[],this.frame.layouts)this.temp.push(c);this.rtl="rtl"===this.frame.textdirection,this.hasModernText=0!==this.frame.renderingEngineType,this.dir=this.dir.bind(this),r.bindAll(this,"onLayoutChanged"),i.on(o.controlLayout.CHANGED,this.onLayoutChanged),i.on(o.controlLayout.UPDATE,(function(e,t,n){l.frame.controlLayouts[e]=n,l.setLayout(e,t)})),i.on(o.controlOptions.CHANGED,(function(e){var t=l.optionChangesRequireMenuRefresh(l.frame.controlOptions.menuOptions,e.menuOptions);l.frame.controlOptions=e,l.setupControlOptions(),i.trigger(o.controlOptions.RESET),t&&i.trigger(o.navData.REFRESH_VIEW)})),i.on(o.frame.FONT_SCALE,(function(e){l.frame.fontscale=e,i.trigger(o.controlOptions.RESET)})),i.on(o.glossary.UPDATE,(function(e){l.frame.glossaryData=e,i.trigger(o.glossary.REFRESH_VIEW)})),i.on(o.navData.UPDATE,(function(e){l.frame.navData=e,i.trigger(o.navData.REFRESH_VIEW)})),i.on(o.resources.UPDATE,(function(e){l.frame.resourceData.resources=e,i.trigger(o.resources.REFRESH_VIEW)})),i.on(o.resources.UPDATE_DESCRIPTION,(function(e){l.frame.resourceData.description=e,i.trigger(o.resources.REFRESH_VIEW)})),i.on(o.layer.DIALOG_SHOWN,(function(e){var t=DS.windowManager.getCurrentWindow();i.trigger(o.frameModel.BLOCKED_CHANGED,t.frame.id,!0,e)})),i.on(o.layer.DIALOG_HIDDEN,(function(){var e=DS.windowManager.getCurrentWindow();i.trigger(o.frameModel.BLOCKED_CHANGED,e.frame.id,!1)}))}var n,s,c;return n=e,(s=[{key:"setupControlOptions",value:function(){var e=this.frame.controlOptions.sidebarOptions;this.sidebarOpts=e,this.bottomBarOpts=this.frame.controlOptions.bottomBarOptions,this.topTabs=e.tabs.linkRight||[],this.topTabsLeft=e.tabs.linkLeft||[],this.topTabsRight=e.tabs.linkRight||[],this.sidebarTabs=e.tabs.sidebar||[],this.outlineInSidebar=this.sidebarTabs.some((function(e){return"outline"===e.name})),this.buttonOptions=this.frame.controlOptions.buttonoptions,this.title={enabled:e.titleEnabled,text:e.titleText}}},{key:"optionChangesRequireMenuRefresh",value:function(e,t){return e.wrapListItems!==t.wrapListItems||e.autonumber!==t.autonumber}},{key:"onLayoutChanged",value:function(e,t){this.setLayout(e,t)}},{key:"hasTopLinks",value:function(){return 0!==this.topTabsLeft.length||0!==this.topTabsRight.length}},{key:"getString",value:function(e){var t=this.currLayout.string_table,n=this.frame.stringTables[t].string[e];return null==n?(l.includes(e)||(l.push(e),console.warn("could not find ".concat(e," in string table ").concat(t))),e.replace("acc_","").replace(/_/g," ")):n}},{key:"setDocked",value:function(e,t){this.dockSettings={dockedState:e,width:t}}},{key:"setLayout",value:function(e,t){this.currLayout=this.frame.layouts[e],this.currControlLayout=this.frame.controlLayouts[e],this.layouts[t]=this.currControlLayout,i.trigger(o.frameModel.LAYOUT_CHANGED,this.currControlLayout,t)}},{key:"getWndControlLayout",value:function(e){return this.layouts[e]||this.currControlLayout}},{key:"dir",value:function(e){if(null!=e)return this.rtl?e.reverse():e}}])&&t(n.prototype,s),c&&t(n,c),Object.defineProperty(n,"prototype",{writable:!1}),e}(),c=s;function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function f(e,t,n){return(t=y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,y(r.key),r)}}function y(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}var b,v=DS,m=v._,g=v.scaler,w=v.detection,S=v.detection.orientation,k=v.utils,C=k.getPath,O=k.scaleVal,E=k.pxify,L=v.dom,T=L.addClass,x=L.removeClass,P=v.constants.refs.FRAME,I=document.getElementById(DS.constants.els.PRESO),j={x:"w",xl:"w",xp:"w",y:"h",yl:"h",yp:"h",wl:"w",wp:"w",hl:"h",hp:"h"},A=["wrapper","lightBoxWrapper"],D=function(e){DS.flagManager.playbackSpeedControl&&null!=e.beforeUpdateHook&&e.beforeUpdateHook()},B=function(e,t,n){D(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.l+n,e.update(!0),t.l=e.x+e.w},R=function(e,t,n){D(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.r-e.w-n,e.update(!0),t.r=e.x},M=function(e,t,n){D(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.t+n,e.update(!0),t.t=e.y+e.h},N=function(e,t,n){D(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.b-e.h-n,e.update(!0),t.b=e.y},H={l:B,r:R,t:M,b:N},F=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.ViewLogic=t,this.params=n,this.nameKey=null!=n&&n.nameKey||t.nameKey,this.enabled=!0}var t,n,r;return t=e,n=[{key:"init",value:function(e){if(this.hasInited)console.warn("has already initialized",this);else{var t;this.hasInited=!0;var n=this.ViewLogic,r=this.params;null==r&&null!=n?r=n:null!=n&&(t=!0),m.isFunction(r)&&(r=r(this.nameSpace||e)),r.w=r.w||r.minW||100,r.h=r.h||r.minH||100,this.orientationProps(r,"x","y","w","h","scale"),(r=Object.assign({position:"absolute",x:0,y:0,minW:0,maxW:Number.MAX_SAFE_INTEGER,minH:0,maxH:Number.MAX_SAFE_INTEGER,wPad:0,scale:1,visibility:"reflow",visual:!0,bgColor:null,overflow:"hidden",origin:"center center",z:null,opacity:null,visible:!0,attrs:{},noContent:!0,calcTextSize:!1},r)).calcTextSize&&(r.noContent=!1),this.noContent=r.noContent,this.lastWidthByText=0,this.lastHeightByText=0,this.padLeft=r.padLeft||0,this.padRight=r.padRight||0,this.childDef=r.childDef,this.childViews=r.childViews,this.updateHook=r.updateHook,this.onCaptionChanged=r.onCaptionChanged,r.childViews=null,r.updateHook=null,r.childDef=null,r.nameKey=null,r.onCaptionChanged=null,Object.assign(this,r.methods),r.methods=null,this.createDynamicGetters(r),r.visual&&(this.el=document.createElement(r.tag||"div"),this.initAttributes(r.attrs),this.initStyles(r),"button"===r.tag&&(this.el.style.cursor="pointer"),this.initContent(r),r.add&&I.appendChild(this.el),this.hasInitialized=!0),this.initVisibility(r),this.initChildRefs(),t&&(this.viewLogic=new n(this))}}},{key:"orientationProp",value:function(e,t){var n="".concat(t,"l"),r="".concat(t,"p");null!=e[n]&&null!=e[r]&&(e[t]=function(){return S.isLandscape?this[n]:this[r]})}},{key:"orientationProps",value:function(e){for(var t=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];r.forEach((function(n){return t.orientationProp(e,n)}))}},{key:"initContent",value:function(e){if(this.noContent){var t=this.html;null!=t&&(this.el.innerHTML=t)}else this.content=document.createElement("div"),this.content.setAttribute("class","view-content"),this.content.setAttribute("tabindex",-1),Object.assign(this.content.style,{position:"relative","text-align":"center",top:0}),Object.assign(this.content.style,e.contentStyle||{}),this.initAttributes(e.contentAttrs,this.content),null!=e.html&&(this.content.innerHTML=this.html),this.el.appendChild(this.content)}},{key:"initAttributes",value:function(e,t){for(var n in e)if(null!=e[n]){var r=e[n];"id"===n?r=m.kebabCase(r):"tabindex"===n&&-1!==r&&this.el.setAttribute("data-".concat(n),r),(t||this.el).setAttribute(n,r)}}},{key:"initStyles",value:function(e){if(Object.assign(this.el.style,{position:e.position,left:0,top:0,backgroundColor:e.bgColor,border:e.border,overflow:e.overflow,transformOrigin:e.origin,opacity:e.opacity,zIndex:e.z}),null!=C(e,"style.display")){var t=e.style.display;"boolean"==typeof t&&(e.style.display=t?"block":"none")}Object.assign(this.el.style,e.style)}},{key:"initVisibility",value:function(e){!1===e.visible&&this.setVisibility(!1)}},{key:"initChildRefs",value:function(){this.children=[],this.childList=[];for(var e=this.el.querySelectorAll("[data-ref]"),t=0;t<e.length;t++)this.children[e[t].dataset.ref]={el:e[t]}}},{key:"updateHtml",value:function(){var e=this.html;null!=e&&(this.noContent?this.el.innerHTML=e:this.content.innerHTML=e)}},{key:"createDynamicGetters",value:function(t){for(var n in t)"id"!==n&&null!=t[n]&&e.prop(this,n,t[n])}},{key:"updateSize",value:function(){this.el.style.width=E(O(this.w)),this.el.style.height=E(O(this.h))}},{key:"updateTrans",value:function(){var e=A.includes(this.nameKey)?m.identity:O,t=["translate(".concat(E(e(this.x)),", ").concat(E(e(this.y)),")")];if(this.xs)for(var n=0;n<this.xs.length;n++)t.push("translateX(".concat(E(e(this.xs[n])),")"));if(this.ys)for(var r=0;r<this.ys.length;r++)t.push("translateY(".concat(E(e(this.ys[r])),")"));w.deviceView.isMobile&&null!=this.scale&&t.push("scale(".concat(this.scale,")")),this.el.style.transform=t.join(" ")}},{key:"calcChildrensWidth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!0},t=0;return null!=this.children&&this.children.forEach((function(n){e(n)&&(n.update(),t+=n.w)})),t}},{key:"calcChildrensHeight",value:function(){var e=0;return null!=this.children&&this.children.forEach((function(t){t.update(),e+=t.h})),e}},{key:"positionChildren",value:function(e){var t=e.vertical,n=e.toTop,r=e.toLeft,i=e.pad,o=e.startPos,a=e.reverse,l=void 0!==a&&a,s=e.hook,c=void 0===s?m.noop:s,u=e.rtl,f=void 0!==u&&u,h=e.sizeToChildren,p=e.alignChild,y=t?n?N:M:r?R:B,b=Object.assign({l:o,r:o,t:o,b:o},e.bounds),v=l!==f?this.children.slice().reverse():this.children,g=d(t?["height","t"]:["width","l"],2),w=g[0],k=g[1],C=S.isLandscape?m.first:m.last;v.forEach((function(e){(e.beforeReflowHook||m.noop)();var t=p&&C(e.parentAlign);!e.visible&&"reflow"===e.visibility||"-"===t||(t?H[t](e,b,i):y(e,b,i),c(e))})),h&&(this[w]=b[k])}},{key:"flowChildren",value:function(e){(e=Object.assign({pad:0,sizeToChildren:!1,startPos:0,toLeft:!1,reverse:!1,fullUpdate:!1,rtl:!1,hook:function(){}},e)).fullUpdate?this.hasAllChildren()&&(this.positionChildren(e),this.updateSize()):this.positionChildren(e)}},{key:"isBlocked",value:function(){if(w.theme.isClassic)return!1;var e=z.getTopNameSpace(),t=z.getCurrentNameSpace(),n=z.getBlocker(t.name);return null!=e&&null!=n&&n.visible}},{key:"setEnabled",value:function(e,t){this.enabled!=e&&(this.enabled=e,e?x(this.el,"cs-disabled"):T(this.el,"cs-disabled"),this.el.setAttribute("aria-disabled",!e));var n=this.el.getAttribute("data-tabindex");null!=n&&this.el.setAttribute("tabindex",t&&!e||this.isBlocked()?-1:n)}},{key:"setVisibility",value:function(e,t){var n=this.visible!==e,r=!1;return"no-reflow"===this.visibility?(this.el.style.visibility=e?"visible":"hidden",this.el.style.pointerEvents=e?"":"none"):"reflow"===this.visibility&&(this.el.style.display=e?"block":"none",r=!0),t&&(this.layoutDefaultVisible=e),this.visible=e,n&&C(this,"viewLogic.didChangeVisibility")&&this.viewLogic.didChangeVisibility(e),r}},{key:"childVisibilityChanged",value:function(){null!=this.childVisibilityChangedHook?this.childVisibilityChangedHook():null!=this.parent&&this.parent.childVisibilityChanged()}},{key:"update",value:function(e){null==this.beforeUpdateHook||e&&DS.flagManager.playbackSpeedControl||this.beforeUpdateHook(),this.updateSize(),this.updateTrans(),null!=this.updateHook&&this.updateHook()}},{key:"updateChildren",value:function(e){this.children.forEach((function(t){t.update(),e&&t.updateChildren(e)}))}},{key:"doTextCalcs",value:function(){var e=1/g.getScale(),t=this.content.clientWidth*e,n=this.content.clientHeight*e;this.lastWidthByText=t+4+this.padLeft+this.padRight,this.lastHeightByText=n+4}},{key:"wasAppended",value:function(){var e=this;this.calcTextSize&&this.doTextCalcs(),null!=this.childViews&&this.childViews.forEach((function(t){"string"==typeof t&&(t=z.getOrCreateView(t)),t.init(e.nameSpace),e.append(t)}))}},{key:"setChildNum",value:function(e){this.defaultChildNum=e}},{key:"hasAllChildren",value:function(){return this.hasInitialized&&this.children.length===this.defaultChildNum}},{key:"append",value:function(e,t){if(e.parent=this,this.children[m.camelCase(e.nameKey)]=e,t?this.children.unshift(e):this.children.push(e),this.noContent||e.outsideContent?this.el.appendChild(e.el):this.content.appendChild(e.el),e.wasAppended(),e.update(),null==e.nameSpace){for(var n=this;null!=n&&null==n.nameSpace;)n=n.parent;e.nameSpace=n.nameSpace,z.hasNamespace(n.nameSpace)?z.getNamespace(n.nameSpace)[e.nameKey]=e:console.warn("could not find namespace ".concat(n.nameSpace," when appending"))}}},{key:"destroy",value:function(){null!=this.children&&this.children.forEach((function(e){return e.destroy()})),null!=this.viewLogic&&this.viewLogic.teardown(),null!=this.el.parentNode&&this.el.parentNode.removeChild(this.el),this.nameSpace=null}},{key:"startFloat",value:function(){this.floating=!0,this.lastFloatParent=this.el.parentNode,this.shouldReparent&&z.getNamespace(this.nameSpace).wrapper.el.appendChild(this.el)}},{key:"endFloat",value:function(){this.floating&&(this.floating=!1,this.shouldReparent&&this.lastFloatParent.appendChild(this.el))}},{key:"right",value:function(){return this.floating?0:this.x+this.w}},{key:"bottom",value:function(){return this.floating?0:this.y+this.h}},{key:"getBox",value:function(){if(null==z.getNamespace(this.nameSpace).wrapper)return null;var e=z.getNamespace(this.nameSpace).wrapper.dimScale||1,t=(z.getNamespace(this.nameSpace).wrapper.scale||1)*e,n=this.x,r=this.y,i=this.w,o=this.h,a=this.offsets,l=(a=void 0===a?{}:a).l,s=void 0===l?0:l,c=a.t,u=this;for(n=(n+s)*t,r=(r+(void 0===c?0:c))*t;u=u.parent;){var f=null!=u.parent?t:1,d=u.offsets,h=(d=void 0===d?{}:d).l,p=void 0===h?0:h,y=d.t,b=void 0===y?0:y;n+=(u.x+p)*f,r+=(u.y+b)*f}return{x:n,y:r,w:i*=t,h:o*=t}}}],n&&p(t.prototype,n),r&&p(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();F.propFns={"fit-to-text-w":function(){return Math.max(this.minW,this.lastWidthByText)+this.wPad},"fit-to-text-h":function(){return Math.max(this.minH,this.lastHeightByText)},"vertical-center":function(){return this.parent.h/2-this.h/2},"horizontal-center":function(){return this.parent.w/2-this.w/2}},F.prop=function(e,t,n){if("number"==typeof n)e[t]=n;else if("string"==typeof n&&null!=F.propFns[n])Object.defineProperties(e,f({},t,{get:F.propFns[n],set:m.noop}));else if("string"==typeof n&&n.endsWith("%")){var r=parseFloat(n)/100,i=null!=j[t]?j[t]:t;Object.defineProperties(e,f({},t,{get:function(){return this.parent[i]?e.parent[i]*r:0},set:m.noop}))}else if(m.isFunction(n)){var o;if("w"===t||"h"===t){var a=n.bind(e),l=t.toUpperCase(),s=e["min".concat(l)],c=e["max".concat(l)];o=function(){var e=a();return e<s?e=s:e>c&&(e=c),e}.bind(e)}else o=n;Object.defineProperties(e,f({},t,{get:o,set:m.noop}))}else e[t]=n;e[t]=n};var V={},U={},W={},Z=function(e){var t,n,r=u(e);return"string"===r?t=e:"object"===r?n=e[t=Object.keys(e)[0]]:console.warn("invalid view definition. ".concat(e," is a ").concat(u(e))),{viewName:t,children:n}},z={nameSpaces:{},nsStack:[],getNamespace:function(e){return this.nameSpaces[e]},hasNamespace:function(e){return null!=this.nameSpaces[e]},setModel:function(e){return this.model=e,this},resetStates:function(e){var t=this.getNamespace(e);m.forEach(t,(function(e){return e&&e.setEnabled&&e.setEnabled(!0)}))},updateVisibility:function(e,t){var n=!1;for(var r in e){var i=this.nameSpaces[t][r];if(null!=i)i.setVisibility(e[r],!0)&&(n=!0)}return n},def:function(e,t,n){null==n?t.nameKey=e:n.nameKey=e;var r=new F(t,n);return null==V[e]?(V[e]=r,U[e]={ViewLogic:t,p:n},W[e]=0):console.warn("views connot share the same name ".concat(e)),r},addNameSpace:function(e){b=e,this.nameSpaces[b]=this.nameSpaces[b]||{name:e,topLevelElements:[],isAttached:!0,tabReachable:!0,detach:function(){this.isAttached=!1,this.topLevelElements.forEach((function(e){return I.removeChild(e.el)}))},reattach:function(){this.isAttached=!0,this.topLevelElements.forEach((function(e){return I.appendChild(e.el)}))},updateTabIndex:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=function(t){if(!t.dataset.leavealone){var n=t.getAttribute("data-tabindex");null==n&&(n=t.tabIndex,t.setAttribute("data-tabindex",n)),t.tabIndex=e.tabReachable?n:-1}};this.topLevelElements.forEach((function(i){var o=function(e,t){t?e.el.removeAttribute("aria-hidden"):e.el.setAttribute("aria-hidden",!0)};t&&(i.children.some((function(e){return"startoverlay"===e.nameKey}))?i.children.filter((function(e){return"startoverlay"!==e.nameKey})).forEach((function(t){return o(t,e.tabReachable)})):o(i,e.tabReachable)),i.el.querySelectorAll(n?"[tabIndex]:not(.acc-shadow-el):not(.slide-object)":"[tabIndex]").forEach((function(e){return r(e)})),"lightBoxClose"===i.nameKey&&r(i.el)}))}}},update:function(e){function t(t,n){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){e.forEach((function(e){m.isFunction(e)?e():Array.isArray(e)?update(e,t):null==e||!e.update||e.noUpdate&&!t||(e.update(),t&&e.updateHtml())}))})),getViewConfig:function(e){var t=U[e];return{name:e,ViewLogic:t.ViewLogic,p:t.p}},getOrCreateView:function(e){if(++W[e]>1){var t=U[e],n=t.ViewLogic,r=t.p;return new F(n,r)}return V[e]},tree:function(e,t){for(var n=this,r=[],i=function t(r){if(null!==r){var i=Z(r),o=i.viewName,a=i.children,l=n.nameSpaces[e][o]=n.getOrCreateView(o);if(null!=l){if(l.nameSpace=e,C(a,"length")>0){l.setChildNum(a.length),l.hasChildren=!0;for(var s=0;s<a.length;s++)t(a[s])}}else console.warn("could not find view '".concat(o,"'"))}},o=function t(i,o){if(null!==i){var a=Z(i),l=a.viewName,s=a.children,c=n.nameSpaces[e][l];if(null!=c){if(c.init(e),null!=o?o.append(c):n.nameSpaces[e].topLevelElements.push(c),null!=c.childDef&&c.childDef(),r.push(c),c.hasChildren)for(var u=0;u<s.length;u++)t(s[u],c)}else console.warn("could not find view '".concat(l,"'"))}},a=0;a<t.length;a++)i(t[a]);for(var l=0;l<t.length;l++)o(t[l]);return r},getCurrentNameSpace:function(){return this.nameSpaces[b]},getTopNameSpace:function(){return m.last(this.nsStack)},getCurrentNameSpaceString:function(){return b},getFrameNameSpace:function(){return this.nameSpaces[P]},getBlocker:function(e){switch(e){case DS.constants.refs.FRAME:return z.getNamespace(e).frameBlocker;case"LightboxWnd":return z.getNamespace(e).lightBoxBlocker;case"LightboxControlsWnd":return z.getNamespace(e).lightBoxControlsBlocker;default:return null}}};function K(e){return function(e){if(Array.isArray(e))return e}(e)||q(e)||Q(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(e){return function(e){if(Array.isArray(e))return Y(e)}(e)||q(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(e,t):void 0}}function q(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var X,$=DS,J=$._,ee=$.pubSub,te=$.events,ne={},re=!1,ie={},oe=function(e){try{var t=G(document.styleSheets).find(e);X=G(t.rules)}catch(e){}};if(oe((function(e){return null!=e.href&&e.href.includes("output.min.css")})),ee.on(te.scheme.CHANGED,(function(e){oe((function(t){return null!=t.ownerNode&&t.ownerNode.id===e})),ie={}})),null!=X){var ae=function(e,t){var n,r;if(re){n="".concat(t,".cs-").concat(z.model.frame.default_layout);var i=e.split(" ");r=(i=i.map((function(e){return(e.startsWith(".")?".":"")+J.compact(e.split(".")).reverse().join(".")}))).join(" ")}else n=".cs-".concat(z.model.frame.default_layout).concat(t),r=e;return"".concat(n," ").concat(r)};ne.getColor=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=ae(t,r);if(null==ie[i]){var o=X.find((function(e){return e.selectorText===i}));null==o&&(re=!re,i=ae(t,r),o=X.find((function(e){return e.selectorText===i}))||{}),ie[i]=o.style||{}}return ie[i][n]||""}}else{var le=function(e,t){var n=document.createElement(e);return n.setAttribute("class",t),n},se=function(e){return le("div",e)},ce=/^\./,ue=/\./g;ne.getColor=function(e,t,n){var r,i,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=(o||t).split(/\s+/),l=z.getNamespace(e).wrapper.el;a.forEach((function(e,t){if(ce.test(e))r=se(e.replace(ue," "));else{var n=K(e.split(".")),o=n[0],a=n.slice(1);r=le(o,a.join(" "))}0===t&&(r.style.position="absolute",r.style.display="none",i=r),l.appendChild(r),l=r}));var s=window.getComputedStyle(r).getPropertyValue(n);return""===s&&"border-color"===n&&(s=window.getComputedStyle(r).getPropertyValue("border-top-color")),i.parentNode.removeChild(i),s||""}}var fe=ne;function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function he(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==de(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==de(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===de(o)?o:String(o)),r)}var i,o}var pe,ye,be,ve=DS,me=ve.dom,ge=ve._,we=ve.detection,Se=ve.pubSub,ke=ve.events,Ce=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ge.bindAll(this,"onLoaderShow","onLoaderMute","onLoaderUnmute","onLoaderHide","onRemoveLoaderTitle");var t={};t[ke.loader.SHOW]=this.onLoaderShow,t[ke.loader.MUTE]=this.onLoaderMute,t[ke.loader.UNMUTE]=this.onLoaderUnmute,t[ke.loader.HIDE]=this.onLoaderHide,t[ke.loader.REMOVE_TITLE]=this.onRemoveLoaderTitle,t[ke.startOverlay.READY]=this.onLoaderHide,me.addClass(document.body,"theme-".concat(window.globals.themeName)),document.body.classList.contains("view-tablet")&&me.addClass(document.body,"is-touchable-tablet"),we.env.is360&&me.addClass(document.body,"is-360"),Se.on(t),this.setupBrandingColor()}var t,n,r;return t=e,(n=[{key:"setupBrandingColor",value:function(){window.requestAnimationFrame((function(){var e=fe.getColor(DS.constants.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");null!=e&&(DS.constants.theme.brandingHighlight=e,Se.trigger(ke.app.BRANDING_COLOR,e))}))}},{key:"onRemoveLoaderTitle",value:function(){var e=document.querySelector("body > .mobile-load-title-overlay");null!=e&&e.parentNode.removeChild(e)}},{key:"getSpinLoader",value:function(){return document.querySelector("body > .slide-loader")}},{key:"onLoaderMute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=0)}},{key:"showLoaderDelayed",value:function(e){clearTimeout(this.loaderTimeout),this.loaderTimeout=setTimeout(this.onLoaderShow,e)}},{key:"onLoaderHide",value:function(){clearTimeout(this.loaderTimeout),this.getSpinLoader().style.display="none",me.addClass(document.getElementById("preso"),"hide-slide-loader"),Se.trigger(ke.app.HIDE_LOADER)}},{key:"onLoaderUnmute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=1)}},{key:"onLoaderShow",value:function(e){e>0?this.showLoaderDelayed(e):(this.getSpinLoader().style.display="block",me.removeClass(document.getElementById("preso"),"hide-slide-loader"),Se.trigger(ke.app.SHOW_LOADER))}}])&&he(t.prototype,n),r&&he(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Oe=DS,Ee=Oe.globalEventHelper.addWindowListener,Le=Oe.events,Te=Le.ds,xe=Te.FRAME_DATA_LOADED,Pe=Te.PRESO_READY,Ie=Le.window.STACKING_CHANGED,_e=Le.frame,je=_e.MODEL_READY,Ae=_e.SCALE,De=Le.resume.SET_DATA,Be=Le.startOverlay.READY,Re=Le.controlOptions.RESET,Me=Le.sidebar.ACTIVE_TAB_SET,Ne=Le.renderTree.DESTROYED,He=Oe.constants,Fe=(He.els.PRESO,He.refs.FRAME),Ve=Oe.detection.theme,Ue=Oe.pubSub,We=Oe.focusManager,Ze=Oe.flagManager,ze=Oe.playerGlobals,Ke=Oe.stringTabler,Ge=Oe.shortcutManager,Qe=Oe.dom,qe={},Ye=function(e){var t=z.nsStack.indexOf(e);t>=0&&z.nsStack.splice(t,1)},Xe=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];z.nsStack.forEach((function(t,n,r){t.tabReachable=n===r.length-1&&e,t.updateTabIndex()}))},$e=function(e){pe={createWindow:function(t){var n,r,i=this,o=z.getNamespace(t),a=function(){return function(e){var t=z.getNamespace(e),n=t.isAttached;t.reattach(),Ye(t),z.nsStack.push(t),n||qe[e].all();var r=z.nsStack.length;z.nsStack.forEach((function(e,t){var n=e.slide.el;t===r-1?(Qe.addClass(n,"primary-slide"),Qe.removeClass(n,"secondary-slide")):(Qe.removeClass(n,"primary-slide"),Qe.addClass(n,"secondary-slide"))})),Xe(),Ue.trigger(Ie,e)}(t)},l=function(e,n,r){if(e===t){var i=r?r.getWndBlockerBackground():"",a=z.getBlocker(e);null!=a&&(a.setBackgroundColor(i),a.setVisibility(n)),o.tabReachable=!n,o.updateTabIndex(!1,!0)}};return null==o&&(z.addNameSpace(t),r=e[t](ye),n=Ee("resize",(function(){r.resize(),Ue.trigger(Ae)})),t===Fe?ye.setLayout(ye.frame.default_layout,Fe):r.all(),o=z.getNamespace(t),qe[t]=r,o.moveToTop=a,Ve.isUnified&&(Ue.on(DS.events.frameModel.BLOCKED_CHANGED,l),Ue.on(DS.events.window.MAIN_CHANGED,(function(e,t){return l(t,!1)})))),a(),Ue.on(Re,(function e(){var t=function(){var e=[],t=z.getNamespace(Fe).tabs.viewLogic.getSelectedTab();return t&&e.push(t.nameKey),_.each(z.getNamespace(Fe).topTabs.children,(function(t){_.each(_.filter(t.children,"viewLogic.showing",!0),(function(t){return e.push(t.nameKey)}))})),e}();Ye(z.getNamespace(Fe)),r.destroy(),DS.pubSub.trigger(Ne),z.nameSpaces[Fe]=null,n(),Ue.off(Re,e),i.createWindow("_frame"),r.rerender(),t.forEach((function(e){Ue.trigger(Me,e)}))})),{id:t,el:o.slide.el,wndEl:o.wrapper.el,captionEl:(o.captionContainer||{}).el,x:function(){return o.wrapper.x},y:function(){return o.wrapper.y},close:function(){o.zoomBounds=null,o.detach(),Ye(o),z.nsStack[z.nsStack.length-1].moveToTop(),We.reCenter()},moveToTop:a,getWinScale:function(){return o.slide.winScale||1},getPinchZoomBounds:function(){return o.slide.pinchZoomBounds},onPinchZoom:qe[t].pinchZoom?function(e){o.zoomBounds=e,qe[t].pinchZoom()}:function(){},onWndBlockedChanged:l}},getSidebarPosition:function(){return ye.sidebarOpts.sidebarPos},getDefaultLayout:function(){return be.default_layout},getFonts:function(){return be.fonts},getFontScale:function(){return ye.frame.fontscale},getCaptionData:function(){var e=ye.frame.controlOptions.controls;return{font:e.font,enabled:e.closed_captions}},getNavData:function(){return ye.frame.navData.outline.links},isReadOnlyOnce:function(){return be.controlOptions.controls.readonlyOnce},topmostUnreachable:function(){return z.nsStack},setAllAccVisibility:function(e){_.forEach(z.nameSpaces,(function(t){var n=t.wrapper;e?n.el.removeAttribute("aria-hidden"):n.el.setAttribute("aria-hidden",!0)}))}},Ue.on(De,Xe),Ue.on(Be,(function(){Xe(!1)})),ze.player=pe};function Je(e){return Je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Je(e)}function et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Je(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Je(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Je(o)?o:String(o)),r)}var i,o}var tt=DS.detection,nt=new(function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.minSize=Math.min(window.innerWidth,window.innerHeight),this.isMobileChrome=tt.os.isIOS&&tt.browser.isChrome}var t,n,r;return t=e,(n=[{key:"shouldUseCache",value:function(){return this.isMobileChrome&&document.activeElement.classList.contains("acc-textinput")}},{key:"height",get:function(){return this.shouldUseCache()?(this.cachedOuterHeight!==window.outerHeight&&(this.cachedHeight=window.outerHeight-(this.cachedOuterHeight-this.cachedHeight),this.cachedOuterHeight=window.outerHeight),this.cachedHeight):(this.cachedOuterHeight=window.outerHeight,this.cachedHeight=window.innerHeight,window.innerHeight)}},{key:"width",get:function(){return window.innerWidth}}])&&et(t.prototype,n),r&&et(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()),rt="wrapper";function it(e){return it="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},it(e)}function ot(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==it(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==it(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===it(o)?o:String(o)),r)}var i,o}z.def(rt,(function(){return{attrs:{id:rt,class:rt},x:0,y:0,w:function(){return nt.width},h:function(){return nt.height},add:!0}}));var at=DS,lt=at.pubSub,st=at.events,ct="click",ut=function(){function e(t){for(var n in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t.el,this.view=t,this.model=t.model,t.children)this[n+"El"]=t.children[n].el;DS._.bindAll(this,"onFocus","onBlur","onLayoutChange"),this.el.addEventListener("focusin",this.onFocus),this.el.addEventListener("focusout",this.onBlur),lt.on(st.frameModel.LAYOUT_CHANGED,this.onLayoutChange)}var t,n,r;return t=e,(n=[{key:"onClick",value:function(e){this.el.addEventListener(ct,e.bind(this))}},{key:"onClickEl",value:function(e,t){e.addEventListener(ct,t.bind(this))}},{key:"on",value:function(e,t){this.el.addEventListener(e,t.bind(this))}},{key:"getViewBox",value:function(){return this.view.getBox()}},{key:"onFocus",value:function(){var e=this.getViewBox(),t=e.x,n=e.y,r=e.w,i=e.h;DS.focusManager.setFocusRectOn(this.el,{left:t,top:n,width:r,height:i}),this.hasFocus=!0}},{key:"onBlur",value:function(){DS.focusManager.takeFocusOff(),this.hasFocus=!1}},{key:"teardown",value:function(){lt.off(st.frameModel.LAYOUT_CHANGED,this.onLayoutChange)}},{key:"onLayoutChange",value:function(){}}])&&ot(t.prototype,n),r&&ot(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function ft(e){return ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ft(e)}function dt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ft(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ft(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ft(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ht=DS,pt=ht._,yt=ht.pubSub,bt=ht.events,vt=ht.detection,mt=ht.constants.MOBILE_UI_SIZE,gt="top ".concat(150,"ms ease-in-out");function wt(e){return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wt(e)}function St(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==wt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==wt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===wt(o)?o:String(o)),r)}var i,o}function kt(e,t){return kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},kt(e,t)}function Ct(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Lt(e);if(t){var i=Lt(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Ot(this,n)}}function Ot(e,t){if(t&&("object"===wt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Et(e)}function Et(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lt(e){return Lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Lt(e)}var Tt=DS,xt=Tt.pubSub,Pt=Tt.events,It=Tt._,_t=Tt.utils,jt=(Tt.flagManager,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kt(e,t)}(o,e);var t,n,r,i=Ct(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),It.bindAll(Et(t),"onSlideChange","onAriaToggle"),xt.on(Pt.slide.HAS_MOUNTED,t.onSlideChange),xt.on(Pt.slide.ARIA_TOGGLE,t.onAriaToggle),t.teardownPushableSlide=function(e){var t;if(!vt.deviceView.isPhone)return pt.noop;function n(){e.view.el.style.transition=null,e.view.el.style.top="0px"}function r(t){e.view.el.style.transition=gt;var n=.45*window.innerHeight-10,r=-(e.view.bottom()-n),i=e.view.y+r,o=0;i<mt&&vt.theme.isUnified?o=mt-i+10:i<0&&(o=-1*i+10),e.view.el.style.top="".concat(r+o,"px")}function i(){e.view.el.style.transition=gt,e.view.el.style.top="".concat(0,"px")}return yt.on((dt(t={},bt.threeSixtyImage.UN_PUSH_LABEL,n),dt(t,bt.threeSixtyImage.PUSH_UP_BY_LABEL,r),dt(t,bt.threeSixtyImage.PUSH_DOWN_BY_LABEL,i),t)),function(){n(),yt.off(bt.threeSixtyImage.UN_PUSH_LABEL,n),yt.off(bt.threeSixtyImage.PUSH_UP_BY_LABEL,r),yt.off(bt.threeSixtyImage.PUSH_DOWN_BY_LABEL,i)}}(Et(t)),t}return t=o,(n=[{key:"onAriaToggle",value:function(e){e.hidden?(this.el.setAttribute("aria-hidden",!0),this.el.setAttribute("tabindex",0)):(this.el.removeAttribute("aria-hidden"),this.el.removeAttribute("tabindex"))}},{key:"teardown",value:function(){xt.off(Pt.slide.HAS_MOUNTED,this.onSlideChange),this.teardownPushableSlide()}},{key:"onSlideChange",value:function(e){null!=this.labelEl&&this.view.nameSpace===e.props.windowId&&(this.labelEl.textContent="".concat(z.model.getString("slide"),": ").concat(_t.stripTags(e.props.model.title())))}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(){}}])&&St(t.prototype,n),r&&St(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut)),At=DS,Dt=At.constants.MOBILE_UI_SIZE,Bt=At.detection.orientation,Rt="slide";z.def(Rt,jt,(function(e){var t=z.model;z.getNamespace(e).optionPane;return{attrs:{id:Rt,class:Rt},methods:{getAvailableWidth:function(){return nt.width-(Bt.isLandscape?Dt:0)},getAvailableHeight:function(){return nt.height-(Bt.isLandscape?0:Dt)}},winScale:function(){var e=t.slideWidth,n=t.slideHeight;return Math.min(this.getAvailableWidth()/e,this.getAvailableHeight()/n)},origin:"0 0",w:function(){return t.slideWidth*this.winScale},h:function(){return t.slideHeight*this.winScale},x:function(){return(this.getAvailableWidth()-this.w)/2},y:function(){return(this.getAvailableHeight()-this.h)/2},z:1,overflow:"visible",add:!0,html:'<div id="slide-label" data-ref="label" aria-live="polite"></div>\n      <main class="slide-container" data-ref="container" aria-live="off" tabindex="-1"></main>'}}));var Mt="frame";function Nt(e){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nt(e)}function Ht(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Nt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Nt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Nt(o)?o:String(o)),r)}var i,o}function Ft(e,t){return Ft=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ft(e,t)}function Vt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Zt(e);if(t){var i=Zt(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Ut(this,n)}}function Ut(e,t){if(t&&("object"===Nt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Wt(e)}function Wt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zt(e){return Zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Zt(e)}z.def(Mt,(function(){return{attrs:{id:Mt},x:0,y:0,w:"100%",h:"100%",z:2}}));var zt=DS,Kt=zt.detection,Gt=zt._,Qt=zt.pubSub,qt=zt.events,Yt=zt.globalEventHelper.addWindowListener,Xt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ft(e,t)}(o,e);var t,n,r,i=Vt(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Gt.bindAll(Wt(t),"onLayoutReady"),Kt.deviceView.isTablet&&Yt("resize",(function(){window.requestAnimationFrame((function(){t.view.update()}))})),Qt.on(qt.frame.LAYOUT_READY,t.onLayoutReady),t}return t=o,(n=[{key:"onLayoutReady",value:function(e){this.view.layout=e}},{key:"teardown",value:function(){Qt.off(qt.frame.LAYOUT_READY,this.onLayoutReady)}}])&&Ht(t.prototype,n),r&&Ht(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),$t=DS,Jt=$t.constants.MOBILE_UI_SIZE,en=$t.detection,tn=en.deviceView.isPhone,nn=en.orientation,rn=$t.dom,on="optionPane";z.def(on,Xt,(function(e){z.model;var t=z.getNamespace(e),n=t.fullScreenOpen,r=t.captions,i=t.settings,o=t.seek,a=t.playPause,l=t.submit,s=t.next,c=t.prev;return{tag:"section",attrs:{id:on,class:_.kebabCase(on)},wl:function(){return Jt},wp:function(){return window.innerWidth},hl:function(){return window.innerHeight},hp:function(){return Jt},xl:function(){return window.innerWidth-Jt},xp:0,yl:0,yp:function(){return window.innerHeight-Jt},z:4,childVisibilityChangedHook:function(){this.update()},methods:{moveButtonsToOverflow:function(){var e=[n,r,i,a,l,s,c].filter((function(e){return e&&e.visible}));this.applyOverflowVisibility(e,[r])},applyOverflowVisibility:function(e,t){for(this.ctrlOverflow=[];e.length>5&&t.length>0;){var n=t.pop(),r=e.indexOf(n);r>=0&&(e.splice(r,1),n.setVisibility(!1),this.ctrlOverflow.push(n),rn.addClass(this.el,"".concat(n.nameKey,"-overflow")))}return e},revertOverflowVisibility:function(){var e=this;null!=this.ctrlOverflow&&this.ctrlOverflow.forEach((function(t){t.setVisibility(t.layoutDefaultVisible),rn.removeClass(e.el,"".concat(t.nameKey,"-overflow"))}))}},updateHook:function(){if(this.hasAllChildren())if(tn&&(this.revertOverflowVisibility(),this.moveButtonsToOverflow()),nn.isPortrait){this.layout.seek&&o.setVisibility(!0),o.width=0;var e=this.calcChildrensWidth();o.width=this.w-e,this.flowChildren({sizeToChildren:!0,reverse:!1})}else o.setVisibility(!1),this.flowChildren({vertical:!0,sizeToChildren:!0,reverse:!1})},overflow:"visible"}}));var an=function(){return"no icon"},ln={hamburger:function(){return'<svg class="cs-icon hamburger-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <g id="icon-menu-mobile">\n    <rect width="40" height="4"></rect>\n    <rect y="16" width="40" height="4"></rect>\n    <rect y="32" width="40" height="4"></rect>\n    </g>\n  </svg>'},play:function(){return'<svg class="cs-icon play-icon" width="13" height="15" viewBox="0 0 13 15" focusable="false">\n    <path id="icon-play" d="M 13 7.15 L 2 0 2 14.3 13 7.15 Z"/>\n  </svg>'},next:function(){return'<svg className="cs-icon pause-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <path transform="translate(0, -5)" d="M39.414,16.587L22.826,0L20,2.826L33.175,16H0v4h33.175L20,33.174L22.826,36l16.588-16.587C40.195,18.633,40.195,17.368,39.414,16.587z"/>\n  </svg>'},prev:function(){return'<svg class="cs-icon prev-mobile-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <path transform="translate(0, -5)" d="M0.586,19.413L17.174,36L20,33.174L6.825,20H40v-4H6.825L20,2.826L17.174,0L0.586,16.587C-0.195,17.367-0.195,18.632,0.586,19.413z"/>\n  </svg>'},pause:function(){return'<svg class="cs-icon pause-icon" width="12" height="14" viewBox="0 0 12 14" focusable="false">\n    <g id="icon-pause">\n      <rect x="0" width="4" height="14"/>\n      <rect x="8" width="4" height="14"/>\n    </g>\n  </svg>'},submit:function(){return'<svg class="cs-icon submit-icon" width="40" height="30" viewBox="0 0 40 30" focusable="false">\n    <path transform="translate(0, -5)" d="M12.685,30c-0.538,0-1.053-0.218-1.429-0.604L0,17.832l2.857-2.8l9.827,10.097L37.143,0L40,2.8L14.114,29.396C13.738,29.782,13.222,30,12.685,30z"/>\n  </svg>'},close:function(){return'\n    <svg class="cs-icon icon-close" width="36" height="36" viewBox="0 0 36 36" focusable="false">\n      <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n    </svg>'},captions:function(e){return'<svg class="cs-icon caption-icon" width="22px" height="20px" viewBox="0 0 22 20" focusable="false">\n    <g stroke="none" stroke-width="1" fill-rule="evenodd" focusable="false">\n      <g>\n        <path d="M14.8517422,14 L20.008845,14 C21.1103261,14 22,13.1019465 22,11.9941413 L22,2.00585866 C22,0.897060126 21.1085295,0 20.008845,0 L1.991155,0 C0.889673948,0 0,0.898053512 0,2.00585866 L0,11.9941413 C0,13.1029399 0.891470458,14 1.991155,14 L8.09084766,14 L11.4712949,17.3804472 L14.8517422,14 Z M3,4 L13,4 L13,6 L3,6 L3,4 Z M14,4 L19,4 L19,6 L14,6 L14,4 Z M19,8 L8,8 L8,10 L19,10 L19,8 Z M7,8 L3,8 L3,10 L7,10 L7,8 Z"></path>\n      </g>\n    </g>\n  </svg>'},disableOrientation:function(){return'<svg viewBox="0 0 161 135">\n    <g stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd">\n      <path d="M59,31.9948589 C59,30.340844 60.3408574,29 62.0069809,29 L99.9930191,29 C101.653729,29 103,30.3364792 103,31.9948589 L103,103.005141 C103,104.659156 101.659143,106 99.9930191,106 L62.0069809,106 C60.3462712,106 59,104.663521 59,103.005141 L59,31.9948589 Z M61,36 L101,36 L101,96 L61,96 L61,36 Z M81,104 C82.6568542,104 84,102.656854 84,101 C84,99.3431458 82.6568542,98 81,98 C79.3431458,98 78,99.3431458 78,101 C78,102.656854 79.3431458,104 81,104 Z M76,32.5 C76,32.2238576 76.2276528,32 76.5096495,32 L85.4903505,32 C85.7718221,32 86,32.2319336 86,32.5 C86,32.7761424 85.7723472,33 85.4903505,33 L76.5096495,33 C76.2281779,33 76,32.7680664 76,32.5 Z"></path>\n      <path d="M144.276039,68.4976037 C143.65768,83.6270348 137.530567,98.6224671 125.961909,110.191125 C101.576936,134.576098 62.1020027,134.704192 37.8006658,110.402855 L37.8275751,110.429765 L33.4090737,114.848266 L33.3821643,114.821357 C60.1400795,141.579272 103.595566,141.480117 130.445572,114.630111 C143.247134,101.828549 149.95913,85.2399018 150.581333,68.4976037 L161.373625,68.4976037 L147.23149,54.3554681 L133.089354,68.4976037 L144.276049,68.4976037 Z"></path>\n      <path d="M17.2900541,66.5559885 C17.8833587,51.3895735 24.012088,36.3498513 35.6085461,24.7533932 C59.9935191,0.36842015 99.4684528,0.240325436 123.76979,24.5416624 L123.74288,24.514753 L128.161382,20.0962516 L128.188291,20.1231609 C101.430376,-6.63475424 57.9748898,-6.5355989 31.1248839,20.314407 C18.2955218,33.1437691 11.582203,49.7766814 10.9851551,66.5559885 L0.259994507,66.5559885 L14.4021301,80.6981242 L28.5442658,66.5559885 L17.2900541,66.5559885 Z"></path>\n    </g>\n  </svg>'},search:function(){return'<svg class="cs-icon search-icon" width="22" height="22" viewBox="0 0 40 40" focusable="false">\n    <g id="icon-search" transform="translate(4, 0)">\n      <path d="M14.1378906,27.4473684 C21.6653507,27.4473684 27.7757813,21.4196023 27.7757813,13.9736842 C27.7757813,6.52776609 21.6653507,0.5 14.1378906,0.5 C6.61043053,0.5 0.5,6.52776609 0.5,13.9736842 C0.5,21.4196023 6.61043053,27.4473684 14.1378906,27.4473684 L14.1378906,27.4473684 Z M14.1378906,24.4473684 C8.25820695,24.4473684 3.5,19.7535325 3.5,13.9736842 C3.5,8.19383595 8.25820695,3.5 14.1378906,3.5 C20.0175743,3.5 24.7757813,8.19383595 24.7757813,13.9736842 C24.7757813,19.7535325 20.0175743,24.4473684 14.1378906,24.4473684 L14.1378906,24.4473684 Z" ></path>\n      <path d="M20.887408,24.4494377 L31.4348235,34.8541634 L32.5026823,35.9075758 L34.609507,33.7718582 L33.5416482,32.7184459 L22.9942327,22.3137202 L21.9263739,21.2603078 L19.8195492,23.3960254 L20.887408,24.4494377 L20.887408,24.4494377 Z" ></path>\n    </g>\n  </svg>'},enterFullScreen:function(){return'<svg class="cs-icon enter-fullscreen-icon" width="25" height="25" viewBox="0 0 32 32" focusable="false">\n    <g>\n      <path d="M0,0 L12,0 L12,3 L3,3 L3,12 L0,12 L0,0 Z"/>  \n      <path d="M20,0 L32,0 L32,12, L29,12, L29,3, L20,3, L20,0 Z"/>\n      <path d="M0,20 L3,20 L3,29, L12,29, L12,32, L0,32, L0,20 Z"/>\n      <path d="M29,20 L32,20 L32,32, L20,32, L20,29, L29,29, L29,20 Z"/>\n    </g>\n  </svg>'},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},track:function(e){return'\n      <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n        <defs>\n            <rect id="'.concat(e,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n            <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(e,'-trackFilter">\n                <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n                <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n                <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n            </filter>\n        </defs>\n        <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#303030" fill-rule="evenodd" xlink:href="#').concat(e,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(e,'-trackFilter)" xlink:href="#').concat(e,'-track"></use>\n                <use stroke="#595959" stroke-width="1" xlink:href="#').concat(e,'-track"></use>\n                <circle fill="#B4B4B4" stroke-width="0" cx="8" cy="8" r="5"></circle>\n            </g>\n        </g>\n        <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#505050" fill-rule="evenodd" xlink:href="#').concat(e,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(e,'-trackFilter)" xlink:href="#').concat(e,'-track"></use>\n                <use stroke="#595959" stroke-width="1" xlink:href="#').concat(e,'-track"></use>\n                <circle fill="#6EBBEF" stroke-width="0" cx="16" cy="8" r="6"></circle>\n            </g>\n        </g>        \n    </svg>\n    ')},downArrow:function(){return'\n      <svg width="22px" height="11px" viewBox="0 0 22 11" focusable="false">\n        <path d="M 0 0 L 11 11 22 0" fill="#191c1d"/>\n      </svg>\n    '}},sn=function(e){return ln[e]||an},cn=function(e){var t=z.model.frame.controlOptions.controls.search;return'\n    <button\n      class="hamburger"\n      aria-expanded="false"\n      data-ref="burger"\n      aria-label="'.concat(z.model.getString("sidebar_toggle"),'">\n        ').concat(sn("hamburger")(),"\n    </button>\n    ").concat(t?function(e){return'\n    <button\n      class="search-toggle-btn search-toggle-'.concat(e,'"\n      aria-pressed="false"\n      data-ref="searchBtn"\n      aria-label="').concat(z.model.getString("search_toggle"),'">\n        ').concat(sn("search")(),"\n    </button>")}(e):"","\n  ")},un=DS.constants.MOBILE_UI_SIZE,fn="hamburger";function dn(e){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dn(e)}function hn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==dn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==dn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===dn(o)?o:String(o)),r)}var i,o}z.def(fn,(function(){z.model;return{attrs:{id:fn},x:0,y:0,w:un,h:un,overflow:"visible",updateHook:function(){var e,t,n,r,i,o;this.setEnabled((e=z.model,t=e.topTabsLeft,n=e.topTabsRight,r=e.sidebarTabs,i=e.currControlLayout,o=function(e){return e.some((function(e){var t=e.name,n=i[t];return"outline"===t?n.enabled:"customlink"===t||n}))},o(t)||o(n)||o(r)))},html:cn("portrait")}}));var pn=DS,yn=pn._,bn=pn.pubSub,vn=pn.events,mn=pn.dom.toggleClasses,gn=(pn.shortcutManager,function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.view=t,yn.bindAll(this,"onSlideChanged","onTimelineChanged","onPlaybackChanged"),bn.on(vn.slide.STARTED,this.onSlideChanged)}var t,n,r;return t=e,(n=[{key:"teardown",value:function(){bn.off(vn.slide.STARTED,this.onSlideChanged)}},{key:"onTimelineChanged",value:function(e,t){e!==this.currTimeline&&(bn.trigger(vn.playbackControls.TIMELINE_CHANGED,e,t),null!=this.currTimeline&&(this.currTimeline.off(vn.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.off(vn.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.off(vn.timeline.ENDED,this.onPlaybackChanged)),this.currTimeline=e,this.currTimeline.on(vn.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.on(vn.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.on(vn.timeline.ENDED,this.onPlaybackChanged),this.onPlaybackChanged())}},{key:"onPlaybackChanged",value:function(){var e=this.view.children,t=e.reset,n=e.playPause,r=null!=this.currTimeline&&"playing"===this.currTimeline.playbackState();mn(document.body,"timeline-playing","timeline-paused",r),mn(document.body,"has-reset","no-reset",this.view.visible&&null!=t&&t.visible),null!=n&&n.viewLogic.onPlaybackChanged()}},{key:"onSlideChanged",value:function(e,t,n){this.view.el.tabIndex=0,this.view.el.tabIndex=-1,this.view.nameSpace===n&&(null!=this.currSlide&&this.currSlide.off(vn.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.currSlide=t,this.currSlide.on(vn.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.onTimelineChanged(t.currentTimeline(),t))}}])&&hn(t.prototype,n),r&&hn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()),wn=DS,Sn=wn.constants.MOBILE_UI_SIZE,kn=wn.detection.orientation,Cn="playbackControls";function On(e){return On="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},On(e)}function En(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,jn(r.key),r)}}function Ln(e,t){return Ln=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ln(e,t)}function Tn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=In(e);if(t){var i=In(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return xn(this,n)}}function xn(e,t){if(t&&("object"===On(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Pn(e)}function Pn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function In(e){return In=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},In(e)}function _n(e,t,n){return(t=jn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jn(e){var t=function(e,t){if("object"!==On(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==On(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===On(t)?t:String(t)}z.def(Cn,gn,(function(e){var t=z.getNamespace(e),n=t.hamburger,r=t.seek,i=z.model,o=(i.rtl,i.currControlLayout);o.pauseplay,o.seekbar;return{attrs:{id:Cn,"aria-label":"playback controls"},visibility:"no-reflow",overflow:"visible",x:0,yp:0,yl:function(){return null!=n?Sn:0},wp:function(){return this.width+(r.visible?0:r.w)||0},wl:"100%",hp:"100%",hl:function(){return this.height||0},noUpdate:!0,updateHook:function(){this.flowChildren({fullUpdate:!0,reverse:!1,sizeToChildren:!0,startPos:0,vertical:kn.isLandscape});var e=this.children.some((function(e){return e.visible}));this.setVisibility(e)}}}));var An=DS,Dn=An._,Bn=An.events,Rn=An.pubSub,Mn=An.detection,Nn=An.dom.tappedClass,Hn=An.keyManager,Fn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ln(e,t)}(o,e);var t,n,r,i=Tn(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),_n(Pn(t=i.call(this,e)),"onTap",(function(e){Nn(t.el)})),_n(Pn(t),"onClickBtn",(function(e){1!==t.currTimeline.progress()?t.currTimeline.togglePlayback():Mn.theme.isUnified&&t.currTimeline.reset()})),_n(Pn(t),"onKeydown",(function(e){var t=e.which;Hn.isSeekKey(t)&&Rn.trigger(Bn.player.SEEK,e)})),_n(Pn(t),"onPlaybackChanged",(function(){t.updateToggle(),t.updateView()})),Dn.bindAll(Pn(t),"onTimelineChanged"),Rn.on(Bn.player.TOGGLE_PLAYBACK,t.onClickBtn),Rn.on(Bn.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),Mn.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t.el.addEventListener("keydown",t.onKeydown),t.view.toggle=!0,Mn.deviceView.isMobile&&Rn.on(Bn.currTimeline.TICK,t.onTick.bind(Pn(t))),t}return t=o,(n=[{key:"teardown",value:function(){Rn.off(Bn.currTimeline.TICK,this.onTick.bind(this))}},{key:"onTick",value:function(e){null!=this.circleProgress&&this.circleProgress.setAttribute("d",DS.svgUtils.wheelPath(17,17,17,0,360*e)),this.lastProgress=e}},{key:"updateView",value:function(){this.view.updateHtml(),this.circleProgress=this.el.querySelector(".circle-progress path"),this.onTick(this.lastProgress)}},{key:"updateToggle",value:function(){this.view.toggle="playing"===this.currTimeline.playbackState(),this.view.el.setAttribute("aria-pressed",!this.view.toggle)}},{key:"onTimelineChanged",value:function(e){this.currTimeline=e,this.view.toggle=this.currTimeline.isPlaying(),this.updateView()}}])&&En(t.prototype,n),r&&En(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Vn=DS.constants.MOBILE_UI_SIZE,Un="playPause";function Wn(e){return Wn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wn(e)}function Zn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Wn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Wn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Wn(o)?o:String(o)),r)}var i,o}function zn(e,t){return zn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zn(e,t)}function Kn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qn(e);if(t){var i=Qn(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Gn(this,n)}}function Gn(e,t){if(t&&("object"===Wn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Qn(e){return Qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qn(e)}z.def(Un,Fn,(function(){var e=z.model;return{attrs:{id:Un,class:"play-pause-btn content-center cs-button","aria-pressed":!1,"aria-label":"".concat(e.getString("acc_play"),"/").concat(e.getString("acc_pause")),tabindex:-1},tag:"button",style:{background:"transparent",border:"none"},updateHook:function(){this.el.setAttribute("aria-pressed",!this.toggle)},html:function(){return'\n        <div style="transform:translate(0, -2px)">\n          '.concat(this.toggle?sn("pause")():sn("play")(),'\n        </div>\n\n        <svg class="circle-progress" width="38" height="38" viewBox="0 0 38 38">\n          <circle\n            cx="17" cy="17" r="17"\n            fill="#464646"\n            stroke="none" />\n\n          <path\n            d="M 0 0"\n            transform="rotate(-90 17 17)"\n            fill="white"\n            stroke="none" />\n\n          <circle\n            cx="17" cy="17" r="14.5"\n            fill="#31373a"\n            stroke="none" />\n        </svg>\n      ')},minW:Vn,minH:Vn,z:1}}));var qn=DS,Yn=qn.detection,Xn=qn.dom,$n=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zn(e,t)}(o,e);var t,n,r,i=Kn(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Yn.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t}return t=o,(n=[{key:"onTap",value:function(e){Xn.tappedClass(this.el)}},{key:"onClickBtn",value:function(e){var t=this.view,n=t.enabled,r=t.nameKey;n&&DS.pubSub.trigger(DS.events.presentation.ON_OBJECT_EVENT,r+"_pressed")}},{key:"onLayoutChange",value:function(e){var t=this,n=this.view.nameKey;this.hasFocus&&(e[n]?window.requestAnimationFrame((function(){return t.onFocus()})):this.onBlur())}}])&&Zn(t.prototype,n),r&&Zn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Jn="next",er=DS,tr=er.detection,nr=tr.orientation,rr=tr.deviceView.isTablet,ir=er.constants.MOBILE_UI_SIZE;z.def(Jn,$n,(function(e){var t=z.model,n={tag:"button",attrs:{id:Jn,class:"cs-button btn","aria-label":t.getString("acc_next"),tabindex:0},html:'\n      <div class="icon">\n        '.concat(sn(Jn)(),"\n      </div>\n    "),padLeft:6,padRight:6,wp:47,wl:ir,minH:ir,noUpdate:!0,parentAlign:"br",noContent:!1};return rr&&Object.assign(n,{wp:"fit-to-text-w",wl:ir,wPad:function(){return nr.isLandscape?0:20},calcTextSize:!0,html:'\n        <span class="txt">'.concat(t.getString(Jn),'</span>\n        <div class="icon">\n          ').concat(sn("next")(),"\n        </div>\n      "),beforeReflowHook:function(){this.doTextCalcs(),this.update()}}),n}));var or=DS,ar=or.detection,lr=ar.orientation,sr=ar.deviceView.isTablet,cr=or.constants.MOBILE_UI_SIZE,ur="prev";z.def(ur,$n,(function(){var e=z.model,t={tag:"button",attrs:{id:ur,class:"cs-button btn","aria-label":e.getString("acc_prev"),tabindex:0},html:'\n      <div class="icon">\n        '.concat(sn(ur)(),"\n      </div>\n    "),padLeft:6,padRight:6,wp:47,wl:cr,minH:cr,noUpdate:!0,parentAlign:"br",noContent:!1};return sr&&Object.assign(t,{wp:"fit-to-text-w",wl:cr,wPad:function(){return lr.isLandscape?0:20},calcTextSize:!0,html:'\n        <span class="txt">'.concat(e.getString(ur),'</span>\n        <div class="icon">\n          ').concat(sn("prev")(),"\n        </div>\n      "),beforeReflowHook:function(){this.doTextCalcs(),this.update()}}),t}));var fr=DS,dr=fr.constants.MOBILE_UI_SIZE,hr=fr.detection.orientation,pr="navControls";function yr(e){return yr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yr(e)}function br(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==yr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==yr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===yr(o)?o:String(o)),r)}var i,o}function vr(e,t){return vr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},vr(e,t)}function mr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Sr(e);if(t){var i=Sr(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return gr(this,n)}}function gr(e,t){if(t&&("object"===yr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return wr(e)}function wr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sr(e){return Sr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Sr(e)}z.def(pr,(function(e){z.model.rtl;var t=z.getNamespace(e).playbackControls;return{tag:"nav",attrs:{id:pr,"aria-label":"slide navigation"},wp:function(){return this.width||0},wl:"100%",hp:"100%",hl:function(){return this.parent.h-dr},updateHook:function(){var e={reverse:!1,pad:0,fullUpdate:!0,sizeToChildren:!0};hr.isLandscape&&(e.sizeToChildren=!1,e.vertical=!0,e.toTop=!0,e.startPos=this.h-t.h,e.reverse=!0),this.flowChildren(e)}}}));var kr=DS,Cr=kr._,Or=kr.detection,Er=kr.utils,Lr=kr.keyManager,Tr=kr.dom,xr=Tr.addClass,Pr=Tr.removeClass,Ir=kr.globalEventHelper.addWindowListener,_r=kr.events,jr=kr.pubSub,Ar=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vr(e,t)}(o,e);var t,n,r,i=mr(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Cr.bindAll(wr(t),"onTick","onSeek","onMouseDown","onMouseUp","checkSeekable","onKeydown","onTimelineChanged","onChange"),jr.on(_r.slide.STARTED,t.checkSeekable),jr.on(_r.player.SEEK,t.onKeydown),jr.on(_r.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),t.isUserControlled=!z.model.frame.controlOptions.controls.readonly||z.model.frame.controlOptions.controls.readonlyOnce,t.bindListeners(),t.isUp=!0,t}return t=o,(n=[{key:"bindListeners",value:function(){if(this.isUserControlled){var e=Or.deviceView.isClassicDesktop?this.seekEl:this.view.el;Or.device.isMobile?(e.addEventListener("touchstart",this.onMouseDown),this.seekEvent="touchmove",this.endEvent="touchend"):(e.addEventListener("mousedown",this.onMouseDown),e.addEventListener("keydown",this.onKeydown),this.seekEvent="mousemove",this.endEvent="mouseup"),this.progressBarEl.addEventListener("change",this.onChange)}}},{key:"isSeekable",value:function(){if(null==this.currSlide)return!1;var e=z.model.frame.controlOptions.controls,t=e.readonly,n=e.readonlyOnce;return!(t&&!n||n&&!this.currSlide.currentTimelineCompletedOnce())}},{key:"getSeekValue",value:function(e){var t=this.view.getBox();return Er.clamp(0,1,(e-t.x)/t.w)}},{key:"getEventX",value:function(e){return Or.device.isMobile?e.touches[0]&&e.touches[0].pageX:e.pageX}},{key:"onTimelineChanged",value:function(e,t){null!=this.currTimeline&&(this.currTimeline.off(_r.timeline.TICK,this.onTick),this.currTimeline.off(_r.timeline.COMPLETE,this.checkSeekable)),this.currSlide=t,this.currTimeline=e,this.currTimeline.on(_r.timeline.TICK,this.onTick),this.currTimeline.on(_r.timeline.COMPLETE,this.checkSeekable);var n=this.currTimeline.progress();this.duration=Er.toSeconds(this.currTimeline.timelineDuration()),this.progressBarEl.max=Math.round(1e3*this.duration),this.progressBarEl.step=100,isNaN(n)&&(n=0),this.onTick(n),this.checkSeekable()}},{key:"checkSeekable",value:function(){this.isSeekable()?(Pr(this.el,"read-only"),this.progressBarEl.disabled=!1):(xr(this.el,"read-only"),this.progressBarEl.disabled=!0)}},{key:"onTick",value:function(e){var t=100*e;this.progressBarFillEl.style.width="".concat(t,"%"),this.progressBarEl.setAttribute("aria-valuetext","".concat(Math.round(t),"%")),this.progressBarEl.value=this.currTimeline.timeline?this.currTimeline.timeline.currentTime:0,jr.trigger(_r.currTimeline.TICK,e)}},{key:"onSeek",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.seeking=!0,!0!==this.isUp&&this.currTimeline.isPlaying()&&(this.currTimeline.pause(!0),this.currTimeline.on(_r.timeline.AFTER_SEEK_UPDATE,(function e(){t.currTimeline.play(),t.currTimeline.off(_r.timeline.AFTER_SEEK_UPDATE,e)})));var n=this.getEventX(e);null!=n&&this.currTimeline.progress(this.getSeekValue(n)),this.isUp=!1}},{key:"seekBy",value:function(e,t){var n=this;e.preventDefault();var r=(this.currTimeline.elapsedTime()+t)/this.currTimeline.duration();this.currTimeline.onSeekStart(),this.currTimeline.progress(r),this.currTimeline.onSeekEnd(),this.currTimeline.isPlaying()&&(this.currTimeline.pause(),setTimeout((function(){return n.currTimeline.play()}),125))}},{key:"isEnded",value:function(){return this.currTimeline.progress()>=1}},{key:"onChange",value:function(e){this.currTimeline.progress(this.progressBarEl.value/this.progressBarEl.max)}},{key:"onMouseDown",value:function(e){this.isUp=!1,this.removeEndListener=Ir(this.endEvent,this.onMouseUp),this.removeSeekListener=Ir(this.seekEvent,this.onSeek),this.currTimeline.onSeekStart(),this.onSeek(e)}},{key:"onMouseUp",value:function(e){this.onSeek(e),this.removeEndListener(),this.removeSeekListener(),this.isUp=!0,this.currTimeline.onSeekEnd()}},{key:"onKeydown",value:function(e){var t=e.which;Lr.isActionKey(t)?jr.trigger(_r.player.TOGGLE_PLAYBACK):this.isSeekable()&&(Lr.isDownishKey(t)?this.seekBy(e,-100):Lr.isPageDownKey(t)?this.seekBy(e,-1e3):Lr.isUpishKey(t)?this.seekBy(e,100):Lr.isPageUpKey(t)&&this.seekBy(e,1e3))}}])&&br(t.prototype,n),r&&br(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Dr="seek";z.def(Dr,Ar,(function(){var e=z.model,t=z.model.frame.controlOptions.controls,n=t.readonly,r=t.seekbar;return{attrs:{id:Dr,tabindex:-1,class:"progress-bar cs-seekcontrol ".concat(n?"read-only":"")},y:function(){return this.parent.h/3},overflow:"visible",noUpdate:!0,w:function(){return this.width||0},h:30,html:'\n      <div class="cs-seekbar-inner progress-bar-inner slide-lockable">\n        <div class="cs-seek progress-bar-seek">\n          <div data-ref="progressBarFill" class="cs-fill progress-bar-fill" style="width: 0px">\n          </div>\n        </div>\n        <input\n          aria-hidden="'.concat(!r,'"\n          ').concat(n?"disabled":"",'\n          data-ref="progressBar"\n          type="range"\n          tabindex="0"\n          aria-label="').concat(e.getString("acc_slide_progress"),'"\n        />\n      </div>\n    ')}}));var Br=DS,Rr=Br.constants.MOBILE_UI_SIZE,Mr=Br.detection,Nr=Mr.orientation,Hr=Mr.deviceView.isTablet,Fr="submit";z.def(Fr,$n,(function(){var e=z.model,t={tag:"button",attrs:{id:Fr,class:"cs-button btn","aria-label":e.getString("acc_submit"),tabindex:0},html:'\n      <div class="icon">\n        '.concat(sn(Fr)(),"\n      </div>\n    "),overflow:"visible",padLeft:6,padRight:6,wp:47,wl:Rr,minH:Rr,noUpdate:!0,parentAlign:"br",noContent:!1};return Hr&&Object.assign(t,{wp:"fit-to-text-w",wl:Rr,wPad:function(){return Nr.isLandscape?0:20},calcTextSize:!0,html:'\n        <span class="txt">'.concat(e.getString(Fr),'</span>\n        <div class="icon">\n          ').concat(sn(Fr)(),"\n        </div>\n      "),beforeReflowHook:function(){this.doTextCalcs(),this.update()}}),t}));function Vr(e){return Vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vr(e)}function Ur(e,t,n){return(t=Zr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Wr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Zr(r.key),r)}}function Zr(e){var t=function(e,t){if("object"!==Vr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Vr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Vr(t)?t:String(t)}function zr(e,t){return zr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zr(e,t)}function Kr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=qr(e);if(t){var i=qr(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Gr(this,n)}}function Gr(e,t){if(t&&("object"===Vr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Qr(e)}function Qr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qr(e){return qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},qr(e)}var Yr=DS,Xr=Yr.events,$r=Yr.pubSub,Jr=Yr.detection.orientation,ei=(Yr.utils.getPath,Yr.dom.hasClass),ti=Yr._,ni=ti.bindAll,ri=ti.first,ii=ti.kebabCase,oi=Yr.globalEventHelper.addWindowListener,ai=Yr.constants.MOBILE_ANIMATION_DURATION,li=/Panel$/,si=/Link$/,ci=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zr(e,t)}(o,e);var t,n,r,i=Kr(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(n=i.call(this,e)).ns=z.getCurrentNameSpace(),ni(Qr(n),"onShow","onHide","onCloseClick","onLayoutReady","onCheckLayoutLandscape"),$r.on((Ur(t={},Xr.tab.SHOW,n.onShow),Ur(t,Xr.tab.HIDE,n.onHide),Ur(t,Xr.frame.LAYOUT_READY,n.onLayoutReady),t)),o.current=ri(n.view.children),n.onClickEl(n.tabCloseEl,n.onCloseClick),oi("resize",n.onCheckLayoutLandscape),n}return t=o,n=[{key:"isDeactivated",value:function(e){return ei(document.body,"no-"+e)}},{key:"onCheckLayoutLandscape",value:function(){!this.view.visible&&null!=this.lastViewVisibility&&Jr.isLandscape&&(this.onLayoutReady(this.lastViewVisibility),this.lastViewVisibility=null)}},{key:"onLayoutReady",value:function(e){var t=this;if(null==this.lastLink||!e[this.lastLink.nameKey])if(Jr.isLandscape){var n=this.ns.tabs.children;o.panelAvailable=n.some((function(n){var r=n.nameKey.replace(si,""),i=si.test(n.nameKey)&&e[n.nameKey]&&!t.isDeactivated(r);return i&&t.onShow(n.nameKey.replace(si,"Panel"),!1),i})),this.lastViewVisibility=null}else this.lastViewVisibility=e}},{key:"onCloseClick",value:function(){Jr.isLandscape?this.ns.sidebar.viewLogic.toggle():$r.trigger(DS.events.tab.HIDE)}},{key:"onShow",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.view.children[e];if(this.view.setVisibility(t),null!=this.lastPanel&&this.lastPanel.setVisibility(!1),null!=n){n.setVisibility(t),o.current=n;var r=this.ns[e.replace(li,"Link")];this.lastLink=r,r.viewLogic.select(),t&&($r.trigger(Xr.slide.ARIA_TOGGLE,{hidden:!0}),"outlinePanel"===e?$r.trigger(Xr.mobile.OUTLINE_SHOWN):"transcriptPanel"===e&&$r.trigger(Xr.mobile.NOTES_SHOWN),document.body.classList.add("tab-shown"),null!=this.prevPanelName&&document.body.classList.remove(this.prevPanelName),this.prevPanelName=this.activePanelClass(e),document.body.classList.add(this.prevPanelName),null!=this.lastPanel&&(this.lastPanel.el.style.opacity=0),n.el.style.opacity=1),this.lastPanel=n}}},{key:"activePanelClass",value:function(e){return"tab-active-".concat(ii(e))}},{key:"onHide",value:function(e){clearTimeout(this.tabShownDelayId),this.tabShownDelayId=setTimeout((function(){document.body.classList.remove("tab-shown")}),ai),$r.trigger(Xr.slide.ARIA_TOGGLE,{hidden:!1}),this.view.setVisibility(!1)}}],n&&Wr(t.prototype,n),r&&Wr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut);ci.current=null,ci.panelAvailable=!1;var ui=ci;function fi(e){return fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fi(e)}function di(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return l}}(e,t)||yi(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hi(e,t,n){return(t=mi(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pi(e){return function(e){if(Array.isArray(e))return bi(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yi(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yi(e,t){if(e){if("string"==typeof e)return bi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?bi(e,t):void 0}}function bi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function vi(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,mi(r.key),r)}}function mi(e){var t=function(e,t){if("object"!==fi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==fi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===fi(t)?t:String(t)}function gi(e,t){return gi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},gi(e,t)}function wi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ci(e);if(t){var i=Ci(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Si(this,n)}}function Si(e,t){if(t&&("object"===fi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ki(e)}function ki(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ci(e){return Ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ci(e)}var Oi=DS,Ei=Oi._,Li=Oi.detection,Ti=Li.orientation,xi=Li.deviceView,Pi=xi.isTablet,Ii=xi.isPhone,_i=Oi.events,ji=Oi.pubSub,Ai=Oi.dom,Di=(Ai.addClass,Ai.removeClass,Oi.globalEventHelper),Bi=Di.addWindowListener,Ri=Di.addDocumentListener,Mi=Oi.constants,Ni=Mi.MOBILE_UI_SIZE,Hi=Mi.MOBILE_ANIMATION_DURATION,Fi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gi(e,t)}(o,e);var t,n,r,i=wi(o);function o(e){var t,n;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),n=i.call(this,e),Ei.bindAll(ki(n),"showContent","hideContent","onCheckTabShown","onCheckTabHidden","forceHide","onToggleSearch");var r=z.getCurrentNameSpace();return n.panels=r.panels,n.visibleOverlay=r.visibleOverlay,n.hamburger=r.hamburger,n.toggleShow=!1,window.requestAnimationFrame((function(){pi(document.querySelectorAll(".hamburger")).forEach((function(e){return n.onClickEl(e,n.onToggle)}))})),Bi("resize",n.onResize.bind(ki(n))),ji.on((hi(t={},_i.sidebar.OPEN,n.showContent),hi(t,_i.sidebar.CLOSE,n.hideContent),hi(t,_i.tab.SHOW,n.onCheckTabShown),hi(t,_i.tab.HIDE,n.onCheckTabHidden),hi(t,_i.window.STACKING_CHANGED,n.forceHide),t)),n.searchToggle=!1,Ri("touchend",n.onToggleSearch),n.hideContent(),n}return t=o,n=[{key:"onToggleSearch",value:function(e){if(e.target.classList.contains("search-toggle-btn")){this.searchToggle=!this.searchToggle;var t=di(this.searchToggle?["add",_i.sidebar.SHOW_SEARCH,!0]:["remove",_i.sidebar.HIDE_SEARCH,!1],3),n=t[0],r=t[1],i=t[2];ji.trigger(r),document.body.classList[n]("search-active"),e.target.setAttribute("aria-pressed",i)}}},{key:"forceHide",value:function(e){"MessageWnd"===e&&(this.hideContent(),ji.trigger(_i.tab.HIDE))}},{key:"onCheckTabShown",value:function(){this.tabShown=!0,Ti.isPortrait&&this.hideContent()}},{key:"onCheckTabHidden",value:function(){this.tabShown=!1}},{key:"onResize",value:function(){var e=this;this.el.classList.remove("transition-all"),setTimeout((function(){e.el.classList.add("transition-all"),e.tabShown&&Ti.isLandscape&&(e.showContent(),e.el.style.display="block")}),Hi),this.hideContent()}},{key:"ariaToggleBurger",value:function(e){null==this.hamburgers&&(this.hamburgers=document.querySelectorAll(".hamburger")),this.hamburgers.forEach((function(t){t.setAttribute("aria-expanded",e)}))}},{key:"showContent",value:function(){var e=this,t=this.view.children.tabs,n=z.getNamespace(this.view.nameSpace).hamburger;if(Ti.isPortrait&&this.visibleOverlay.setVisibility(!0),this.el.removeAttribute("aria-hidden"),this.el.focus(),this.el.style.display="block",window.requestAnimationFrame((function(){e.el.style.opacity=1;var n=window.innerHeight-t.h-Ni,r=0;Pi&&(n-=25,r=25),e.el.style.transform=Ti.isPortrait?"translate(".concat(r,"px, ").concat(n,"px)"):"translate(".concat(window.innerWidth-160,"px, 0)")})),this.toggleShow=!0,null!=n){var r=n.children,i=r.burger,o=r.searchBtn;this.ariaToggleBurger(!0),this.el.setAttribute("aria-expanded",!0),(Ii||Pi&&Ti.isLandscape)&&(i.el.style.display="none"),null!=o&&(o.el.style.display="none")}if(!this.tabShown&&Ti.isLandscape&&ui.panelAvailable){var a=ui.current||Ei.first(this.panels.children);null!=a&&(ji.trigger(_i.tab.SHOW,a.nameKey),this.tabShown=!0)}}},{key:"hideContent",value:function(){var e=this,t=z.getNamespace(this.view.nameSpace).hamburger;if(this.visibleOverlay.setVisibility(!1),this.el.style.opacity=0,this.el.setAttribute("aria-hidden",!0),Ti.isPortrait&&setTimeout((function(){e.el.style.display="none"}),Hi),this.view.updateTrans(),this.toggleShow=!1,null!=t){var n=t.children,r=n.burger,i=n.searchBtn;this.ariaToggleBurger(!1),r.el.style.display="block",null!=i&&(i.el.style.display="block"),this.el.setAttribute("aria-expanded",!1)}}},{key:"toggle",value:function(){this.toggleShow?(this.hideContent(),Ei.forEach(this.el.querySelectorAll(".selected"),(function(e){return e.classList.remove("selected")})),Ti.isLandscape&&ji.trigger(_i.tab.HIDE)):this.showContent(),this.ariaToggleBurger(this.toggleShow),this.el.setAttribute("aria-expanded",this.toggleShow)}},{key:"onToggle",value:function(e){null!=this.hamburger&&this.hamburger.enabled&&this.toggle()}}],n&&vi(t.prototype,n),r&&vi(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Vi=Fi,Ui=DS.constants.MOBILE_UI_SIZE,Wi=DS.detection.deviceView.isTablet,Zi="sidebar";function zi(e){return zi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zi(e)}function Ki(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==zi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==zi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===zi(o)?o:String(o)),r)}var i,o}function Gi(e,t){return Gi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Gi(e,t)}function Qi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Yi(e);if(t){var i=Yi(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return qi(this,n)}}function qi(e,t){if(t&&("object"===zi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Yi(e){return Yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yi(e)}z.def(Zi,Vi,(function(e){var t=z.getNamespace(e).tabs,n={tag:"section",attrs:{id:Zi,"aria-label":"sidebar",class:"transition-all "+(Wi?"sidebar-tablet-trans":"")},z:3,overflow:"visible",wp:function(){return this.parent.w},wl:160,hp:function(){return t.h+Ui},hl:function(){return this.parent.w},xp:0,xl:function(){return window.innerWidth},yl:0,yp:function(){return window.innerHeight},html:cn("landscape")};return Wi&&Object.assign(n,{hp:function(){return t.h},wp:function(){return this.width||this.parent.w}}),n}));var Xi=DS,$i=Xi._,Ji=Xi.pubSub,eo=Xi.events,to=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gi(e,t)}(o,e);var t,n,r,i=Qi(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickLink),t}return t=o,(n=[{key:"onClickLink",value:function(e){Ji.trigger(eo.tab.SHOW,this.model.name+"Panel")}},{key:"deselect",value:function(){$i.forEach(this.el.parentNode.querySelectorAll(".selected"),(function(e){e.classList.remove("selected")}))}},{key:"select",value:function(){this.deselect(),this.el.classList.add("selected")}}])&&Ki(t.prototype,n),r&&Ki(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut);function no(e){return no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},no(e)}function ro(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==no(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==no(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===no(o)?o:String(o)),r)}var i,o}function io(e,t){return io=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},io(e,t)}function oo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=lo(e);if(t){var i=lo(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return ao(this,n)}}function ao(e,t){if(t&&("object"===no(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function lo(e){return lo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},lo(e)}var so=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&io(e,t)}(o,e);var t,n,r,i=oo(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickLink),t}return t=o,(n=[{key:"onClickLink",value:function(e){var t=this.model.properties.data;DS.pubSub.trigger(DS.events.sidebar.CLOSE),DS.pubSub.trigger(DS.events.tab.HIDE),DS.pubSub.trigger(DS.events.customlink.EVENT,t)}}])&&ro(t.prototype,n),r&&ro(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut);function co(e){return function(e){if(Array.isArray(e))return uo(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return uo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return uo(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var fo=DS,ho=(fo.utils.getPath,fo.constants.MOBILE_UI_SIZE),po=fo.detection,yo=po.orientation,bo=po.deviceView,vo=bo.isTablet,mo=bo.isPhone,go="tabs",wo=z.def(go,(function(e){var t=!0,n=z.getNamespace(e),r=n.sidebar;n.tabs;return{attrs:{id:go,tabindex:0,"aria-label":"sidebar-tabs"},overflow:"auto",x:0,yl:ho,yp:function(){return vo&&yo.isPortrait?0:ho},noContent:!1,w:"100%",h:function(){return yo.isLandscape?window.innerHeight-58:this.height||0},methods:{sizeToLargestTab:function(){var e=160;this.children.forEach((function(t){t.el.style.width="auto";var n=t.el.clientWidth;n>e&&(e=n)})),r.width=e,r.updateSize(),this.updateSize(),this.children.forEach((function(t){t.el.style.width="",t.width=e,t.updateSize()}))}},updateHook:function(){0!==this.children.length&&(this.flowChildren({fullUpdate:!1,sizeToChildren:!0,vertical:!0}),vo&&t&&yo.isPortrait?(this.sizeToLargestTab(),t=!1):this.updateSize())},childDef:function(){So(z.model)}}})),So=function(e){[].concat(co(e.sidebarTabs),co(e.topTabsLeft),co(e.topTabsRight)).forEach((function(t,n){!function(e,t,n){var r,i,o,a=t.name,l=a+"Tab";"customlink"===a?(r=so,i=t.properties.title,l="link".concat(n),o="custom-link"):(r=to,i=e.getString(t.name),l=t.name+"Link",o="panel-link");var s=z.def(l,r,{model:Object.assign(t,{idx:n}),tag:"button",attrs:{id:l,class:"tab ".concat(o),"aria-label":i,tabindex:-1},w:function(){return yo.isLandscape||mo?this.parent.w:this.width||null},h:50,noUpdate:!0,html:i});s.init(),wo.append(s)}(e,t,n)}))};function ko(e){return ko="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ko(e)}function Co(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ko(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ko(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ko(o)?o:String(o)),r)}var i,o}function Oo(e,t){return Oo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Oo(e,t)}function Eo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=xo(e);if(t){var i=xo(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Lo(this,n)}}function Lo(e,t){if(t&&("object"===ko(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return To(e)}function To(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xo(e){return xo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xo(e)}var Po,Io=DS,_o=Io.pubSub,jo=Io.events,Ao=Io.TweenLite,Do=Io.focusManager,Bo=Io._.bindAll,Ro=Io.dom.addClass,Mo=Io.detection,No=!1,Ho=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Oo(e,t)}(o,e);var t,n,r,i=Eo(o);function o(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o);var n=(t=i.call(this,e)).view.children.searchForm.el,r=t.view.children.searchInput.el;return n.addEventListener("submit",t.onSearch.bind(To(t))),Mo.deviceView.isMobile&&(Bo(To(t),"onMobileOutlineShown"),r.addEventListener("input",t.onSearch.bind(To(t))),_o.on(jo.mobile.OUTLINE_SHOWN,t.onMobileOutlineShown)),t.ensureEventSubscriptions(),t}return t=o,n=[{key:"teardown",value:function(){_o.off(jo.mobile.OUTLINE_SHOWN,this.onMobileOutlineShown),_o.off(jo.controlOptions.RESET,this.onControlOptionsReset,this),No=!1}},{key:"ensureEventSubscriptions",value:function(){No||(_o.on(jo.controlOptions.RESET,this.onControlOptionsReset,this),No=!0)}},{key:"onControlOptionsReset",value:function(){this.view.updateHtml(),this.view.update()}},{key:"onMobileOutlineShown",value:function(){document.getElementById("outline-panel").appendChild(this.el),Ro(document.body,"tab-shown")}},{key:"onFocus",value:function(e){Do.setFocusRectOn(this.view.el)}},{key:"onSearch",value:function(e){e.preventDefault();var t=this.view.parent.children.searchResults,n=this.view.children.bottomDiv,r=document.querySelector("#outline-content ul");Ao.to(r,.2,{alpha:0,onComplete:function(){t.el.style.opacity=0,Ao.to(t.el,.2,{alpha:1,onComplete:function(){return t.viewLogic.onAfterVisible()}}),n.el.style.opacity=0,t.setVisibility(!0)}}),t.viewLogic.performSearch(this.searchInputEl.value),Ro(document.body,"search-results-active")}},{key:"updateVisibility",value:function(e){var t=this.view,n=t.searchResults,r=t.searchResults.visible,i=t.visible;this.view.setVisibility(e),n.setVisibility(e&&this.searchResultsOpen),i&&(this.searchResultsOpen=r)}}],n&&Co(t.prototype,n),r&&Co(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Fo=Ho,Vo="search",Uo=DS,Wo=Uo.pubSub,Zo=Uo.events,zo=Uo.constants.MOBILE_ANIMATION_DURATION,Ko=z.def(Vo,Fo,(function(){var e=z.model,t=(z.getCurrentNameSpaceString(),e.frame.controlOptions.controls.search),n=0;return t&&(n=-58,Wo.on(Zo.sidebar.SHOW_SEARCH,(function(){n=0,Po.update()})),Wo.on(Zo.sidebar.HIDE_SEARCH,(function(){setTimeout((function(){n=-58,Po.update()}),zo)}))),{attrs:{class:Vo,"aria-label":e.getString("acc_search_input"),tabIndex:0},style:{background:"white"},z:3,x:0,y:function(){return n},w:function(){return this.parent.w},h:function(){return 58},updateHook:function(){Po=this},html:'\n      <div class="search-ui">\n        <div data-ref="bottomDiv"></div>\n        <form id="outline-search" data-ref="searchForm" class="search-input cs-outlinesearch cs-searchinput">\n          <input class="cs-input " data-ref="searchInput" type="search" placeholder="'.concat(e.getString("search"),'">\n          <div\n            class="input-search-icon"\n            style="background-repeat: no-repeat"\n            tabindex="-1"\n          >').concat(sn("search")(),"</div>\n        </form>\n      </div>\n    ")}}));function Go(e){return Go="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Go(e)}function Qo(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Go(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Go(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Go(o)?o:String(o)),r)}var i,o}function qo(){return qo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Yo(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},qo.apply(this,arguments)}function Yo(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ta(e)););return e}function Xo(e,t){return Xo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Xo(e,t)}function $o(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ta(e);if(t){var i=ta(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Jo(this,n)}}function Jo(e,t){if(t&&("object"===Go(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ea(e)}function ea(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ta(e)}var na=DS,ra=na.focusManager,ia=na.keyManager,oa=na.detection,aa=na.globalEventHelper,la=aa.addDocumentListener,sa=aa.removeDocumentListener,ca=(na.utils.getPath,na.dom),ua=ca.parentNodesOf,fa=ca.addClass,da=ca.removeClass,ha=na._,pa=ha.first,ya=ha.last,ba=ha.bindAll,va=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xo(e,t)}(o,e);var t,n,r,i=$o(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),ba(ea(t),"onKeydown","addEvents","onClickItem"),t.addEvents(),t}return t=o,(n=[{key:"focusSelf",value:function(){this.hasListItems()&&this.getItems().find((function(e){return 0===e.tabIndex})).focus()}},{key:"addEvents",value:function(){var e=this;oa.device.isMobile?(this.el.addEventListener("touchmove",(function(t){e.moved=!0})),this.onClick((function(t){e.moved||e.onClickItem(t),e.moved=!1}))):this.onClick(this.onClickItem)}},{key:"onClickItem",value:function(){}},{key:"onFocus",value:function(){!this.isFocused&&this.hasListItems()&&(this.isFocused=!0,this.currentItem=this.currentItem||pa(this.getItems()),fa(this.currentItem,"hover"),ia.isShowFocus&&this.centerOnFocused(),ra.setFocusRectOn(this.getFocusRectTarget()),la("keydown",this.onKeydown))}},{key:"onBlur",value:function(e){this.el.contains(e.relatedTarget)||(qo(ta(o.prototype),"onBlur",this).call(this,e),null!=this.currentItem&&(da(this.currentItem,"hover"),this.currentItem.style.backgroundColor=""),sa("keydown",this.onKeydown),this.isFocused=!1)}},{key:"onKeydown",value:function(e){var t=this.currentItem;ia.isActionKey(e.which)?(this.activateItem(e),ia.isSpaceKey(e.which)&&e.preventDefault()):ia.isDownishKey(e.which)?this.currentItem=this.getNextItem(this.getItemContent()):ia.isUpishKey(e.which)?this.currentItem=this.getPrevItem(this.getItemContent()):ia.isHomeKey(e.which)?this.currentItem=this.getFirstItem():ia.isEndKey(e.which)&&(this.currentItem=this.getLastItem()),t!==this.currentItem&&(e.preventDefault(),t.tabIndex=-1,da(t,"hover"),this.focusOnCurrent())}},{key:"hasListItems",value:function(){return!1}},{key:"getFocusRectTarget",value:function(){return this.currentItem}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem&&(this.currentItem.tabIndex=0,fa(this.currentItem,"hover"),this.currentItem.focus()),ra.setFocusRectOn(this.getFocusRectTarget())}},{key:"activateItem",value:function(){this.onClickItem({target:this.currentItem.firstElementChild})}},{key:"getNextItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))+1;return n===this.getItems().length&&(n=0),this.getItems()[n]}},{key:"getPrevItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))-1;return-1===n&&(n=this.getItems().length-1),this.getItems()[n]}},{key:"getLastItem",value:function(){return ya(this.getItems())}},{key:"getFirstItem",value:function(){return pa(this.getItems())}},{key:"getItemContent",value:function(){return this.currentItem.textContent.trim()}},{key:"findIndexCb",value:function(e,t){return e.textContent.trim()===t}},{key:"getItems",value:function(){return[]}},{key:"getScrollEl",value:function(){return this.el}},{key:"getOffsetEl",value:function(){return this.el}},{key:"getOffsetTop",value:function(e){return function(e){return ua(e,(function(e){return"li"===e.nodeName.toLowerCase()})).reduce((function(e,t){return e+t.offsetTop}),0)}(e)}},{key:"getOffsetHeight",value:function(e){return e.offsetHeight}},{key:"centerOnFocused",value:function(){if(null!=this.currentItem){var e=this.getOffsetEl(),t=this.getOffsetHeight(e),n=this.getScrollEl().scrollTop,r=this.getOffsetTop(this.currentItem),i=r+this.getOffsetHeight(this.currentItem);i-n>t?e.scrollTop=i-t+10:r<n+10&&(e.scrollTop=r-10)}}}])&&Qo(t.prototype,n),r&&Qo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),ma=DS,ga=(ma.MicroScrollBar,ma.detection,function(e){return null!=e&&2===e.split(".").length}),wa=function(){return"no icon"},Sa={next:function(){return'\n  <svg class="cs-icon next-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="rotate(180, 5, 8)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>'},prev:function(){return'\n  <svg class="cs-icon prev-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="translate(0, 1)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>\n'},submit:function(){return'\n   <svg class="cs-icon check-icon" width="17px" height="18px" viewBox="0 0 17 16" focusable="false">\n\n  <path stroke="none" transform="translate(0, 1)" d="\n  M 17 1.4\n  L 15.6 0 5.7 9.9 1.4 5.65 0 7.05 5.65 12.75 5.7 12.75 17 1.4 Z"/>\n\n  </svg>'},replay:function(){return'<svg class="cs-icon" x="0px" y="0px" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 10.95 8.75\n      Q 11 9 11 9.25 10.95 11.15 9.7 12.4 8.4 13.7 6.5 13.75 4.6 13.7 3.3 12.4 2.05 11.15 2 9.25 2.05 7.3 3.3 6.05 4.398828125 4.998828125 6 4.75\n      L 6 6.9\n      Q 6.05 7.75 6.85 7.35\n      L 11.35 4.3\n      Q 11.7 4.05 11.7 3.75 11.7 3.45 11.35 3.2\n      L 6.85 0.15\n      Q 6.05 -0.3 6 0.6\n      L 6 2.75\n      Q 3.4517578125 3.001171875 1.8 4.75 0.05 6.6 0 9.25 0.05 12 1.9 13.85 3.75 15.65 6.5 15.75 9.25 15.65 11.1 13.85 12.95 12 13 9.25 13 9 13 8.75\n      L 10.95 8.75 Z"/>\n    </svg>'},play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="11" height="13" viewBox="0 0 11 13" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 0.851 13.011\n      C 0.381 13.295 0 13.068 0 12.526\n      L 0 0.771\n      C 0 0.219 0.378 0 0.854 0.288\n      L 10.507 6.132\n      C 10.979 6.417 10.981 6.878 10.504 7.168\n      L 6.307 9.708\n      L 0.851 13.011 Z" />\n  </svg>'},pause:function(){return'<svg id="icon-pause" class="cs-icon pause-icon" width="9" height="14" viewBox="0 0 9 14" focusable="false">\n    <rect x="0" width="3" height="14"/>\n    <rect x="6" width="3" height="14"/>\n  </svg>'},volume:function(e,t){var n=Math.min(1,e/5),r=Math.min(1,Math.max(0,e/5-.5));return'<svg class="cs-icon volume-icon '.concat(t?"volume-icon-selected":"",'" width="16px" height="14px" viewBox="0 0 16 14" focusable="false">\n      <rect x="0" y="4" width="3" height="6"></rect>\n      <polygon points="4 4 9 0 9 14 4 10"></polygon>\n      <g transform="translate(10, 0)">\n        <mask id="vol-mask" fill="white">\n          <rect id="path-1" x="0" y="0" width="8" height="14" style="fill: white;"></rect>\n        </mask>\n        <circle strokeWidth="1.5" style="opacity: ').concat(r,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="6.5"></circle>\n        <circle strokeWidth="1.5" style="opacity: ').concat(n,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="3.5"></circle>\n      </g>\n    </g>\n  </svg>')},captionsOn:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <path fill="#FFFFFF" stroke="none" d="M 19 2 Q 19 1.15 18.4 0.6 17.85 0 17 0 L 2 0 Q 1.15 0 0.6 0.6 0 1.15 0 2 L 0 12 Q 0 12.85 0.6 13.4 1.15 14 2 14 L 7.6 14 9.5 15.9 11.4 14 17 14 Q 17.85 14 18.4 13.4 19 12.85 19 12 L 19 2 M 15.7 4.2 L 15.25 4.85 Q 15.15 4.9 15.1 5 15 5.05 14.85 5.05 14.75 5.05 14.6 4.95 14.5 4.9 14.3 4.8 14.15 4.65 13.9 4.6 13.65 4.5 13.3 4.5 12.85 4.5 12.5 4.7 12.15 4.85 11.9 5.15 11.7 5.45 11.6 5.9 11.45 6.35 11.45 6.9 11.5 7.45 11.6 7.9 11.7 8.35 11.95 8.65 12.2 8.95 12.5 9.15 12.85 9.3 13.25 9.3 13.65 9.3 13.9 9.2 14.2 9.1 14.35 8.95 14.5 8.85 14.65 8.75 14.8 8.65 14.95 8.65 15.15 8.65 15.25 8.8 L 15.75 9.4 Q 15.45 9.75 15.15 10 14.8 10.2 14.45 10.35 14.05 10.5 13.7 10.55 13.3 10.6 12.95 10.6 12.25 10.6 11.65 10.35 11.1 10.1 10.65 9.65 10.2 9.15 9.95 8.45 9.7 7.75 9.7 6.9 9.7 6.1 9.95 5.4 10.15 4.75 10.6 4.25 11.05 3.75 11.7 3.5 12.35 3.2 13.2 3.2 14 3.2 14.6 3.45 15.2 3.7 15.7 4.2 M 5.85 4.7 Q 5.5 4.85 5.25 5.15 5.05 5.45 4.95 5.9 4.8 6.35 4.8 6.9 4.85 7.45 4.95 7.9 5.05 8.35 5.3 8.65 5.55 8.95 5.85 9.15 6.2 9.3 6.6 9.3 7 9.3 7.25 9.2 7.55 9.1 7.7 8.95 7.85 8.85 8 8.75 8.15 8.65 8.3 8.65 8.5 8.65 8.6 8.8 L 9.1 9.4 Q 8.8 9.75 8.5 10 8.15 10.2 7.8 10.35 7.4 10.5 7.05 10.55 6.65 10.6 6.3 10.6 5.6 10.6 5 10.35 4.45 10.1 4 9.65 3.55 9.15 3.3 8.45 3.05 7.75 3.05 6.9 3.05 6.1 3.3 5.4 3.5 4.75 3.95 4.25 4.4 3.75 5.05 3.5 5.7 3.2 6.55 3.2 7.35 3.2 7.95 3.45 8.55 3.7 9.05 4.2 L 8.6 4.85 Q 8.5 4.9 8.45 5 8.35 5.05 8.2 5.05 8.1 5.05 7.95 4.95 7.85 4.9 7.65 4.8 7.5 4.65 7.25 4.6 7 4.5 6.65 4.5 6.2 4.5 5.85 4.7 Z"/>\n          </svg>'},captionsOff:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <g>\n              <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n              <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n            </g>\n          </svg>'},captions:function(){return'\n    <svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n      <g>\n        <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n        <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n      </g>\n    </svg>'},carrot:function(e){return'\n    <svg style="left:calc('.concat(e,');" class="cs-icon cs-icon-carrot carrot"width="30" height="30" viewBox="0 0 30 30" focusable="false">\n      <g transform="translate(8, 8)">\n        <polygon style="fill:currentColor !important" points="1,1.5 5,5 1,8.5"/>\n      </g>\n  </svg>')},search:function(){return'\n    <svg class="search-icon" width="13px" height="15px" viewBox="0 0 13 15" focusable="false"\n      <g fill="none" fill-rule="evenodd">\n        <g stroke-width="2">\n          <circle cx="5.6" cy="5.6" r="4.6"/>\n          <path d="M8 9l4 5"/>\n        </g>\n      </g>\n    </svg>\n    '},searchClear:function(){return'\n    <svg class="cs-icon icon" width="11px" height="11px" viewBox="0 0 11 11">\n    <g id="Desktop-Color-Contrast" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="Search" transform="translate(-290.000000, -69.000000)" fill="fill:currentColor !important" fill-rule="nonzero">\n            <g id="search" transform="translate(13.000000, 59.000000)">\n                <polygon id="ic_close" points="286.777666 10 282.500215 14.2779053 278.222334 10 277 11.2222382 281.277881 15.5002869 277 19.7779053 278.222334 21 282.500215 16.7222382 286.777666 21 288 19.7779053 283.722119 15.5002869 288 11.2222382"></polygon>\n            </g>\n        </g>\n    </g>\n    </svg>\n    '},filter:function(){return'<svg class="cs-icon icon-gear" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n    <path id="icon-gear" transform="translate(0,3)" d="M11.1,9.8C11.1,9.8,11.1,9.8,11.1,9.8C11.1,9.8,11.1,9.7,11.1,9.8c0-0.1,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1c0,0,0,0,0,0c0-0.1,0.1-0.1,0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1,0.1-0.2c0,0,0,0,0,0c0.1-0.2,0.2-0.5,0.2-0.7l2-0.4V6.4l-2-0.4c0-0.3-0.1-0.5-0.2-0.7c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0,0,0-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.2-1.7l-0.9-0.9L9.7,2.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1-0.1c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0C8.3,2.1,8.1,2.1,7.8,2L7.4,0H6.2L5.9,2c-0.3,0-0.5,0.1-0.7,0.2c0,0,0,0,0,0C5,2.3,5,2.3,4.9,2.3c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0L2.3,1.6L1.4,2.5l1.2,1.7c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0C2.2,5,2.2,5,2.2,5.1c0,0,0,0,0,0c0,0.1,0,0.1-0.1,0.2c0,0,0,0,0,0C2,5.5,1.9,5.8,1.9,6l-2,0.4v1.2l2,0.4c0,0.3,0.1,0.5,0.2,0.7c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-1.2,1.7l0.9,0.9L4,11.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1c0,0,0,0,0,0c0.1,0,0.1,0.1,0.2,0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0.1c0,0,0,0,0,0c0.2,0.1,0.5,0.2,0.7,0.2l0.4,2h1.2l0.4-2c0.3,0,0.5-0.1,0.7-0.2c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.7,1.2l0.9-0.9L11.1,9.8C11,9.8,11,9.8,11.1,9.8C11,9.8,11.1,9.8,11.1,9.8z M6.8,9.2c-1.2,0-2.2-1-2.2-2.2c0-1.2,1-2.2,2.2-2.2C8,4.8,9,5.8,9,7C9,8.2,8,9.2,6.8,9.2z"/>\n  </svg>'},close:function(){return'\n    <svg class="cs-icon icon-close" width="20" height="20" viewBox="0 0 36 36" focusable="false">\n      <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n    </svg>'},clear:function(){return'<svg class="cs-icon icon-clear" width="13" height="14" viewBox="0 0 13 14" focusable="false">\n    <use xlink:href="#icon-clear" fill="rgba(240, 240, 240, 1)" transform="translate(0, 1)" />\n    <path id="icon-clear" transform="translate(3,3)" d="M6.5,0C2.9,0,0,2.9,0,6.5C0,10.1,2.9,13,6.5,13c3.6,0,6.5-2.9,6.5-6.5C13,2.9,10.1,0,6.5,0z M1.5,6.5c0-2.8,2.2-5,5-5c1.2,0,2.4,0.5,3.2,1.2L2.2,9.1C1.8,8.3,1.5,7.5,1.5,6.5z M6.5,11.5c-1.2,0-2.3-0.5-3.2-1.2L10.8,4c0.4,0.7,0.7,1.6,0.7,2.5C11.5,9.3,9.3,11.5,6.5,11.5z"/>\n  </svg>'},hamburger:function(){return'\n    <svg class="cs-icon" width="30px" height="12px" viewBox="0 10 30 12" focusable="false">\n      <path transform="translate(0, 1)" d="M0,15 L17,15 L17,17 L0,17 L0,15 Z M0,11 L17,11 L17,13 L0,13 L0,11 Z M0,19 L17,19 L17,21 L0,21 L0,19 Z" ></path>\n    </svg>\n  '},file:function(){return'\n    <svg width="20px" height="27px" viewBox="0 0 40 50" role="presentation" focusable="false">\n      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">\n        <g>\n          <path class="file-icon-bg" d="M2.00804893,0 C0.899034128,0 0,0.889064278 0,1.99091407 L0,48.0090859 C0,49.1086374 0.892756032,50 1.99862555,50 L37.2170607,50 C38.3208711,50 39.2156863,49.1011186 39.2156863,47.993136 L39.2156863,13.6363636 L26.1437908,0 L2.00804893,0 Z"></path>\n          <path class="file-icon-fold" d="M26.1437908,0 L26.1437908,11.7296861 C26.1437908,12.8319383 27.0422752,13.7254902 28.1433598,13.7254902 L39.2156863,13.7254902"></path>\n        </g>\n      </g>\n    </svg>'},link:function(e){return'\n  <svg class="link-icon" preserveAspectRatio="none" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" role="presentation" focusable="false">\n      <path fill="'.concat(e,'" stroke="none" d="\n            M 1.45 8.5\n            Q 0.0453125 10.0015625 0 11.9\n            L 0 12.15\n            Q 0.0453125 14.0484375 1.45 15.55\n            L 1.5 15.6\n            Q 3.0015625 17.0046875 4.85 17.05\n            L 5.1 17.05\n            Q 7.0150390625 17.0046875 8.5 15.6\n            L 10.65 13.45\n            Q 10.95 13.15 10.95 12.75 10.95 12.35 10.65 12.05 10.3689453125 11.7689453125 10 11.75\n            L 9.95 11.75\n            Q 9.55 11.75 9.2 12.05\n            L 7.1 14.15\n            Q 6.2 15.05 5 15.05 3.8 15.05 2.9 14.15 2 13.25 2 12.05 2 10.8826171875 2.85 9.95\n            L 5 7.8\n            Q 5.3 7.5 5.3 7.1\n            L 5.3 7.05\n            Q 5.2810546875 6.6810546875 5 6.4 4.7 6.1 4.3 6.1 3.9 6.1 3.55 6.4\n            L 1.45 8.5\n            M 12.05 5\n            Q 11.75 4.7 11.35 4.7 10.95 4.7 10.65 5\n            L 5 10.65\n            Q 4.7 10.95 4.7 11.35 4.7 11.75 5 12.05 5.3 12.35 5.7 12.35 6.1 12.35 6.4 12.05\n            L 12.05 6.4\n            Q 12.35 6.1 12.35 5.7 12.35 5.3 12.05 5\n            M 15.6 1.5\n            L 15.55 1.45\n            Q 14 0 12.05 0\n            L 12 0\n            Q 10.05 0 8.5 1.45\n            L 6.4 3.55\n            Q 6.1 3.9 6.1 4.3 6.1 4.7 6.4 5 6.7 5.3 7.1 5.3 7.5 5.3 7.8 5\n            L 9.95 2.85\n            Q 10.8826171875 2 12.05 2 13.25 2 14.15 2.9 15.05 3.8 15.05 5 15.05 6.2 14.15 7.1\n            L 12.05 9.2\n            Q 11.75 9.55 11.75 9.95 11.75 10.35 12.05 10.65 12.35 10.95 12.75 10.95 13.15 10.95 13.45 10.65\n            L 15.6 8.5\n            Q 17.05 6.96875 17.05 5 17.05 3.05 15.6 1.5 Z"/>\n          </svg>')},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},playbackSpeed:function(){return'\n    <svg class="cs-icon" width="15" height="15" viewBox="0 0 15 15" focusable="false">\n      <path d="M5.9 4.0L10.4 7.4L5.9 10.8V4.0ZM1.5 8.2H0.0C0.1 9.6 0.6 10.9 1.5 12.0L2.6 11.0C2.0 10.1 1.6 9.2 1.5 8.2H1.5ZM15 7.4H14.9C14.9 5.6 14.3 3.8 13.0 2.4C11.8 1.0 10.0 0.1 8.2 0.0V1.5C10.1 1.7 11.8 2.9 12.8 4.7C13.7 6.4 13.7 8.5 12.8 10.2C11.8 12.0 10.1 13.1 8.2 13.4V14.9C10.0 14.7 11.8 13.8 13.0 12.5C14.3 11.1 14.9 9.3 14.9 7.4L15 7.4ZM3.6 12.1L2.5 13.1C3.7 14.1 5.1 14.8 6.7 14.9V13.4V13.4C5.5 13.3 4.5 12.8 3.6 12.1V12.1ZM2.6 3.9L1.5 2.8C0.6 3.9 0.1 5.3 0 6.7H1.5H1.5C1.6 5.7 2.0 4.7 2.6 3.9H2.6ZM6.7 1.5V0.0C5.1 0.1 3.7 0.7 2.5 1.7L3.6 2.8C4.5 2.1 5.5 1.6 6.7 1.5V1.5Z" stroke="none" />\n    </svg>\n  '},track:function(e,t){return'\n    <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n      <defs>\n        <rect id="'.concat(t,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n        <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(t,'-trackFilter">\n          <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n          <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n          <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n          <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n        </filter>\n      </defs>\n      <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle class="thumb" stroke-width="0" cx="8" cy="8" r="6"></circle>\n        </g>\n      </g>\n      <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle fill="').concat(e,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n        </g>\n      </g>\n    </svg>\n  ')},downArrow:function(e,t){return'\n    <div style="height: 100%; width: 100%; background-color: '.concat(e,"; border-right: 1px solid; border-bottom: 1px solid; border-color: ").concat(t,'; border-bottom-right-radius: 3px; transform: rotate(45deg);" />\n    ')},checkmark:function(){return'<svg  class="cs-icon check-icon" focusable="false" width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n          <polygon style="fill:currentColor !important" points="12.04 4 13.45 5.41 6.25 12.62 2 8.36 3.41 6.95 6.25 9.79"></polygon>\n      </g>\n    </svg>'},lock:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 Z"></path>\n        </g>\n    </g>\n</svg>'},lockedViewed:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M7.11248548,6.17405922 C7.38175483,6.47268397 7.38175483,6.92847965 7.0966461,7.21138731 L4.53066757,9.75755627 C4.38811321,9.8990101 4.19804072,9.96187847 4.02380761,9.96187847 C3.83373513,9.96187847 3.65950202,9.8990101 3.51694766,9.75755627 L2.34544071,8.45018243 C2.06033199,8.16727477 2.06033199,7.71147909 2.34544071,7.42857143 C2.63054944,7.14566377 3.08989127,7.14566377 3.375,7.42857143 L4.02380761,8.21728122 L6.08292619,6.17405922 C6.36803491,5.89115155 6.82737675,5.89115155 7.11248548,6.17405922 Z M4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 Z"></path>\n        </g>\n    </g>\n</svg>'}},ka={play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="14" height="16" viewBox="0 0 14 16" focusable="false">\n    <path d="M1.4 15.4C0.8 15.8 0 15.3 0 14.5L0 1.4C0 0.6 0.8 0.1 1.4 0.5L12.9 7.1C13.5 7.5 13.5 8.4 12.9 8.8L8.0 11.6L1.4 15.4Z" stroke="none" />\n  </svg>'}},Ca=function(e){return DS.detection.env.isPerpetual?Sa[e]||wa:ka[e]||Sa[e]||wa};function Oa(e){return Oa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Oa(e)}function Ea(e){return function(e){if(Array.isArray(e))return xa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Ta(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function La(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return l}}(e,t)||Ta(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ta(e,t){if(e){if("string"==typeof e)return xa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?xa(e,t):void 0}}function xa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Pa(e,t){return Pa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Pa(e,t)}function Ia(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Aa(e);if(t){var i=Aa(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return _a(this,n)}}function _a(e,t){if(t&&("object"===Oa(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ja(e)}function ja(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Aa(e){return Aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Aa(e)}function Da(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Oa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Oa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Oa(o)?o:String(o)),r)}var i,o}function Ba(e,t,n){return t&&Da(e.prototype,t),n&&Da(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ra(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ma=function(e){return ga(e.slideid)},Na=DS,Ha=Na.windowManager,Fa=Na.focusManager,Va=Na.TweenLite,Ua=Na.events,Wa=Na.pubSub,Za=Na.keyManager,za=Na.resolver,Ka=Na.dom,Ga=(Ka.hasClass,Ka.removeClass),Qa=Ka.addClass,qa=Ka.getParentWithClass,Ya=Na._,Xa=Ya.bindAll,$a=(Ya.first,Na.utils),Ja=$a.stripTags,el=($a.stripPlayer,$a.prefixWithPlayer),tl=Na.globalEventHelper,nl=tl.addDocumentListener,rl=(tl.removeDocumentListener,Na.detection),il=rl.theme,ol=rl.os,al=Ba((function e(t,n,r){Ra(this,e),this.slideId=t,this.title=n,this.text=r})),ll=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pa(e,t)}(n,e);var t=Ia(n);function n(e){var r;Ra(this,n),r=t.call(this,e),Xa(ja(r),"traverseLinks","performSearch","onSlideChanged","onClear","onReset","loadSlideBankSearchData"),r.animationDuration=.2;var i=z.model.frame.notesData||[];return r.notesBySlideId=i.map((function(e){return new al(el(e.slideId),DS.utils.getPath(za.resolvePath(e.slideId,DS.presentation),"title"),Ja(e.content).trim())})),r.slideTextData=z.model.frame.navData.search||[],r.slideTextBySlideId=r.slideTextData.filter((function(e){return!e.slidebank})).map((function(e){return new al(el(e.slideid),DS.utils.getPath(za.resolvePath(e.slideid,DS.presentation),"title"),Ja(e.Text).trim())})),r.menuOptions=z.model.frame.controlOptions.menuOptions,r.wrapListItems=r.menuOptions.wrapListItems,r.links=z.model.frame.navData.outline.links,r.hasSlideBankSlides=r.slideTextData.some((function(e){return e.slidebank})),r.hasSlideBankSlides&&Wa.on(Ua.player.RESET,r.onReset),null!=r.clearEl&&r.onClickEl(r.clearEl,r.onClear),Wa.on(Ua.search.CLEAR,r.onClear),r.onClickEl(r.searchFilterEl,r.onToggleSearchOptions),r.onClick(r.onClickLink),Wa.on(Ua.window.MAIN_CHANGED,r.onSlideChanged),r}return Ba(n,[{key:"loadSlideBankSearchData",value:function(){this.slideBankTextBySlideId=this.slideTextData.filter((function(e){return e.slidebank})).reduce((function(e,t){var n=za.getSlideBankSlideInstance(t.slideid);return null!==n&&e.push(new al(n.absoluteId,n.title(),Ja(t.Text))),e}),[])}},{key:"teardown",value:function(){Wa.off(Ua.window.MAIN_CHANGED,this.onSlideChanged),window.globals.HAS_SLIDE&&Wa.off(Ua.search.CLEAR,this.onClear)}},{key:"onSlideChanged",value:function(e){var t=this.resultsEl.querySelector(".cs-selected"),n=this.resultsEl.querySelector('[data-slide-id="'.concat(e.absoluteId,'"]'));null!=t&&Ga(t,"cs-selected"),null!=n&&(Qa(n,"cs-selected"),Qa(n,"cs-viewed"),this.updateAriaLabel(n))}},{key:"onReset",value:function(){this.onClear(),this.loadSlideBankSearchData()}},{key:"onFocus",value:function(e){e.relatedTarget;var t=e.target;if(this.el.contains(t)){if(t===this.searchFilterEl||t===this.clearEl)return Fa.setFocusRectOn(t),this.isFocused=!1,!1;if(t===this.notesCheckEl||t===this.slideCheckEl)return Fa.setFocusRectOn(t.parentElement),this.isFocused=!1,!1;if(!this.isFocused&&(!il.isUnified||Za.isShowFocus)){var n=La(this.getItems(),1)[0];this.isFocused=!0,null!=n&&(this.currentItem=n,this.lastSelected!==this.currentItem&&this.focusOnCurrent(),nl("keydown",this.onKeydown))}}return!1}},{key:"getIsSearchVisible",value:function(){return this.searchOptionsEl.classList.contains("visible")}},{key:"setCheckboxesVisible",value:function(e){this.notesCheckEl.hidden=e,this.slideCheckEl.hidden=e}},{key:"onToggleSearchOptions",value:function(e){var t=this,n=this.getIsSearchVisible(),r=n?"remove":"add";this.searchOptionsEl.classList[r]("visible"),n?(clearTimeout(this.checkBoxTimeout),this.checkBoxTimeout=setTimeout((function(){t.setCheckboxesVisible(n)}),200)):this.setCheckboxesVisible(n)}},{key:"onClickLink",value:function(e){if("locked"!==this.menuOptions.flow){var t=qa(e.target,"listitem"),n=t.dataset.slideId;null==n||"restricted"===this.menuOptions.flow&&!e.target.classList.contains("cs-viewed")||(Qa(t,"cs-viewed"),this.updateAriaLabel(t),Wa.trigger(Ua.request.NEXT_SLIDE,n))}}},{key:"updateAriaLabel",value:function(e){il.isUnified&&Ea(e.querySelector(".outline-states").children).forEach((function(t){if("none"===window.getComputedStyle(t).display);else{var n=t.getAttribute("aria-label"),r=e.getAttribute("data-slide-title");e.children[0].textContent=r+" "+n}}))}},{key:"traverseLinks",value:function(e){for(var t=0;t<e.length;t++){var n=e[t];Ma(n)||this.searchResults.has(n.slideid)||(this.noSearchTerm||n.displaytext.toLowerCase().indexOf(this.term)>=0)&&this.searchResults.set(n.slideid,n.displaytext),null!=n.links&&this.traverseLinks(n.links)}}},{key:"getDefaultSate",value:function(e){var t=Ha.getCurrentWindow().getCurrentSlide().absoluteId===e||za.resolvePath(e).viewed,n=this.menuOptions.flow;if(t)switch(n){case"free":case"restricted":return z.model.getString("acc_visited");case"locked":return"".concat(z.model.getString("acc_visited"),", ").concat(z.model.getString("acc_locked"))}else switch(n){case"free":return"";case"restricted":case"locked":return z.model.getString("acc_locked")}}},{key:"performSearch",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(window.globals.HAS_SLIDE){this.term=t.toLowerCase(),this.items=null,this.noSearchTerm=""===t,this.searchResults=new Map,this.resultsEl.innerHTML="",this.slideTextEnabled=this.slideCheckEl.checked,this.notesEnabled=this.notesCheckEl.checked;var n=z.model.frame.navData.links;null!=n&&this.traverseLinks(n),this.hasSlideBankSlides&&void 0===this.slideBankTextBySlideId&&this.loadSlideBankSearchData();var r=function(t){t.forEach((function(t){(t.text.toLowerCase().indexOf(e.term)>=0||t.title.toLowerCase().indexOf(e.term)>=0)&&e.searchResults.set(t.slideId,t.title)}))},i=!this.noSearchTerm;this.slideTextEnabled&&i&&r(this.slideTextBySlideId),this.hasSlideBankSlides&&i&&r(this.slideBankTextBySlideId),this.notesEnabled&&i&&r(this.notesBySlideId);ol.isIOS;var o=this.wrapListItems?"":"no-wrap",a=Ha.getCurrentWindow().getCurrentSlide().absoluteId,l=0;this.searchResults.forEach((function(t,n){0;var r=a===n?"cs-selected":"",i=document.createElement("li"),s=za.resolvePath(n).viewed?"cs-viewed":"",c=e.menuOptions.flow,u=e.getDefaultSate(n),f='\n      <div role="menuitem" class="cs-listitem listitem '.concat(r," ").concat(o," ").concat(s," ").concat("free"!==c&&"cs-"+c,'"\n           data-slide-id="').concat(n,'"\n           data-slide-title="').concat(t,'"\n           tabindex="').concat(l,'"\n           role = "treeitem">\n          <span style="position: absolute; opacity: 0;">').concat(t," ").concat(u,"</span>");il.isUnified?f+='\n           <span class="linkText" aria-hidden="true">\n             '.concat(t,'\n           </span>\n           <span class="outline-states" aria-hidden="true">\n             <span class="visitedIcon" aria-label="').concat(z.model.getString("acc_visited"),'">\n               ').concat(Ca("checkmark")(),'\n             </span>\n             <span class="lockedIcon" aria-label="').concat(z.model.getString("acc_locked"),'">\n               ').concat(Ca("lock")(),'\n             </span>\n             <span class="lockedViewedIcon" aria-label="').concat(z.model.getString("acc_visited"),", ").concat(z.model.getString("acc_locked"),'">\n               ').concat(Ca("lockedViewed")(),"\n             </span>\n           </span>\n        </div>\n      "):f+="\n             ".concat(t,"\n        </div>"),i.innerHTML=f,e.resultsEl.appendChild(i),l=-1}))}}},{key:"onClear",value:function(){var e=this,t=this.view.parent.children,n=t.outline,r=t.search;this.outlineUl=this.outlineUl||document.querySelector("#outline-content ul"),n.setVisibility(!0),Va.to(this.el,this.animationDuration,{alpha:0,onComplete:function(){Va.to(e.outlineUl,e.animationDuration,{alpha:1}),e.view.setVisibility(!1),r.children.bottomDiv.el.style.opacity=1,Za.isShowFocus&&n.el.focus()}}),this.term="",r.children.searchInput.el.value="",Ga(document.body,"search-results-active"),Wa.trigger(Ua.search.UPDATE_PANEL)}},{key:"hasListItems",value:function(){return!_.isEmpty(this.getItems())}},{key:"getItems",value:function(){return this.items=this.items||Ea(this.el.querySelectorAll(".cs-listitem")),this.items}},{key:"activateItem",value:function(){this["locked"!==this.menuOptions.flow?"onClickLink":"onCarrotClick"]({target:this.currentItem})}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"onAfterVisible",value:function(){this.view.parent.children.outline.setVisibility(!1)}}]),n}(va),sl=ll;function cl(e){return cl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cl(e)}function ul(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==cl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==cl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===cl(o)?o:String(o)),r)}var i,o}function fl(){return fl="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=dl(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},fl.apply(this,arguments)}function dl(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=vl(e)););return e}function hl(e,t){return hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},hl(e,t)}function pl(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=vl(e);if(t){var i=vl(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return yl(this,n)}}function yl(e,t){if(t&&("object"===cl(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return bl(e)}function bl(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vl(e){return vl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vl(e)}var ml=DS,gl=ml.pubSub,wl=ml.events,Sl=ml.utils.toSeconds,kl=ml.constants.MOBILE_ANIMATION_DURATION,Cl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hl(e,t)}(o,e);var t,n,r,i=pl(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),_.bindAll(bl(t),"onClear"),t.animationDuration=Sl(kl),gl.on(wl.sidebar.HIDE_SEARCH,t.onClear),t}return t=o,(n=[{key:"onClear",value:function(){fl(vl(o.prototype),"onClear",this).call(this)}},{key:"onClickLink",value:function(e){fl(vl(o.prototype),"onClickLink",this).call(this,e),gl.trigger(wl.tab.HIDE),gl.trigger(wl.sidebar.CLOSE),gl.trigger(wl.topEllipsesPanel.HIDE)}}])&&ul(t.prototype,n),r&&ul(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(sl);function Ol(e){return Ol="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ol(e)}function El(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ol(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Ol(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ol(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ll="searchResults",Tl=z.def(Ll,Cl,(function(e){var t,n=z.model,r=n.rtl?"rtl":"";return El(t={attrs:{id:"searchResults-content",class:"cs-menu cs-panel panel".concat(r),tabindex:-1},z:2,x:0,y:0,w:"100%"},"y",(function(){return 48})),El(t,"h",(function(){return this.parent.h})),El(t,"visible",!1),El(t,"html",'\n     <div class="outline-title">'.concat(n.title.text,'</div>\n      <span class="cs-outline search-content">\n        <div style="display:none">\n          <span class="flex-static-auto">\n            <h4 data-ref="title" class=\'cs-heading search-heading panel-section-heading\'>\n              ').concat(n.getString("search_results"),'\n            </h4>\n            <button class="btn-unstyled search-filter cs-search-filter" tabindex="0" data-ref="searchFilter">\n              <span>').concat(n.getString("filter"),"</span>\n              ").concat(sn("filter")(),'\n            </button>\n          </span>\n          \n          <div class="search-options flex-static-auto" data-ref="searchOptions">\n            <p>').concat(n.getString("search_in"),'</p>\n            <label>\n              <input data-ref="notesCheck" type="checkbox" checked>\n              <span>').concat(n.getString("transcript_chk"),'</span>\n            </label>\n            <label>\n              <input data-ref="slideCheck" type="checkbox" checked>\n              <span>').concat(n.getString("slide_text_chk"),'</span>\n            </label>\n          </div>\n        </div>\n        <div class="search-results is-scrollable" tabindex="0" data-ref="searchResults">\n          <ul data-ref="results"></ul>\n        </div>\n       \n        <button data-ref="clear" class="btn search-clear cs-button flex-static-auto">\n          <span>').concat(n.getString("clear"),"</span>\n          ").concat(sn("clear")(),"\n        </button>\n      </span>\n    ")),t}));function xl(e){return xl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xl(e)}function Pl(e){return function(e){if(Array.isArray(e))return Il(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Il(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Il(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Il(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==xl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==xl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===xl(o)?o:String(o)),r)}var i,o}function jl(){return jl="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=Al(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},jl.apply(this,arguments)}function Al(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Nl(e)););return e}function Dl(e,t){return Dl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Dl(e,t)}function Bl(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Nl(e);if(t){var i=Nl(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Rl(this,n)}}function Rl(e,t){if(t&&("object"===xl(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ml(e)}function Ml(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nl(e){return Nl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Nl(e)}var Hl,Fl=DS,Vl=Fl.detection,Ul=Fl.pubSub,Wl=Fl._,Zl=Fl.events,zl=Fl.keyManager,Kl=Fl.resolver,Gl=Fl.windowManager,Ql=Fl.utils.prefixWithPlayer,ql=Fl.focusManager.setFocusRectOn,Yl=Fl.globalEventHelper.addDocumentListener,Xl=Fl.dom,$l=Xl.parentNodesOf,Jl=Xl.getParentWithClass,es=Xl.hasClass,ts=Xl.addClass,ns=Xl.removeClass,rs=function(e){return $l(e,(function(e){return"li"===e.nodeName.toLowerCase()}))},is=function(e){return rs(e).slice(1).some((function(e){return es(e,"item-collapsed")}))},os=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Dl(e,t)}(o,e);var t,n,r,i=Bl(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Wl.bindAll(Ml(t),"updateVisitedSlides","onSlideChanged","onSelectFirstSlideLink","addSlideToVisited","visuallyUpdateLinks","collapseLastItem","onClickLink","onCarrotClick","onClickItem","centerOnFocused","setDrawSlides","mouseDown","mouseUp"),t.visitedSlides=new Set,t.menuOptions=z.model.frame.controlOptions.menuOptions,Ul.once(Zl.resume.SET_DATA,t.updateVisitedSlides),Ul.on(Zl.window.MAIN_CHANGED,t.onSlideChanged),Ul.on(Zl.mobile.OUTLINE_SHOWN,t.centerOnFocused),Ul.on(Zl.navData.SELECT_FIRST_SLIDE_LINK,t.onSelectFirstSlideLink),t.removeDocListeners=Wl.flow(Yl("mousedown",t.mouseDown),Yl("mouseup",t.mouseUp)),null!=DS.presentation.getDrawPromise&&DS.presentation.getDrawPromise().then(t.setDrawSlides),t.isTopTabChild=z.model.topTabs.some((function(e){return"outline"===e.name})),Ul.on(Zl.navData.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,n=[{key:"teardown",value:function(){Ul.off(Zl.window.MAIN_CHANGED,this.onSlideChanged),Ul.off(Zl.mobile.OUTLINE_SHOWN,this.centerOnFocused),this.removeDocListeners()}},{key:"focusSelf",value:function(){this.getItems().find((function(e){return 0===e.parentNode.tabIndex})).parentNode.focus()}},{key:"onFocus",value:function(){var e=this;this.isMouseDown||(null!=this.view.parent.children.search&&Vl.theme.isUnified?setTimeout((function(){e.el.contains(document.activeElement)&&jl(Nl(o.prototype),"onFocus",e).call(e)}),500):jl(Nl(o.prototype),"onFocus",this).call(this))}},{key:"mouseDown",value:function(){this.isMouseDown=!0}},{key:"mouseUp",value:function(){this.isMouseDown=!1}},{key:"onClickItem",value:function(e){var t=this;if(Vl.theme.isUnified){var n=Jl(e.target,"listitem");es(e.target,"carrot")&&(t.onCarrotClick(n),1)||n&&es(n.parentNode,"item-collapsible")&&(t.onCarrotClick(n),!es(n,"is-promoted-slide"))||"locked"!==t.menuOptions.flow&&t.onClickLink(n)}else("locked"!==this.menuOptions.flow?this.onClickLink:this.onCarrotClick)(e.target)}},{key:"visuallyUpdateLinks",value:function(){var e=this;this.visitedSlides.forEach((function(t){var n=e.view.children[t];null!=n&&(Vl.theme.isUnified?e.updateViewedState(n.el):ts(n.el,"cs-viewed"))})),Vl.theme.isUnified&&(this.setComplete("div.is-promoted-slide"),this.setComplete("div.is-scene",1))}},{key:"setComplete",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.el.querySelectorAll(e);Array.from(r).forEach((function(e){Array.from(e.parentNode.querySelectorAll("div")).slice(n).every((function(e){return es(e,"cs-viewed")}))&&(ts(e,"cs-complete"),t.updateAriaLabel(e))}))}},{key:"updateVisitedSlides",value:function(e,t){null!=e&&(e.forEach(this.addSlideToVisited),this.visuallyUpdateLinks())}},{key:"getNextItem",value:function(e){var t,n=this,r=this.getItems(),i=r.findIndex((function(t){return n.findIndexCb(t,e)}));do{(i+=1)===r.length&&(i=0),t=r[i]}while(is(t));return t}},{key:"getPrevItem",value:function(e){var t,n=this,r=this.getItems(),i=r.findIndex((function(t){return n.findIndexCb(t,e)}));do{-1==(i-=1)&&(i=r.length-1),t=r[i]}while(is(t));return t}},{key:"addSlideToVisited",value:function(e){var t=e.absoluteId,n=e.getScene().absoluteId;this.visitedSlides.add(n).add(t)}},{key:"isCurrentLinkParent",value:function(e,t){return e.el===this.view.children[t].el&&e.el.getAttribute("data-has-links")&&!e.el.getAttribute("data-is-scene")}},{key:"onSelectFirstSlideLink",value:function(){var e=this.view.el.querySelector("ul > li > div:not(.is-scene)");if(null!=e){var t=e.getAttribute("data-ref");this.selectLink(e,t)}}},{key:"onSlideChanged",value:function(e){if(this.addSlideToVisited(e),!this.currentItem||Jl(this.currentItem,"listitem").getAttribute("data-ref")!=e.absoluteId){var t=this.view.children[e.absoluteId];null!=t&&this.selectLink(t.el,e.absoluteId)}}},{key:"selectLink",value:function(e,t){var n=this;this.onClickLink(e,!0),$l(e,(function(r){r.classList.contains("item-collapsed")&&(n.isCurrentLinkParent(e,t)||(ns(r,"item-collapsed"),r.setAttribute("aria-expanded",!0)))}),(function(e){return n.el===e}))}},{key:"collapsibleParents",value:function(e){var t=this;return $l(e,(function(e){return e.classList.contains("item-collapsible")}),(function(e){return t.el===e}))}},{key:"collapseLastItem",value:function(e){var t=this;null!=this.lastExpandedEls&&this.menuOptions.autocollapse&&this.lastExpandedEls.forEach((function(n){n.contains(e)||es(n,"item-collapsed")||t.toggleScene(n)}))}},{key:"toggleScene",value:function(e){var t,n=es(e,"item-collapsed");if(t=n?"remove":"add",e.classList[t]("item-collapsed"),e.setAttribute("aria-expanded",n),Vl.theme.isUnified){var r=Array.from(e.querySelectorAll("div.listitem")),i=r[0];!function(e,t){if(!n){var r=e.slice(1).filter((function(e){return!es(e,"is-promoted-slide")&&!es(e,"is-scene")}));r.some((function(e){return es(e,"cs-viewed")}))&&ns(t,"cs-unvisited"),r.every((function(e){return es(e,"cs-viewed")}))&&ts(t,"cs-complete")}}(r,i),this.updateAriaLabel(i)}Ul.trigger(Zl.menuLinksListItem.TOGGLE)}},{key:"updateAriaLabel",value:function(e){var t=[].slice.call(e.querySelector(".outline-states").children),n=e.getAttribute("data-slide-title"),r=e.children[0];t.some((function(e){if("none"!==window.getComputedStyle(e).display){var t=e.getAttribute("aria-label");return r.textContent=n+" "+t,!0}}))||(r.textContent=n)}},{key:"onCarrotClick",value:function(e){if(null!==e){var t=Jl(e,"item-collapsible");this.toggleScene(t)}}},{key:"onClickLink",value:function(e,t){var n=this;if(e)if(Vl.theme.isUnified){var r=e.getAttribute("data-ref"),i=null!=r;if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(r)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,ns(this.currentItem,"hover")),this.currentItem=e,null!=r&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),this.lastExpandedEls.forEach((function(e){var t=e.firstElementChild;r.includes(t.getAttribute("data-ref"))&&(ts(t,"cs-viewed"),n.updateAriaLabel(t))}))),es(e,"listitem")){this.updateViewedState(e);var o=this.el.querySelector(".cs-selected");es(e,"is-scene")||(null!=o&&(ns(o,"cs-selected"),this.updateAriaLabel(o)),ts(e,"cs-selected"),window.requestAnimationFrame((function(){return n.updateAriaLabel(e)})))}else this.onCarrotClick(e);if(i&&!t){var a=Gl.getCurrentWindowSlide().absoluteId;r!=a&&(Ul.trigger(Zl.request.NEXT_SLIDE,r),Vl.theme.isUnified&&Ul.trigger(Zl.topEllipsesPanel.HIDE)),Vl.deviceView.isMobile&&(Ul.trigger(Zl.tab.HIDE),(Vl.deviceView.isPhone||Vl.theme.isClassic)&&Ul.trigger(Zl.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&ql(this.currentItem)}}else this.onClickLinkOld(e,t)}},{key:"onClickLinkOld",value:function(e,t){var n=e.getAttribute("data-ref"),r=null!=n,i=ga(n);if("UL"!==e.nodeName)if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(n)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,ns(this.currentItem,"hover")),this.currentItem=e,(i||es(e.parentNode,"item-collapsible"))&&this.toggleScene(e.parentNode),null!=n&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),Array.from(this.lastExpandedEls).forEach((function(e){var t=e.firstElementChild;n.includes(t.getAttribute("data-ref"))&&ts(t,"cs-viewed")}))),es(e,"listitem")){ts(e,"cs-viewed");var o=this.el.querySelector(".cs-selected");null!=o&&ns(o,"cs-selected"),ts(e,"cs-selected")}else this.onCarrotClick(e);if(r&&!t){var a=Gl.getCurrentWindowSlide().absoluteId;i||n==a||(Ul.trigger(Zl.request.NEXT_SLIDE,n),Vl.theme.isUnified&&Ul.trigger(Zl.topEllipsesPanel.HIDE)),Vl.deviceView.isMobile&&(Ul.trigger(Zl.tab.HIDE),(Vl.deviceView.isPhone||Vl.theme.isClassic)&&Ul.trigger(Zl.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&ql(this.currentItem)}else es(e,"carrot")&&this.onCarrotClick(e)}},{key:"updateViewedState",value:function(e){ts(e,"cs-viewed");var t=rs(e).pop();if(!es(e,"is-scene")&&null!=t){var n=t.querySelector("div.listitem");ns(n,"cs-unvisited")}this.updateAriaLabel(e)}},{key:"isExpanded",value:function(){return!!this.currentItem.dataset.hasLinks&&es(this.parentElement,"item-collapsed")}},{key:"onKeydown",value:function(e){var t=this.currentItem;if(zl.isActionKey(e.which))this.activateItem(),zl.isSpaceKey(e.which)&&e.preventDefault();else if(zl.isRightKey(e.which)&&t.dataset.hasLinks)es(this.currentItem.parentNode,"item-collapsed")?this.onCarrotClick(this.currentItem.firstElementChild):this.currentItem=this.getNextItem(this.getItemContent());else if(zl.isDownKey(e.which))this.currentItem=this.getNextItem(this.getItemContent());else if(zl.isUpKey(e.which))this.currentItem=this.getPrevItem(this.getItemContent());else if(zl.isLeftKey(e.which))if(t.dataset.hasLinks&&!es(t.parentNode,"item-collapsed"))this.onCarrotClick(this.currentItem.firstElementChild);else{var n=Jl(this.currentItem,"item-collapsible");null!=n&&(this.currentItem=n.querySelector(".cs-listitem"))}else zl.isHomeKey(e.which)?this.currentItem=this.getFirstItem():zl.isEndKey(e.which)&&(this.currentItem=this.getLastItem());t!==this.currentItem&&(e.preventDefault(),ns(t,"hover"),t.parentNode.tabIndex=-1,this.focusOnCurrent())}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem.parentNode&&(this.currentItem.parentNode.tabIndex=0,ts(this.currentItem,"hover"),this.currentItem.parentNode.focus()),ql(this.currentItem)}},{key:"hasListItems",value:function(){return!Wl.isEmpty(z.model.frame.navData.outline.links)}},{key:"getItems",value:function(){return this.links=this.links||Pl(this.el.querySelectorAll(".cs-listitem")),this.links}},{key:"activateItem",value:function(){var e="locked"!==this.menuOptions.flow&&null==this.currentItem.dataset.isScene,t=e?"onClickLink":"onCarrotClick",n=e?this.currentItem:this.currentItem.querySelector(".carrot");this[t](n)}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"getTreeRootEl",value:function(){return this.el.querySelector("ul")}},{key:"getOffsetEl",value:function(){return Vl.deviceView.isMobile?this.el.parentNode:this.el}},{key:"getOffsetTop",value:function(e){return Vl.deviceView.isMobile?Wl.first($l(e,(function(e){return"li"===e.nodeName.toLowerCase()}))).offsetTop:jl(Nl(o.prototype),"getOffsetTop",this).call(this,e)}},{key:"scrollToCurrent",value:function(){var e=this;Vl.deviceView.isMobile&&(this.currentItem=this.getItems().find((function(t){return t.getAttribute("data-ref")===e.currentSlideId}))),jl(Nl(o.prototype),"scrollToCurrent",this).call(this)}},{key:"setDrawSlides",value:function(){var e=this;this.view.draws.forEach((function(t){var n=t.link,r=t.links,i=Kl.resolvePath(Ql(n.drawid)),o=function(){var t=n.spliceNum||1,o=i.slides();r.splice.apply(r,[n.index,t].concat(Pl(o.map(e.createNewLink)))),n.spliceNum=o.length,e.view.updateHtml(),e.view.initChildRefs()};i.on(Zl.draw.RESET_COMPLETE,o),null!=i.slides()&&o()}))}},{key:"createNewLink",value:function(e){var t={kind:"slidelink",expand:!1,type:"slide"};return t.slideid=e.absoluteId,t.slidetitle=t.displaytext=e.get("title"),t}}],n&&_l(t.prototype,n),r&&_l(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(va),as=os,ls=DS,ss=ls.pubSub,cs=ls.events,us=ls.constants.MOBILE_ANIMATION_DURATION,fs="outline",ds=function(e){for(var t="";e;)t=e.index+1+"."+t,e=null!=e.parent&&"slidelink"===e.parent.kind&&e.parent;return t+" "},hs=z.def(fs,as,(function(e){var t=z.model,n=z.model.rtl,r=(z.getNamespace(DS.constants.refs.FRAME),n?"rtl":""),i=t.frame.controlOptions.menuOptions,o=i.wrapListItems,a=i.autonumber,l=t.frame.controlOptions.controls.search,s=0;return l&&(ss.on(cs.sidebar.SHOW_SEARCH,(function(){s=48,Hl.update()})),ss.on(cs.sidebar.HIDE_SEARCH,(function(){setTimeout((function(){s=0,Hl.update()}),us)}))),{tag:"nav",attrs:{id:"outline-content",class:"cs-menu cs-panel is-scrollable cs-outline panel ".concat(r),tabindex:-1},w:"100%",y:function(){return s},h:function(){var e=this.parent.h;return e-s},overflow:"auto",draws:[],updateHook:function(){Hl=this},html:function(){var e=this,r=t.frame.navData.outline.links||[];this.el.depth=0,this.el.innerHTML='\n        <div class="outline-title">'.concat(t.title.text,"</div>\n      "),function t(r,i,l){var s=document.createElement("ul");s.depth=i.depth+1,i.appendChild(s);for(var c=0;c<r.length;c++){var u=document.createElement("li"),f=r[c];f.parent=l,f.index=c,u.setAttribute("tabindex",-1),s.appendChild(u),u.depth=s.depth;var d=null!=f.links,h=15*u.depth+5,p=f.slideid;null==p&&e.draws.push({link:f,links:r,i:c});var y=ga(p);u.innerHTML='\n            <div\n              class="cs-listitem listitem '.concat(o?"":"no-wrap",'"\n              style="padding-').concat(n?"right":"left",": ").concat(h,'px;"\n              data-ref="').concat(p,'"\n              data-slide-title="').concat(r[c].displaytext,'"\n              ').concat(y?'data-is-scene="true"':"","\n              ").concat(d?'data-has-links="true"':"",'\n              tabIndex="-1"\n              >\n\n               ').concat(a?ds(f):""," ").concat(f.displaytext,"\n            </div>\n          "),d&&(u.classList.add("item-collapsible"),f.expand||u.classList.add("item-collapsed"),t(f.links,u,f))}}(r,this.el)}}}));function ps(e){return function(e){if(Array.isArray(e))return ys(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ys(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ys(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ys(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var bs="outlineSearch",vs=z.def(bs,(function(){var e=z.model,t=e.frame,n=e.rtl,r=t.controlOptions.controls.search,i=n?"rtl":"";return{attrs:{id:"".concat(bs,"-content"),class:"outline-search ".concat(i),tabindex:-1},overflow:"scroll",w:"100%",h:"100%",model:t,childViews:[hs].concat(ps(r?[Ko,Tl]:[]))}}));function ms(e){return ms="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ms(e)}function gs(e){return function(e){if(Array.isArray(e))return ws(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ws(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ws(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ws(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ss(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ms(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ms(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ms(o)?o:String(o)),r)}var i,o}function ks(e,t){return ks=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ks(e,t)}function Cs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Es(e);if(t){var i=Es(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Os(this,n)}}function Os(e,t){if(t&&("object"===ms(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Es(e){return Es=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Es(e)}var Ls=DS,Ts=(Ls._,Ls.focusManager),xs=Ls.detection,Ps=Ls.pubSub,Is=Ls.events,_s=Ls.dom,js=_s.addClass,As=_s.removeClass,Ds=(Ls.keyManager.isTabKey,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ks(e,t)}(o,e);var t,n,r,i=Cs(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).view.isUnified&&(t.getScrollEl=function(){return t.termsEl},t.getOffsetTop=function(e){return e.offsetTop},t.getOffsetHeight=function(e){return e.offsetHeight+e.nextElementSibling.clientHeight}),t.hasDefinition=xs.deviceView.isDesktop&&!t.view.isUnified,Ps.on(Is.glossary.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,(n=[{key:"onClickItem",value:function(e){var t=e.target;if(this.hasDefinition&&null!=t&&"p"!==t.nodeName.toLowerCase()){var n=null!=this.activeItem;n&&(this.activeItem.firstElementChild.setAttribute("aria-expanded",!1),this.activeItem.style.backgroundColor="",this.closeItem(this.activeItem)),n&&this.lastSelected==t||(t.setAttribute("aria-expanded",!0),this.openItem(t.parentElement)),this.lastSelected=t}}},{key:"getNextItem",value:function(e){var t,n=this.getItems(),r=this.getItems().indexOf(this.currentItem);do{(r+=1)===n.length&&(r=0),t=n[r]}while("none"===t.parentElement.style.display);return t}},{key:"getPrevItem",value:function(e){var t,n=this.getItems(),r=this.getItems().indexOf(this.currentItem);do{-1==(r-=1)&&(r=n.length-1),t=n[r]}while("none"===t.parentElement.style.display);return t}},{key:"hasListItems",value:function(){return!DS._.isEmpty(this.model.frame.glossaryData)}},{key:"getItems",value:function(){return this.links=this.links||gs(this.el.querySelectorAll(".term")),this.links}},{key:"openItem",value:function(e){js(e,"cs-selected"),e.nextElementSibling.style.display="block",js(e.nextElementSibling,"open"),this.activeItem=e}},{key:"closeItem",value:function(e){var t=this;As(e,"cs-selected"),As(e.nextElementSibling,"open"),this.activeItem=null,TweenLite.to(e,.2,{opacity:1,onComplete:function(){e.nextElementSibling.style.display="none",Ts.setFocusRectOn(t.currentItem)}})}}])&&Ss(t.prototype,n),r&&Ss(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(va));function Bs(e){return Bs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bs(e)}function Rs(e){return function(e){if(Array.isArray(e))return Ms(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ms(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ms(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ms(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ns(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Bs(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Bs(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Bs(o)?o:String(o)),r)}var i,o}function Hs(e,t){return Hs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Hs(e,t)}function Fs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Us(e);if(t){var i=Us(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Vs(this,n)}}function Vs(e,t){if(t&&("object"===Bs(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Us(e){return Us=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Us(e)}var Ws=DS,Zs=(Ws.detection,Ws.pubSub),zs=Ws.events,Ks=Ws.keyManager.isSpaceKey,Gs=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hs(e,t)}(o,e);var t,n,r,i=Fs(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Zs.on(zs.resources.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),t}return t=o,(n=[{key:"onClickItem",value:function(){}},{key:"hasListItems",value:function(){return(this.model.resourceData.resources||[]).length>0}},{key:"getItems",value:function(){return this.items=this.items||Rs(this.el.querySelectorAll(".resource a")),this.items}},{key:"activateItem",value:function(e){Ks(e.keyCode)&&DS.windowOpen.open({url:this.currentItem.dataset.url})}},{key:"getOffsetTop",value:function(e){return this.currentItem.offsetTop}},{key:"getFocusRectTarget",value:function(){return this.currentItem.parentNode}}])&&Ns(t.prototype,n),r&&Ns(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(Ds),Qs=DS,qs=Qs.utils,Ys=qs.getPath,Xs=qs.stripTags,$s=Qs.detection.device.isMobile,Js="resources",ec=z.def(Js,Gs,(function(){var e=z.model,t=e.frame,n=e.rtl?"rtl":"";return DS.slideObjectUtils.activeMobileMenuItem(z.model.frame.resourceData.resources,"no-resources"),{attrs:{id:"".concat(Js,"-content"),class:"is-scrollable panel ".concat(n),tabindex:-1},overflow:"",model:t,html:'\n      <h4 class="resources-title '.concat(n,'" tabindex="-1">\n        ').concat(Xs(Ys(t,"resourceData.description")),"\n      </h4>\n      <div class='resources-content panel-content'>\n        <ul>\n          ").concat(Ys(t,"resourceData.resources",[]).reduce((function(e,t,n){var r=$s?"data-url":"href",i=$s?t.url:"javascript:DS.windowOpen.open({ url: '".concat(t.url,"' })"),o=$s?'target="_blank"':"";return"".concat(e,'\n              <li class="cs-listitem resource" tabindex="-1"\n                data-url="').concat(t.url,'"\n                id="resource-').concat(n,'"\n                role="presentation">\n                  <a href="').concat(t.url,'" ').concat(r,'="').concat(i,'" ').concat(o,' tabindex="').concat(0===n?0:-1,'">\n                    <img src="').concat(t.image,'" role="presentation" tabindex="-1" />\n                    ').concat(t.title,"\n                  </a>\n              </li>")}),""),"\n        </ul>\n      </div>")}})),tc=DS,nc=tc.slideObjectUtils,rc=tc.utils.getPath,ic="glossary",oc=z.def(ic,(function(){var e=z.model,t=z.model.rtl?"rtl":"";return nc.activeMobileMenuItem(e.frame.glossaryData,"no-glossary"),{attrs:{id:"".concat(ic,"-content"),class:"panel ".concat(t),tabindex:-1},w:"100%",h:"100%",model:e,html:'\n      <h4 class="panel-section-heading glossary-title cs-heading">\n        '.concat(e.getString("terms"),'\n      </h4>\n\n      <div data-ref="terms" class="glossary-content scrolling-panel">\n        <ul>\n        ').concat(rc(e,"frame.glossaryData",[]).map((function(e){return'\n              <dt class="glossary-item glossary-term" tabindex="-1">\n                '.concat(e.title,'\n              </dt>\n              <dd class="glossary-item glossary-desc" tabindex="-1">\n                ').concat(e.content,"\n              </dd>\n            ")})).join(""),"\n        </ul>\n      </div>")}}));function ac(e){return ac="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ac(e)}function lc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ac(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ac(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ac(o)?o:String(o)),r)}var i,o}function sc(e,t){return sc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},sc(e,t)}function cc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=dc(e);if(t){var i=dc(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return uc(this,n)}}function uc(e,t){if(t&&("object"===ac(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return fc(e)}function fc(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function dc(e){return dc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},dc(e)}var hc=/color:#ffffff/gi,pc=DS,yc=pc.detection,bc=pc.events,vc=pc.focusManager,mc=pc.pubSub,gc=(pc.utils,pc.slideObjectUtils),wc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sc(e,t)}(o,e);var t,n,r,i=cc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),mc.on(bc.window.MAIN_CHANGED,t.onSlideChanged.bind(fc(t))),yc.deviceView.isClassicMobile?mc.on(bc.mobile.NOTES_SHOWN,t.onHamburgerToggle.bind(fc(t),!1)):yc.deviceView.isUnifiedMobile&&mc.on(bc.hamburger.TOGGLE,t.onHamburgerToggle.bind(fc(t))),t.forceScrollToTop=!1,t}return t=o,(n=[{key:"onHamburgerToggle",value:function(e){this.forceScrollToTop&&!e&&(this.getScrollElement().scrollTop=0,this.forceScrollToTop=!1)}},{key:"onFocus",value:function(){this.el.focus(),vc.setFocusRectOn(this.el.parentNode)}},{key:"focusSelf",value:function(){this.onFocus()}},{key:"handleTab",value:function(){return this.el.parentElement.focus(),!0}},{key:"onSlideChanged",value:function(e){var t=(this.model.notesData||[]).find((function(t){return e.absoluteId.includes(t.slideId)}));null!=this.titleEl&&(this.titleEl.innerHTML=e.get("title"));var n=null==t?"":t.content;yc.deviceView.isClassicMobile&&(n=n.replace(hc,"color:#515557")),this.contentEl.innerHTML=n,mc.trigger(bc.transcript.CHANGED),gc.activeMobileMenuItem(n,"no-transcript"),this.scrollToTop()}},{key:"scrollToTop",value:function(){yc.deviceView.isDesktop?this.view.el.scrollTop=0:yc.theme.isClassic?0===this.getScrollElement().scrollTop?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0:z.getNamespace(this.view.nameSpace).sidebar.collapsed?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0}},{key:"getScrollElement",value:function(){return yc.deviceView.isClassicMobile?this.view.parent.el:this.view.el}},{key:"getViewBox",value:function(){return this.view.parent.getBox()}},{key:"teardown",value:function(){mc.off(bc.window.MAIN_CHANGED,this.onSlideChanged.bind(this)),yc.deviceView.isClassicMobile?mc.off(bc.mobile.NOTES_SHOWN,this.onHamburgerToggle.bind(this,!1)):yc.deviceView.isUnifiedMobile&&mc.off(bc.hamburger.TOGGLE,this.onHamburgerToggle.bind(this))}}])&&lc(t.prototype,n),r&&lc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Sc="transcript",kc={resources:ec,transcript:z.def(Sc,wc,(function(){var e=z.model,t=e.rtl?"rtl":"";return{attrs:{id:"transcript-content",class:"cs-menu cs-panel is-scrollable cs-menu cs-transcript panel ".concat(t),tabindex:-1},w:"100%",h:"100%",html:'\n      <h4 data-ref="title" class="note-title cs-heading panel-section-heading"></h4>\n      <hr class="cs-div"></hr>\n      <div data-ref="content" class="note-content"></div>\n    ',model:e.frame}})),glossary:oc,outline:vs};function Cc(e){return function(e){if(Array.isArray(e))return Oc(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Oc(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Oc(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oc(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ec=DS.constants.MOBILE_UI_SIZE,Lc=z.def("panels",ui,(function(){var e=z.model;return{attrs:{class:"panels"},x:0,y:0,wp:function(){return window.innerWidth+2},hp:function(){return window.innerHeight-Ec+2},wl:function(){return window.innerWidth-160},hl:function(){return window.innerHeight+2},bgColor:"white",z:3,visible:!1,html:'\n      <button class="tab-close" data-ref="tabClose" aria-label="'.concat(e.getString("close"),'">\n        ').concat(sn("close")(),"\n      </button>\n    "),childDef:function(){Tc(e)}}})),Tc=function(e){[].concat(Cc(e.sidebarTabs),Cc(e.topTabsLeft),Cc(e.topTabsRight)).forEach((function(e,t){"customlink"!==e.name&&function(e,t,n){var r=t.name,i=r+"Panel",o=z.def(i,{attrs:{id:i,class:"tab-panel is-scrollable","aria-labelledby":r+"-tab",role:"tabpanel",tabindex:0},visibility:"no-reflow",visible:!1,overflow:"auto",x:0,y:0,w:"100%",h:"100%"});o.init(),Lc.append(o);var a=kc[r];null!=a&&(a.init(),o.append(a))}(0,e)}))};function xc(e){return xc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xc(e)}function Pc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==xc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==xc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===xc(o)?o:String(o)),r)}var i,o}function Ic(e,t){return Ic=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ic(e,t)}function _c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Ac(e);if(t){var i=Ac(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return jc(this,n)}}function jc(e,t){if(t&&("object"===xc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ac(e){return Ac=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ac(e)}var Dc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ic(e,t)}(o,e);var t,n,r,i=_c(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickOverlay),t}return t=o,(n=[{key:"onClickOverlay",value:function(e){DS.pubSub.trigger(DS.events.sidebar.CLOSE)}}])&&Pc(t.prototype,n),r&&Pc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut);function Bc(e){return Bc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bc(e)}function Rc(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Bc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Bc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Bc(o)?o:String(o)),r)}var i,o}function Mc(e,t){return Mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Mc(e,t)}function Nc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Vc(e);if(t){var i=Vc(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Hc(this,n)}}function Hc(e,t){if(t&&("object"===Bc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Fc(e)}function Fc(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vc(e){return Vc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Vc(e)}z.def("visibleOverlay",Dc,(function(){return{attrs:{class:"visible-overlay"},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},visible:!1,position:"fixed",bgColor:"rgba(0, 0, 0, 0.5)",add:!0}}));var Uc=DS,Wc=Uc.pubSub,Zc=Uc.captionsManager,zc=Uc.events.captions,Kc=zc.SHOW_BUTTON,Gc=zc.HIDE_BUTTON,Qc=zc.ENABLED,qc=zc.ENABLE,Yc=Uc.detection.theme.isUnified,Xc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mc(e,t)}(o,e);var t,n,r,i=Nc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickCaptions),t.toggle=!1,Wc.on(Kc,(function(e){return t.onVisibilityChanged(!0)})),Wc.on(Gc,(function(e){return t.onVisibilityChanged(!1)})),Wc.on(Qc,t.onCaptionsEnabled.bind(Fc(t))),t}return t=o,(n=[{key:"onVisibilityChanged",value:function(e){this.view.setVisibility(e,!0),this.view.childVisibilityChanged(),this.toggle=Zc.isCaptionEnabled(),this.updateBtn()}},{key:"onCaptionsEnabled",value:function(e){this.toggle=e,this.updateBtn()}},{key:"updateBtn",value:function(){var e=this.toggle?"add":"remove";this.view.el.classList[e]("cs-tabs","cs-selected"),this.view.el.setAttribute("aria-pressed",this.toggle),Yc&&this.view.updateHtml()}},{key:"onClickCaptions",value:function(e){this.toggleCaptions()}},{key:"toggleCaptions",value:function(){this.toggle=!this.toggle,Wc.trigger(qc,this.toggle),this.updateBtn()}},{key:"teardown",value:function(){Wc.off(Kc),Wc.off(Gc),Wc.off(Qc)}}])&&Rc(t.prototype,n),r&&Rc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut);function $c(e){return $c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$c(e)}function Jc(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==$c(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==$c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===$c(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var eu=DS.constants.MOBILE_UI_SIZE,tu="captions";function nu(e){return nu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nu(e)}function ru(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,du(r.key),r)}}function iu(){return iu="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=ou(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},iu.apply(this,arguments)}function ou(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=uu(e)););return e}function au(e,t){return au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},au(e,t)}function lu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=uu(e);if(t){var i=uu(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return su(this,n)}}function su(e,t){if(t&&("object"===nu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return cu(e)}function cu(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function uu(e){return uu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},uu(e)}function fu(e,t,n){return(t=du(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function du(e){var t=function(e,t){if("object"!==nu(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==nu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nu(t)?t:String(t)}z.def(tu,Xc,(function(){var e,t=z.model;return Jc(e={tag:"button",attrs:{id:tu,class:"cs-button btn","aria-label":t.getString("acc_closed_captions"),tabindex:0,"aria-pressed":!0},xp:function(){return this.left||0},xl:function(){return(this.parent.w-this.w)/2},minH:eu,noUpdate:!0,visible:!1,parentAlign:"tr",html:sn("captions")()},"xp",(function(){return this.left||0})),Jc(e,"xl",(function(){return(this.parent.w-this.w)/2})),Jc(e,"minW",47),e}));var hu=DS,pu=hu.dom,yu=pu.addClass,bu=pu.removeClass,vu=hu.appState,mu=hu.scaler,gu=hu.shortcutManager,wu=hu.events,Su=hu.focusManager,ku=hu.pubSub,Cu=hu.utils.fullScreen,Ou=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&au(e,t)}(o,e);var t,n,r,i=lu(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),fu(cu(t=i.call(this,e)),"toggleBackgroundAudio",(function(){DS.courseAudio.toggleEnabled(),t.updateToggle(t.backgroundAudioSwitchEl,DS.courseAudio.enabled())})),fu(cu(t),"onCaptionsEnabled",(function(){t.view.updateHook()})),DS._.bindAll(cu(t),"onAccessibleTextChanged","onZoomModeChanged","onKeyboardShortcutsChanged"),t.onClickEl(t.settingsBtnEl,t.togglePanel),null!=t.shortcutsSwitchEl&&(t.onClickEl(t.shortcutsSwitchEl,t.toggleShortcuts),DS.pubSub.on(wu.player.ENABLE_KEYBOARD_SHORTCUTS,t.onKeyboardShortcutsChanged)),null!=t.captionsButtonEl&&(t.onClickEl(t.captionsButtonEl,t.toggleCaptions),ku.on(wu.captions.ENABLED,t.onCaptionsEnabled)),null!=t.resetButtonEl&&t.onClickEl(t.resetButtonEl,t.resetTimeline),null!=t.acctextSwitchEl&&(t.onClickEl(t.acctextSwitchEl,t.toggleAccessibleText),vu.on(wu.player.ACCESSIBLE_TEXT_CHANGED,t.onAccessibleTextChanged)),null!=t.zoomSwitchEl&&(t.onClickEl(t.zoomSwitchEl,t.toggleZoomMode),ku.on(wu.window.ZOOM_MODE_CHANGED,t.onZoomModeChanged)),t.handleFullScreenChange=t.handleFullScreenChange.bind(cu(t)),null!=t.fullScreenToggleButtonEl&&(t.onClickEl(t.fullScreenToggleButtonEl,t.toggleFullScreen),t.removeFullScreenListener=Cu.addChangeListener(t.handleFullScreenChange)),null!=t.backgroundAudioSwitchEl&&t.onClickEl(t.backgroundAudioSwitchEl,t.toggleBackgroundAudio),t}return t=o,(n=[{key:"toggleFullScreen",value:function(){vu.toggleFullScreen()}},{key:"handleFullScreenChange",value:function(){Cu.getEl()===vu.getPresoEl()?yu(this.el,"full-screen"):bu(this.el,"full-screen")}},{key:"togglePanel",value:function(){this.isOpen?this.hidePanel():this.showPanel()}},{key:"showPanel",value:function(){this.isOpen=!0,yu(this.el,"open"),this.view.updatePanelPosition(),this.settingsBtnEl.setAttribute("aria-expanded",!0),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!0)}},{key:"hidePanel",value:function(){this.isOpen=!1,bu(this.el,"open"),this.settingsBtnEl.setAttribute("aria-expanded",!1),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!1)}},{key:"onBlur",value:function(e){null!=e&&this.isOpen&&!this.el.contains(e.relatedTarget)&&(this.hidePanel(),iu(uu(o.prototype),"onBlur",this).call(this))}},{key:"updateToggle",value:function(e,t){t?(yu(e,"toggle-on"),bu(e,"toggle-off")):(yu(e,"toggle-off"),bu(e,"toggle-on")),e.querySelector("button").setAttribute("aria-checked",t)}},{key:"onAccessibleTextChanged",value:function(e){this.accTextOn=e,this.updateToggle(this.acctextSwitchEl,this.accTextOn)}},{key:"onKeyboardShortcutsChanged",value:function(e){this.updateToggle(this.shortcutsSwitchEl,e)}},{key:"toggleAccessibleText",value:function(){this.accTextOn=!this.accTextOn,this.updateToggle(this.acctextSwitchEl,this.accTextOn),vu.onToggleAccessibleText(this.accTextOn)}},{key:"onZoomModeChanged",value:function(){this.updateToggle(this.zoomSwitchEl,mu.zoomMode)}},{key:"toggleZoomMode",value:function(){mu.enableZoomMode(!mu.zoomMode),this.updateToggle(this.zoomSwitchEl,mu.zoomMode),Su.setFocusRectOn(this.zoomSwitchEl.querySelector("button"))}},{key:"toggleShortcuts",value:function(){gu.enableShortcuts(!gu.enabled),this.updateToggle(this.shortcutsSwitchEl,gu.enabled)}},{key:"toggleCaptions",value:function(){z.getNamespace("_frame").captions.viewLogic.toggleCaptions(),this.view.updateHook()}},{key:"resetTimeline",value:function(){z.getNamespace("_frame").reset.viewLogic.resetTimeline()}},{key:"teardown",value:function(){vu.off(wu.player.ACCESSIBLE_TEXT_CHANGED,this.onAccessibleTextChanged),ku.off(wu.window.ZOOM_MODE_CHANGED,this.onZoomModeChanged),ku.off(wu.captions.ENABLED,this.onCaptionsEnabled),null!=this.removeFullScreenListener&&this.removeFullScreenListener()}},{key:"onFocus",value:function(e){var t=e.target;if(this.el.contains(t))return Su.setFocusRectOn(t),this.isFocused=!1,!1}}])&&ru(t.prototype,n),r&&ru(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),Eu=DS,Lu=Eu.dom,Tu=Lu.addClass,xu=Lu.removeClass,Pu=Eu.appState,Iu=Eu.utils,_u=Iu.pxify,ju=Iu.getPath,Au=Eu.detection,Du=(Au.deviceView.isPhone,Au.orientation),Bu=Eu.constants.MOBILE_UI_SIZE,Ru="settings";function Mu(e){return Mu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mu(e)}function Nu(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Hu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Hu(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Fu(e,t,n){return(t=Uu(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Uu(r.key),r)}}function Uu(e){var t=function(e,t){if("object"!==Mu(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Mu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Mu(t)?t:String(t)}function Wu(e,t){return Wu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Wu(e,t)}function Zu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Gu(e);if(t){var i=Gu(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return zu(this,n)}}function zu(e,t){if(t&&("object"===Mu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ku(e)}function Ku(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gu(e){return Gu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Gu(e)}z.def(Ru,Ou,(function(e){var t=z.model,n=(z.model.rtl,z.getNamespace(e)),r=n.optionPane,i=n.captions;return{noUpdate:!0,attrs:{id:Ru,class:"cs-settings"},overflow:"visible",minW:Bu,minH:Bu,w:47,h:Bu,z:2,xp:function(){return this.left||0},xl:function(){return 0},y:function(){return this.top||0},methods:{getControlConfigs:function(){return DS.frameModel.hasModernText?[{name:"acctext",labelId:"accessible_text",isOn:function(){return Pu.accessibleTextOn()}}]:[]},getToggleControls:function(){return this.getControlConfigs().map((function(e){var n=e.name,r=e.labelId,i=(0,e.isOn)();return'\n            <div class="switch '.concat(i?"toggle-on":"toggle-off",'" data-ref="').concat(n,'Switch">\n              <label class="switch-label" id="').concat(n,'-label">\n                ').concat(t.getString(r),'\n              </label>\n              <button class="switch-toggle" id="').concat(n,'-switch" tabindex="0" role="switch" aria-checked="').concat(i,'" aria-labelledby="').concat(n,'-label">\n                ').concat(sn("track")(n),"\n              </button>\n            </div>\n          ")})).join("")},getIconConfigs:function(){return[{name:"captions",labelId:"acc_closed_captions",getIcon:sn("captions")}]},getIconControls:function(){return this.getIconConfigs().map((function(e){var n=e.name,r=e.labelId,i=e.getIcon;return'\n            <button id="'.concat(n,'-overflow" class="cs-button overflow-button" tabindex="0" aria-label="').concat(t.getString(r),'" data-ref="').concat(n,'Button">\n              ').concat(i(),"\n            </button>          \n          ")})).join("")},updatePanelPosition:function(){if(null!=r){var e=this.children.settingsPanel.el;if(e.style.left=0,!Du.isLandscape){var t=e.getBoundingClientRect(),n=r.el.getBoundingClientRect(),i=0;t.right>n.right&&(i=n.right-(t.right+10)),t.left<n.left&&(i=n.left-t.left+10),e.style.left=_u(i)}}}},updateHook:function(){var e=this;window.requestAnimationFrame((function(){return e.updatePanelPosition()}));var t=ju(i,"viewLogic.toggle");t?(Tu(this.el,"captions-on"),xu(this.el,"captions-off")):(Tu(this.el,"captions-off"),xu(this.el,"captions-on")),this.setVisibility(0!==this.getControlConfigs().length||t)},html:function(){return'\n        <button data-ref="settingsBtn" aria-expanded="false" class="cs-button settings-button" aria-label="'.concat(t.getString("acc_settings"),'" tabIndex="0">\n          ').concat(sn("settings")(),'\n        </button>\n        <div data-ref="settingsPanel" class="settings-panel top-tabs-drop" tabindex="-1">\n          <div class="icon-buttons">\n            ').concat(this.getIconControls(),'\n          </div>\n          <div class="toggle-buttons">\n            ').concat(this.getToggleControls(),'\n          </div>\n        </div>\n        <div class="panel-down-arrow">\n          ').concat(sn("downArrow")(),"\n        </div>\n      ")}}}));var Qu=DS,qu=Qu.detection,Yu=Qu.events,Xu=Qu.pubSub,$u=Qu.svgUtils,Ju=Qu._.bindAll,ef={remaining:function(e,t){return DS.utils.formatSecondsAsTime(t-e,!0)},totalelapsed:function(e,t){return[e,t].map((function(e){return DS.utils.formatSecondsAsTime(e,!0)})).join(" / ")},elapsed:function(e){return DS.utils.formatSecondsAsTime(e,!0)},none:function(){return""}},tf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wu(e,t)}(o,e);var t,n,r,i=Zu(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),n=i.call(this,e),Ju(Ku(n),"onShow","onHide","onTick"),Xu.on((Fu(t={},Yu.timer.SHOW,n.onShow),Fu(t,Yu.timer.HIDE,n.onHide),t)),n.timeFormat=n.getTimeFormat(),n.isPieProgress=qu.theme.isUnified,n}return t=o,(n=[{key:"getTimeFormat",value:function(){if(!qu.deviceView.isClassicMobile){var e=z.model.sidebarOpts.timeFormat;if(e&&null!=ef[e.toLowerCase()])return e.toLowerCase();if(!z.model.sidebarOpts.timeEnabled)return"none"}return"remaining"}},{key:"teardown",value:function(){var e;Xu.off((Fu(e={},Yu.timer.SHOW,this.onShow),Fu(e,Yu.timer.HIDE,this.onHide),e))}},{key:"onTick",value:function(e,t,n){if(this.view.children.timerText.el.innerHTML=ef[this.timeFormat](t,n),!qu.deviceView.isClassicMobile){var r=$u.wheelPath(9,9,9,0,360*(1-e),this.isPieProgress);this.view.children.timerPath.el.setAttributeNS(null,"d",r)}}},{key:"onShow",value:function(e){null!=this.currentTimer&&this.onHide(),this.currentTimer=e,this.currentTimer.on("tick",this.onTick),this.toggleVisibility(!0),window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.SHOWN))}},{key:"onHide",value:function(){null!=this.currentTimer&&(this.currentTimer.off("tick",this.onTick),this.currentTimer=null,window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.HIDDEN))),this.toggleVisibility(!1)}},{key:"toggleVisibility",value:function(e){var t=Nu(e?["add","remove"]:["remove","add"],2),n=t[0],r=t[1];document.body.classList[n]("timer-shown"),this.el.classList[r]("hidden"),this.el.classList[n]("shown"),this.view.setVisibility(e),this.view.parent.updateChildren(!0)}},{key:"onFocus",value:function(){var e=this.view.children.timerText.el.getBoundingClientRect(),t=e.left,n=e.top,r=e.width,i=e.height;t-=8,n-=8,r+=37,i+=12,DS.focusManager.setFocusRectOn(this.el,{left:t,top:n,width:r,height:i})}}])&&Vu(t.prototype,n),r&&Vu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),nf="timer";z.def(nf,tf,(function(){return{noTabIndex:!0,attrs:{id:nf,"aria-label":"timer",class:"timer"},overflow:"visible",visible:!1,x:0,y:0,w:58,hp:58,hl:34,html:'<div class="timer-text" data-ref="timerText" tabindex="0" data-tabindex="0"></div>'}}));var rf=DS,of=(rf.detection,rf.detection),af=of.orientation,lf=of.deviceView,sf="disableOverlay";function cf(e){return cf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cf(e)}function uf(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==cf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==cf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===cf(o)?o:String(o)),r)}var i,o}function ff(e,t){return ff=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ff(e,t)}function df(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pf(e);if(t){var i=pf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return hf(this,n)}}function hf(e,t){if(t&&("object"===cf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function pf(e){return pf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},pf(e)}z.def(sf,(function(){var e=z.model,t=e.frame,n=window.globals.parseParams().orientations||null,r=JSON.parse(decodeURIComponent(n))||t.orientations,i=r.phone,o=r.tablet;return{attrs:{id:sf,role:"dialog","aria-modal":!0,"aria-labelledby":"mobile-disabled-orientation-text","aria-live":"assertive"},style:{fontSize:"".concat(t.fontscale,"%"),background:"black",color:"white"},x:0,y:0,w:function(){},h:function(){},updateHook:function(){var e=!0;af.isLandscape?(lf.isPhone&&i.includes("landscape")||lf.isTablet&&o.includes("landscape"))&&(e=!1):af.isPortrait&&(lf.isPhone&&i.includes("portrait")||lf.isTablet&&o.includes("portrait"))&&(e=!1),e&&!af.forceHideWarning?document.body.classList.add("show-disabled-overlay"):document.body.classList.remove("show-disabled-overlay")},html:function(){return'<div class="mobile-disabled-orientation-overlay-inner">\n        <div class="mobile-disabled-orientation-overlay-icon">\n          '.concat(sn("disableOrientation")(),'\n        </div>\n        <div id="mobile-disabled-orientation-text">').concat(e.getString("disabled_orientation"),"</div>\n      </div>")},add:!0}}));var yf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ff(e,t)}(o,e);var t,n,r,i=df(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),new MutationObserver((function(){t.view.onCaptionChanged()})).observe(t.el,{childList:!0,subtree:!0}),t}return t=o,n&&uf(t.prototype,n),r&&uf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}(ut),bf=DS,vf=bf.utils.pxify,mf=bf.constants.MOBILE_UI_SIZE,gf=bf.detection.orientation;function wf(e){return wf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wf(e)}function Sf(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==wf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==wf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===wf(o)?o:String(o)),r)}var i,o}function kf(e,t){return kf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},kf(e,t)}function Cf(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Lf(e);if(t){var i=Lf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Of(this,n)}}function Of(e,t){if(t&&("object"===wf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ef(e)}function Ef(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lf(e){return Lf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Lf(e)}z.def("captionContainer",yf,(function(e){var t=z.getNamespace(DS.constants.refs.FRAME).slide;return{attrs:{class:"caption-container"},overflow:"visible",z:1,wp:function(){return window.innerWidth},wl:function(){return window.innerWidth-mf},hl:function(){return window.innerHeight},hp:function(){return"unset"},yp:null,add:!0,updateHook:function(){gf.isPortrait?(this.el.style.height="unset",this.el.style.top="unset",this.onCaptionChanged()):this.el.style.bottom=""},onCaptionChanged:function(){if(gf.isPortrait){var e=Math.max(0,window.innerHeight-(t.y+t.h+mf));this.el.style.bottom=vf((e-this.el.clientHeight)/2+mf)}}}}));var Tf=DS,xf=Tf.events,Pf=Tf.pubSub,If=Tf._,_f=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kf(e,t)}(o,e);var t,n,r,i=Cf(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),If.bindAll(Ef(t),"onEnterFullscreen"),t.onClick(t.onClickClose),t.view.setVisibility(!1),Pf.on(xf.presentation.ENTER_FULLSCREEN,t.onEnterFullscreen),t}return t=o,(n=[{key:"onEnterFullscreen",value:function(){this.view.setVisibility(!0)}},{key:"onClickClose",value:function(e){var t=this;DS.utils.fullScreen.exit(),setTimeout((function(){Pf.trigger(xf.presentation.EXIT_FULLSCREEN),t.view.setVisibility(!1)}),300)}}])&&Sf(t.prototype,n),r&&Sf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),jf="fullScreenClose";function Af(e){return Af="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Af(e)}function Df(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Af(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Af(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Af(o)?o:String(o)),r)}var i,o}function Bf(e,t){return Bf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Bf(e,t)}function Rf(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Hf(e);if(t){var i=Hf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Mf(this,n)}}function Mf(e,t){if(t&&("object"===Af(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Nf(e)}function Nf(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Hf(e){return Hf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Hf(e)}z.def(jf,_f,(function(e){return{tag:"button",attrs:{id:jf,class:"cs-button btn fullscreen-close-btn",tabindex:0},overflow:"visible",html:'\n      <div class="fullscreen-close-btn-bg"></div>\n      '.concat(sn("close")(),"\n    "),x:26,y:26,w:18,h:18,noContent:!0}}));var Ff=DS,Vf=Ff.events,Uf=Ff.pubSub,Wf=Ff._,Zf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bf(e,t)}(o,e);var t,n,r,i=Rf(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),Wf.bindAll(Nf(t),"onExitFullScreen"),t.onClick(t.onClickOpen),Uf.on(Vf.presentation.EXIT_FULLSCREEN,t.onExitFullScreen),t}return t=o,(n=[{key:"onExitFullScreen",value:function(){this.view.setVisibility(!0)}},{key:"onClickOpen",value:function(e){Uf.trigger(Vf.presentation.ENTER_FULLSCREEN),this.view.setVisibility(!1)}}])&&Df(t.prototype,n),r&&Df(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),zf="fullScreenOpen",Kf=DS.constants.MOBILE_UI_SIZE;function Gf(e){return function(e){if(Array.isArray(e))return Qf(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Qf(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Qf(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qf(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}z.def(zf,Zf,(function(e){z.model;return{tag:"button",attrs:{id:zf,class:"cs-button btn fullscreen-open-btn",tabindex:0},html:"\n      ".concat(sn("enterFullScreen")(),"\n    "),padLeft:6,padRight:6,minW:Kf,minH:Kf,noUpdate:!0,parentAlign:"br",noContent:!1}}));var qf=DS.constants.refs.FRAME,Yf=DS,Xf=Yf.pubSub,$f=Yf.events,Jf=Yf._.isEmpty,ed=Yf.detection,td=ed.device,nd=ed.env,rd=nd.is360,id=nd.isDevicePreview,od=nd.enableFs,ad=function(){var e=z.model,t=e.topTabsLeft,n=e.topTabsRight,r=e.sidebarTabs;return!_.isEmpty(t)||!_.isEmpty(n)||!_.isEmpty(r)},ld="lightBoxWrapper";z.def(ld,(function(e){var t=z.model,n=t.slideWidth,r=t.slideHeight;z.getNamespace(DS.constants.refs.FRAME);return{attrs:{id:ld,class:"cs-base fn-".concat(t.frame.default_layout)},style:{transformOrigin:"0px 0px"},overflow:"visible",dimScale:function(){var e=.8*nt.width,t=.8*nt.height;return Math.min(e/n,t/r)},wl:function(){return n*this.dimScale+58},wp:function(){return n*this.dimScale},hl:function(){return r*this.dimScale},hp:function(){return r*this.dimScale+58},x:function(){return(nt.width-this.w)/2},y:function(){return(nt.height-this.h)/2},add:!0}}));var sd="lightBoxSlide";z.def(sd,(function(){var e=z.getCurrentNameSpace().lightBoxWrapper;return{attrs:{class:sd,"aria-live":"assertive",role:"dialog","aria-modal":!0},overflow:"visible",winScale:function(){return e.dimScale},origin:"0 0",wl:function(){return this.parent.wl-58},hl:function(){return this.parent.hl},wp:function(){return this.parent.wp},hp:function(){return this.parent.hp-58},z:1,bgColor:"white",add:!0,html:function(){return'<div id="slide-label-lightbox" data-ref="label" aria-live="polite"></div><main data-ref="container" class="slide-container" transform-origin: 0 0; position: absolute;" tabindex="-1"></main>'}}}));var cd="lightBox";function ud(e){return ud="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ud(e)}function fd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==ud(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==ud(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===ud(o)?o:String(o)),r)}var i,o}function dd(e,t){return dd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},dd(e,t)}function hd(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=yd(e);if(t){var i=yd(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return pd(this,n)}}function pd(e,t){if(t&&("object"===ud(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function yd(e){return yd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yd(e)}z.def(cd,(function(){return{attrs:{class:"".concat(cd," cs-base")},x:0,y:0,z:1,w:"100%",h:"100%",overflow:"visible"}}));var bd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dd(e,t)}(o,e);var t,n,r,i=hd(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=i.call(this,e)).onClick(t.onClickClose),t}return t=o,(n=[{key:"onClickClose",value:function(e){var t=this.model.windowId;DS.pubSub.trigger(DS.events.window.CLOSING,t)}}])&&fd(t.prototype,n),r&&fd(t,r),Object.defineProperty(t,"prototype",{writable:!1}),o}(ut),vd="lightBoxClose";z.def(vd,bd,(function(e){var t={windowId:e};return{tag:"button",attrs:{id:vd,class:"lightbox-close-btn-floating",tabindex:0,"aria-label":DS.stringTabler.getString("close")},style:{overflow:"visible"},html:sn("close"),y:35,x:function(){return window.innerWidth-(this.w+35)},model:t,w:20,h:20}}));var md="close",gd=DS,wd=gd.detection.orientation,Sd=gd.constants.MOBILE_UI_SIZE;z.def(md,bd,(function(e){var t={windowId:e};return{tag:"button",attrs:{id:md,class:"cs-button btn",tabindex:0},html:'\n      <div class="icon">\n        '.concat(sn("close")(),"\n      </div>\n    "),model:t,parentAlign:"t-",w:Sd,h:Sd,noContent:!1,beforeReflowHook:function(){this.setVisibility(wd.isLandscape)}}}));var kd="LightboxWnd",Cd=DS.detection.orientation,Od="lightBoxBottom";z.def(Od,(function(e){var t=z.model.rtl;return{tag:"nav",attrs:{id:Od,class:"option-pane"},methods:{getZoomBounds:function(){return z.getNamespace(e).zoomBounds},getParentBB:function(){return null==this.cachedParentBB&&(this.cachedParentBB=this.parent.getBox(),null!=this.cachedParentBB&&(this.cachedParentBB.left=this.cachedParentBB.x,this.cachedParentBB.right=this.cachedParentBB.x+this.cachedParentBB.w,this.cachedParentBB.top=this.cachedParentBB.y,this.cachedParentBB.bottom=this.cachedParentBB.y+this.cachedParentBB.h)),this.cachedParentBB},shouldZoom:function(){return null!=this.getParentBB()&&null!=this.getZoomBounds()}},z:1,xp:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return Math.floor(Math.max(e.left,0)-t.left)}return 0},xl:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return t.w+(Math.min(e.right,window.innerWidth-58)-t.right)}return this.parent.w-this.w},yp:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return t.h+(Math.min(e.bottom,window.innerHeight-58)-t.bottom)}return this.parent.h-this.h},yl:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return Math.floor(Math.max(e.top,0)-t.top)}return 0},wp:function(){if(this.shouldZoom()){var e=this.getZoomBounds();return Math.ceil(Math.min(e.right,window.innerWidth)-Math.max(e.left,0))}return this.parent.w},wl:58,hp:58,hl:function(){if(this.shouldZoom()){var e=this.getZoomBounds();return Math.ceil(Math.min(e.bottom,window.innerHeight)-Math.max(e.top,0))}return this.parent.h},updateHook:function(){var e=this;if(this.hasAllChildren()){Cd.isLandscape?this.setVisibility(!0):this.setVisibility(this.children.some((function(t){return t.visible&&t!=e.children.close})));var n={vertical:Cd.isLandscape,alignChild:!0,bounds:{t:0,b:this.h,l:0,r:this.w},reverse:t};this.flowChildren(n),this.cachedParentBB=null}},childVisibilityChangedHook:function(){this.update()}}}));var Ed="LightboxControlsWnd",Ld="printWrapper";z.def(Ld,(function(e){return{attrs:{id:Ld,class:"print-window"},w:0,h:0,style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},scale:1,x:0,y:0,add:!0}}));var Td=DS.constants.printSettings,xd="printSlide";z.def(xd,jt,(function(e){var t=z.model.slideWidth;return{attrs:{id:xd,class:"cs-window window-slide ".concat(e,"-slide"),tabindex:-1},style:{overflow:"visible",transformOrigin:"0 0",background:"transparent",display:"none"},origin:"0 0",winScale:function(){return this.w/t},w:function(){var e=DS.detection.os,t=e.isAndroid,n=e.isIOS;return t?Td.android.pageW:n?Td.ios.pageW:Td.desktop.pageW},h:0,x:0,y:0,z:1,html:'<div data-ref="container" class="slide-container"></div>'}}));var Pd="PrintWindow";function Id(e){return Id="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Id(e)}function _d(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==Id(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Id(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(i,"string"),"symbol"===Id(o)?o:String(o)),r)}var i,o}function jd(e,t){return jd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},jd(e,t)}function Ad(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Bd(e);if(t){var i=Bd(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return Dd(this,n)}}function Dd(e,t){if(t&&("object"===Id(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Bd(e){return Bd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Bd(e)}var Rd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jd(e,t)}(o,e);var t,n,r,i=Ad(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=i.call(this,e),DS.pubSub.on(DS.events.messageWindow.SHOWN,(function(){t.view.isNotResume=!0,t.view.update()})),t}return t=o,n&&_d(t.prototype,n),r&&_d(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}(ut),Md="messageWindowWrapper";z.def(Md,Rd,(function(e){var t=z.model.preso.display().windows().find({id:e});return{attrs:{id:Md},w:t.get("width"),h:t.get("height"),style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},y:function(){return this.isNotResume?(window.innerHeight-this.h)/2:window.innerHeight/2-this.h/2*this.scale},scalel:function(){return this.isNotResume?1:window.innerWidth/this.w},scalep:function(){return this.isNotResume?.8:window.innerWidth/this.w},xl:function(){return this.isNotResume?(window.innerWidth-this.w)/2:0},xp:function(){return this.isNotResume?(window.innerWidth-.8*this.w)/2:0},add:!0}}));var Nd="messageWindowSlide";z.def(Nd,jt,(function(){return{attrs:{id:Nd,class:"cs-window","aria-labelledby":"slide-label-message",role:"alertdialog","aria-modal":!0,tabindex:-1},winScale:1,origin:"0 0",w:"100%",h:"100%",x:0,y:0,z:1,html:'<div id="slide-label-message" data-ref="label"></div><div data-ref="container" class="slide-container"></div>'}}));z.def("overlay",(function(){return{attrs:{class:"overlay overlay-message"},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},position:"fixed",add:!0}}));var Hd,Fd="MessageWnd";function Vd(e){return Vd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vd(e)}function Ud(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Vd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Vd(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Vd(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t){$e(e),function(e){var t=window.globalProvideData;if(window.globalProvideData=function(e,n){"frame"===e&&(be=JSON.parse(n),Ze.register("frame",be.flags,Ve),pe.hasData=!0,Ue.trigger(xe,pe),window.globalProvideData=t)},Ue.on(Pe,(function(e){new Ce,ye=new c(be,e),z.setModel(ye),Ke.initialize(ye),Ge.initialize((function(e){return ye.getString(e)})),DS.frameModel=ye,DS.views=z,Ue.trigger(je)})),window.globals.useJson){var n=e.replace(".js",".json"),r=new XMLHttpRequest;return r.overrideMimeType("application/json"),r.onreadystatechange=function(){4===r.readyState&&200===r.status&&window.globalProvideData("frame",r.responseText.replace(/\\'/g,"'").replace(/\\"/g,'"'))},r.open("GET",n,!0),void r.send(null)}DS.loadScript(e)}(t)}((Ud(Hd={},qf,(function(e){var t,n,r=e.topTabsLeft,i=e.topTabsRight,o=e.sidebarOpts,a=e.bottomBarOpts,l=e.sidebarOpts.timeEnabled,s=e.frame.controlOptions.controls,c=s.closed_captions,u=s.settings,f=z.tree(qf,[{wrapper:["slide",{frame:[{optionPane:[].concat(Gf(ad()?["hamburger"]:[]),[{playbackControls:["playPause","seek"].concat(Gf(c?["captions"]:[]),Gf(u?["settings"]:[]),Gf(td.isTablet&&rd?["fullScreenOpen"]:[]))},{navControls:["prev","next","submit"].concat(Gf(l?["timer"]:[]))}])}]},"panels","visibleOverlay","captionContainer",{sidebar:["tabs"]}]}].concat(Gf(rd?[]:["disableOverlay"]),["fullScreenClose"]));n=!Jf(r)||!Jf(i),o.sidebarEnabled||a.bottomBarEnabled||n||!t&&rd&&(!id||od)||z.getNamespace(qf).optionPane.setVisibility(!1),Xf.on($f.frameModel.LAYOUT_CHANGED,(function(e,t){if(t===qf){var n={playPause:e.seekbar,seek:e.seekbar,next:e.next,prev:e.previous,submit:e.submit,glossaryLink:e.glossary,outlineLink:e.outline.enabled,transcriptLink:e.transcript,resourcesLink:e.resources};Xf.trigger($f.frame.LAYOUT_READY,n),z.resetStates(qf),z.updateVisibility(n,qf),z.update(f)}})),DS.pubSub.on(DS.events.navcontrols.CHANGED,(function(e){var t=e.kind,n=e.name,r=e.visible,i=e.enable,o="toggle_window_control_visible"===t||"toggle_window_control"===t;"previous"===n&&(n="prev");var a=z["enable_window_control"===t||"toggle_window_control"===t?"getTopNameSpace":"getFrameNameSpace"]()[n];null!=a&&(o?"toggle_window_control_visible"===t?(a.setVisibility(!a.visible),a.childVisibilityChanged()):a.setEnabled(!a.enabled):"set_window_control_visible"===t?(a.setVisibility(r),a.childVisibilityChanged()):a.setEnabled(i))}));return{all:function(){return z.update(f)},resize:function(){z.update(f),z.getNamespace(qf).panels.children.forEach((function(e){null!=e.update&&(e.update(),e.updateChildren(!0))}))}}})),Ud(Hd,kd,(function(e){var t=z.tree(kd,["visibleOverlay",{lightBoxWrapper:["lightBoxSlide",{lightBox:[{lightBoxBottom:["close","captions"]}]}]},"captionContainer","lightBoxClose"]),n=z.getNamespace(kd);return n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper,n.visibleOverlay.setVisibility(!0),{all:function(){z.update(t)},resize:function(){n.zoomBounds=null,z.update(t)},pinchZoom:function(){n.lightBoxBottom.update()}}})),Ud(Hd,Ed,(function(e){var t=z.tree(Ed,["visibleOverlay",{lightBoxWrapper:["lightBoxSlide",{lightBox:[{lightBoxBottom:["close","submit","next","prev","captions"]}]}]},"captionContainer","lightBoxClose"]),n=z.getNamespace(Ed);n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper,n.visibleOverlay.setVisibility(!0);var r=function(e,n){if(n===Ed){var r={next:e.next,prev:e.previous,submit:e.submit};z.resetStates(Ed),z.updateVisibility(r,Ed),z.update(t)}};return DS.pubSub.on(DS.events.frameModel.LAYOUT_CHANGED,r),r(e.currControlLayout),{all:function(){return z.update(t)},resize:function(){n.zoomBounds=null,z.update(t)},pinchZoom:function(){return n.lightBoxBottom.update()}}})),Ud(Hd,Pd,(function(e){z.tree(Pd,[{printWrapper:["printSlide"]}]);var t=z.getNamespace(Pd);return t.slide=t.printSlide,t.wrapper=t.printWrapper,{all:function(){},resize:function(){},pinchZoom:function(){}}})),Ud(Hd,Fd,(function(e){var t=z.tree(Fd,["overlay",{messageWindowWrapper:["messageWindowSlide"]},"disableOverlay"]),n=z.getNamespace(Fd);return n.slide=n.messageWindowSlide,n.wrapper=n.messageWindowWrapper,{all:function(){return z.update(t)},resize:function(){n.isAttached&&(n.overlay.update(),n.messageWindowWrapper.update())}}})),Hd),"html5/data/js/frame.js")}();
