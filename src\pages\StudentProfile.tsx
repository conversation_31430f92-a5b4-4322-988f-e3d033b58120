import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Student, Course, StudentProgress, getStoredData } from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  BookOpen, 
  ArrowLeft,
  Clock,
  Award,
  TrendingUp,
  CheckCircle,
  Play
} from 'lucide-react';

const StudentProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [student, setStudent] = useState<Student | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [progress, setProgress] = useState<StudentProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStudentData = () => {
      if (!id) {
        navigate('/dashboard');
        return;
      }

      const students = getStoredData<Student[]>('ecurso_students', []);
      const foundStudent = students.find(s => s.id === id);
      
      if (!foundStudent) {
        toast({
          title: "Aluno não encontrado",
          description: "O aluno solicitado não existe ou foi removido.",
          variant: "destructive",
        });
        navigate('/dashboard');
        return;
      }

      setStudent(foundStudent);

      // Carregar cursos matriculados
      const allCourses = getStoredData<Course[]>('ecurso_courses', []);
      const enrolledCourses = allCourses.filter(course => 
        foundStudent.enrolledCourses.includes(course.id)
      );
      setCourses(enrolledCourses);

      // Carregar progresso do aluno
      const allProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
      const studentProgress = allProgress.filter(p => p.studentId === id);
      setProgress(studentProgress);

      setLoading(false);
    };

    loadStudentData();
  }, [id, navigate, toast]);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const getCourseProgress = (courseId: string): StudentProgress | undefined => {
    return progress.find(p => p.courseId === courseId);
  };

  const getOverallProgress = (): number => {
    if (progress.length === 0) return 0;
    const totalProgress = progress.reduce((acc, p) => acc + p.progress, 0);
    return Math.round(totalProgress / progress.length);
  };

  const getTotalTimeSpent = (): number => {
    return progress.reduce((acc, p) => acc + (p.timeSpent || 0), 0);
  };

  const getCompletedLessons = (): number => {
    return progress.reduce((acc, p) => acc + p.completedLessons.length, 0);
  };

  const getTotalLessons = (): number => {
    return courses.reduce((acc, course) => 
      acc + course.modules.reduce((moduleAcc, module) => 
        moduleAcc + module.lessons.length, 0
      ), 0
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-success/10 text-success border-success/20';
      case 'inactive': return 'bg-muted/50 text-muted-foreground border-muted';
      case 'suspended': return 'bg-destructive/10 text-destructive border-destructive/20';
      default: return 'bg-muted/50 text-muted-foreground border-muted';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Ativo';
      case 'inactive': return 'Inativo';
      case 'suspended': return 'Suspenso';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 loading-spinner mx-auto mb-4" />
          <p>Carregando perfil...</p>
        </div>
      </div>
    );
  }

  if (!student) {
    return null;
  }

  return (
    <>
      {/* Page Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="hover-lift focus-ring"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </div>
          
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center hover-glow transition-all duration-300">
                <User className="w-8 h-8 text-white" />
              </div>
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl sm:text-3xl font-bold">{student.name}</h1>
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(student.status)}`}
                  >
                    {getStatusLabel(student.status)}
                  </Badge>
                </div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    {student.email}
                  </div>
                  {student.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      {student.phone}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Cadastrado em: {new Date(student.createdAt).toLocaleDateString('pt-PT')}
                  </div>
                  {student.lastAccess && (
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Último acesso: {new Date(student.lastAccess).toLocaleDateString('pt-PT')}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {user?.type === 'admin' && (
              <div className="flex gap-2">
                <Button variant="outline" className="hover-lift focus-ring">
                  <User className="w-4 h-4 mr-2" />
                  Editar Perfil
                </Button>
                <Button className="btn-primary-enhanced focus-ring">
                  <Award className="w-4 h-4 mr-2" />
                  Relatório
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container-responsive py-8">
        <Tabs defaultValue="overview" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="overview" className="transition-all duration-300 hover-lift focus-ring">
              Visão Geral
            </TabsTrigger>
            <TabsTrigger value="courses" className="transition-all duration-300 hover-lift focus-ring">
              Cursos
            </TabsTrigger>
            <TabsTrigger value="progress" className="transition-all duration-300 hover-lift focus-ring">
              Progresso
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 animate-slide-up">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="shadow-card card-interactive animate-scale-in">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <BookOpen className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Cursos</p>
                      <p className="text-2xl font-bold">{courses.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.1s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <CheckCircle className="w-6 h-6 text-success" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Lições Concluídas</p>
                      <p className="text-2xl font-bold">{getCompletedLessons()}/{getTotalLessons()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-warning/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <TrendingUp className="w-6 h-6 text-warning" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Progresso Geral</p>
                      <p className="text-2xl font-bold">{getOverallProgress()}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.3s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <Clock className="w-6 h-6 text-secondary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Tempo Total</p>
                      <p className="text-2xl font-bold">{getTotalTimeSpent()}min</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Student Information */}
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Informações do Aluno</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Nome Completo</label>
                      <p className="text-lg">{student.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email</label>
                      <p className="text-lg">{student.email}</p>
                    </div>
                    {student.phone && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Telefone</label>
                        <p className="text-lg">{student.phone}</p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="mt-1">
                        <Badge 
                          variant="outline" 
                          className={`${getStatusColor(student.status)}`}
                        >
                          {getStatusLabel(student.status)}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Data de Cadastro</label>
                      <p className="text-lg">{new Date(student.createdAt).toLocaleDateString('pt-PT')}</p>
                    </div>
                    {student.lastAccess && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Último Acesso</label>
                        <p className="text-lg">{new Date(student.lastAccess).toLocaleDateString('pt-PT')}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courses" className="space-y-6 animate-slide-up">
            <div className="grid gap-4">
              {courses.map((course) => {
                const courseProgress = getCourseProgress(course.id);
                return (
                  <Card key={course.id} className="shadow-card card-interactive animate-fade-in">
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                            <BookOpen className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{course.title}</h3>
                            <p className="text-sm text-muted-foreground">{course.instructor}</p>
                            <p className="text-xs text-muted-foreground">{course.duration}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">Progresso</p>
                            <div className="flex items-center gap-2">
                              <Progress value={courseProgress?.progress || 0} className="w-20" />
                              <span className="text-sm font-medium">{courseProgress?.progress || 0}%</span>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="hover-lift focus-ring"
                              onClick={() => navigate(`/course/${course.id}`)}
                            >
                              Ver Curso
                            </Button>
                            <Button 
                              size="sm" 
                              className="btn-primary-enhanced focus-ring"
                            >
                              <Play className="w-4 h-4 mr-2" />
                              Continuar
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
              
              {courses.length === 0 && (
                <div className="text-center py-12 text-muted-foreground border-2 border-dashed rounded-lg">
                  <BookOpen className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">Nenhum curso matriculado</p>
                  <p className="text-sm">Este aluno ainda não está matriculado em nenhum curso.</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="progress" className="space-y-6 animate-slide-up">
            <div className="grid gap-4">
              {progress.map((prog) => {
                const course = courses.find(c => c.id === prog.courseId);
                if (!course) return null;
                
                return (
                  <Card key={prog.courseId} className="shadow-card animate-fade-in">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="w-5 h-5 text-primary" />
                        {course.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Progresso Geral</span>
                          <span className="text-sm font-bold">{prog.progress}%</span>
                        </div>
                        <Progress value={prog.progress} className="w-full" />
                        
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Lições Concluídas:</span>
                            <p className="font-medium">{prog.completedLessons.length}</p>
                          </div>
                          {prog.timeSpent && (
                            <div>
                              <span className="text-muted-foreground">Tempo Gasto:</span>
                              <p className="font-medium">{prog.timeSpent} minutos</p>
                            </div>
                          )}
                          {prog.lastAccessed && (
                            <div>
                              <span className="text-muted-foreground">Último Acesso:</span>
                              <p className="font-medium">{new Date(prog.lastAccessed).toLocaleDateString('pt-PT')}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default StudentProfile;
