﻿window.globalProvideData('slide', '{"title":"Untitled Slide","trackViews":true,"showMenuResultIcon":false,"viewGroupId":"","historyGroupId":"","videoZoom":"","scrolling":false,"transition":"appear","transDuration":0,"transDir":1,"wipeTrans":false,"slideLock":false,"navIndex":-1,"globalAudioId":"","thumbnailid":"","slideNumberInScene":4,"includeInSlideCounts":true,"presenterRef":{"id":"none"},"slideLayers":[{"enableSeek":true,"enableReplay":true,"timeline":{"duration":9500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5bejcmFjdEo"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5laLzm2QlEG"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6Xo9Yu9ih2Z"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nt6k7PH6Ka"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5qB7KBWe296.5qB7KBWe296_expandinglabel.content_scroll_5qB7KBWe296"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5qB7KBWe296.5qB7KBWe296_expandinglabel.content_scroll_5qB7KBWe296.61Y1pyh3Wwq"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5qB7KBWe296"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6AQHtUbW082"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6krz8peGydI"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"61jub7Qy8OO"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5W3Xv2j6uLu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5hha4NXNQWL"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ctqN84HwS5"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fjXMMrmwFm"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5v2NzpQVpmK"}}]},{"kind":"ontimelinetick","time":1500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6IQamCBu9up"}}]},{"kind":"ontimelinetick","time":5250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"62Z8hF80mGS"}}]},{"kind":"ontimelinetick","time":5750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6QA7CStqMGr"}}]},{"kind":"ontimelinetick","time":6500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69vlBjD71lA"}}]}]},"objects":[{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":35,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":34,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":1}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":2}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":49,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fgE044eYLG.6cac0NAnUiv"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":39,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":38,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":3}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":4}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":51,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.5Xlh34Ekb5w"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":37,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":36,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":5}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":6}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":50,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":3,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6QtAxFNOoxm"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_6ctqN84HwS5","type":"richvartext","xPos":10,"yPos":5,"xAccOffset":0,"yAccOffset":0,"width":212,"height":30,"device":false,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Pontuação: ","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"%_player.nota%","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"/20","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":28,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":135,"yPos":672,"tabIndex":27,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":41,"altText":"Pontuação: %_player.nota%/20","pngfb":false,"pr":{"l":"Lib","i":7}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":40,"resume":false,"useHandCursor":true,"id":"6ctqN84HwS5"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PYUvVg1zLz_545423499","id":"01","linkId":"txt__default_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6dBYEI4LvKs_2042009458","id":"02","linkId":"txt__default_Disabled_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":12}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":32,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":false,"useHandCursor":true,"id":"6fjXMMrmwFm","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":14,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":false,"useHandCursor":true,"id":"5v2NzpQVpmK"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":112,"yPos":8,"tabIndex":17,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":579,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1158,"bottom":40,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":14}},"html5data":{"xPos":-1,"yPos":-1,"width":1159,"height":41,"strokewidth":0}},"width":1158,"height":40,"resume":false,"useHandCursor":true,"id":"6AQHtUbW082"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6krz8peGydI_1913940565","id":"01","linkId":"txt__default_6krz8peGydI","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: FUNCIONAMENTO DA IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":23,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":242,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":364}}}],"shapemaskId":"","xPos":128,"yPos":12,"tabIndex":18,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: FUNCIONAMENTO DA IA","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":false,"useHandCursor":true,"id":"6krz8peGydI"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"60ldkGklJOn_-1184010334","id":"01","linkId":"txt__default_5bejcmFjdEo","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"6poqED4XXLu_-840728632","id":"02","linkId":"txt__default_Disabled_5bejcmFjdEo","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":26,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":false,"useHandCursor":true,"id":"5bejcmFjdEo","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5ZcIVIKkL4Z.6UTSUk0McK3"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5laLzm2QlEG_814765736","id":"01","linkId":"txt__default_5laLzm2QlEG","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":780,"height":54,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Áreas ","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}},{"text":"da","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}},{"text":" Inteligência Artificial","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":32,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":592,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":418}}}],"shapemaskId":"","xPos":240,"yPos":88,"tabIndex":19,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":400,"rotateYPos":32,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":800,"bottom":64,"altText":"Áreas da Inteligência Artificial","pngfb":false,"pr":{"l":"Lib","i":197}},"html5data":{"xPos":0,"yPos":0,"width":800,"height":64,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":800,"height":64,"resume":false,"useHandCursor":true,"id":"5laLzm2QlEG"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":22,"id":"01","url":"story_content/6l2TQAIb06M.png","type":"normal","altText":"Image 2.png","width":1748,"height":1748,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":944,"yPos":296,"tabIndex":21,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":176,"rotateYPos":176,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":352,"bottom":352,"altText":"Image 2.png","pngfb":false,"pr":{"l":"Lib","i":426}},"html5data":{"xPos":0,"yPos":0,"width":352,"height":352,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":352,"height":352,"resume":false,"useHandCursor":true,"id":"6QA7CStqMGr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69vlBjD71lA_-1119362358","id":"01","linkId":"txt__default_69vlBjD71lA","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":516,"height":174,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Ferramentas como o ChatGPT geram textos com base em prompts fornecidos pelos utilizadores, enquanto ferramentas como o DALL-E geram imagens realistas com base em descrições textuais.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":182,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":516,"bottom":147,"pngfb":false,"pr":{"l":"Lib","i":427}}}],"shapemaskId":"","xPos":80,"yPos":416,"tabIndex":23,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":268,"rotateYPos":92,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":536,"bottom":184,"altText":"Ferramentas como o ChatGPT geram textos com base em prompts fornecidos pelos utilizadores, enquanto ferramentas como o DALL-E geram imagens realistas com base em descrições textuais.","pngfb":false,"pr":{"l":"Lib","i":410}},"html5data":{"xPos":-1,"yPos":-1,"width":537,"height":185,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":536,"height":184,"resume":false,"useHandCursor":true,"id":"69vlBjD71lA"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"62Z8hF80mGS_-1748497591","id":"01","linkId":"txt__default_62Z8hF80mGS","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":26,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Exemplo prático:","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":16,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":165,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":413}}}],"shapemaskId":"","xPos":104,"yPos":350,"tabIndex":22,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":18,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":36,"altText":"Exemplo prático:","pngfb":false,"pr":{"l":"Lib","i":412}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":36,"strokewidth":0}},"width":640,"height":36,"resume":false,"useHandCursor":true,"id":"62Z8hF80mGS"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6IQamCBu9up_302027492","id":"01","linkId":"txt__default_6IQamCBu9up","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1116,"height":142,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"IA Generativa ","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}},{"text":"é uma área emergente da IA que envolve a criação de novos conteúdos, como textos, imagens, músicas ou vídeos, a partir de dados existentes. Usa modelos complexos, como redes neuronais generativas, para produzir resultados que parecem criados por humanos. Recentemente, ferramentas ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"como ChatGPT e DALL-E ","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}},{"text":"trouxeram grande visibilidade para essa área.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":364,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":1071,"bottom":128,"pngfb":false,"pr":{"l":"Lib","i":428}}}],"shapemaskId":"","xPos":80,"yPos":168,"tabIndex":20,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":568,"rotateYPos":76,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1137,"bottom":153,"altText":"A IA Generativa é uma área emergente da IA que envolve a criação de novos conteúdos, como textos, imagens, músicas ou vídeos, a partir de dados existentes. Usa modelos complexos, como redes neuronais generativas, para produzir resultados que parecem criados por humanos. Recentemente, ferramentas como ChatGPT e DALL-E trouxeram grande visibilidade para essa área.","pngfb":false,"pr":{"l":"Lib","i":416}},"html5data":{"xPos":-2,"yPos":-2,"width":1139,"height":155,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1136,"height":152,"resume":false,"useHandCursor":true,"id":"6IQamCBu9up"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6Xo9Yu9ih2Z_-953515562","id":"01","linkId":"txt__default_6Xo9Yu9ih2Z","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":489,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"(Clique em “próximo” para continuar)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":36,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":403,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":362}}}],"shapemaskId":"","xPos":386,"yPos":672,"tabIndex":24,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":254.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":15,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":509,"bottom":40,"altText":"(Clique em “próximo” para continuar)","pngfb":false,"pr":{"l":"Lib","i":66}},"html5data":{"xPos":-1,"yPos":-1,"width":510,"height":41,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":509,"height":40,"resume":false,"useHandCursor":true,"id":"6Xo9Yu9ih2Z"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5nt6k7PH6Ka_-1925527724","id":"01","linkId":"txt__default_5nt6k7PH6Ka","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":132,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"04/10","style":{"fontSize":14,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":5,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":62,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":429}}}],"shapemaskId":"","xPos":1072,"yPos":672,"tabIndex":33,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":76,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":16,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":152,"bottom":33,"altText":"04/10","pngfb":false,"pr":{"l":"Lib","i":70}},"html5data":{"xPos":0,"yPos":0,"width":152,"height":33,"strokewidth":0}},"width":152,"height":33,"resume":false,"useHandCursor":true,"id":"5nt6k7PH6Ka"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":1,"arrowxpos":203,"arrowypos":156,"objects":[{"kind":"scrollarea","contentwidth":396,"contentheight":99,"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"61Y1pyh3Wwq_-1721074523","id":"01","linkId":"txt__default_61Y1pyh3Wwq","type":"acctext","xPos":0,"yPos":0,"xAccOffset":0,"yAccOffset":0,"width":396,"height":57,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\tA análise preditiva com IA permite reduzir o turnover em até 10^%^ ou 15^%^, optimizando estratégias de retenção de colaboradores.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":14.373,"descent":3.447,"leading":0,"underlinePosition":-0.748,"underlineThickness":0.719,"xHeight":8.067}}],"style":{"flowDirection":"leftToRight","leadingMargin":24,"trailingMargin":0,"firstLineMargin":0,"justification":"left","listLevel":0,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":127,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":11,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":389,"bottom":56,"pngfb":false,"pr":{"l":"Lib","i":430}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":31,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":198,"rotateYPos":28.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":1300,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":396,"bottom":57,"altText":"\\tA análise preditiva com IA permite reduzir o turnover em até 10^%^ ou 15^%^, optimizando estratégias de retenção de colaboradores.","pngfb":false,"pr":{"l":"Lib","i":299}},"html5data":{"xPos":0,"yPos":0,"width":396,"height":57,"strokewidth":0}},"width":396,"height":57,"resume":false,"useHandCursor":true,"id":"61Y1pyh3Wwq"}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":30,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":198,"rotateYPos":49.5,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":0,"scrolling":true,"shuffleLock":false,"width":396,"height":99,"resume":false,"useHandCursor":true,"id":"content_scroll_5qB7KBWe296"}],"textLib":[{"kind":"textdata","uniqueId":"6T6O0pdmeL9_-517310977","id":"01","linkId":"txt__default_6T6O0pdmeL9","type":"acctext","xPos":0,"yPos":0,"xAccOffset":0,"yAccOffset":0,"width":396,"height":22,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Curiosidade","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":13.6,"descent":4.178,"leading":3.555,"underlinePosition":-0.693,"underlineThickness":1.422,"xHeight":10.169,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":13.333,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":100,"bottom":26,"pngfb":false,"pr":{"l":"Lib","i":301}}}],"shapemaskId":"","xPos":-189,"yPos":-142,"tabIndex":29,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0x262626","alpha":100,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":396,"bottom":22,"altText":"Curiosidade","pngfb":false,"pr":{"l":"Lib","i":298}},"html5data":{"xPos":0,"yPos":0,"width":396,"height":22,"strokewidth":0}},"width":406,"height":138,"resume":false,"useHandCursor":true,"id":"5qB7KBWe296_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":28,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":302}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5qB7KBWe296"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5qB7KBWe296"}}],"clickdef":[{"kind":"objref","type":"string","value":"5qB7KBWe296"}]}],"shapemaskId":"","xPos":856,"yPos":672,"tabIndex":48,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":17,"scrolling":true,"shuffleLock":false,"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5qB7KBWe296","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5qB7KBWe296_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5qB7KBWe296"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5qB7KBWe296_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5qB7KBWe296_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5qB7KBWe296_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"startTime":-1,"elapsedTimeMode":"normal","useHandCursor":false,"resume":false,"kind":"slidelayer","isBaseLayer":true},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":39500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tan86yaQfz"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VCYeYUEni1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69s3gqNHg1Y"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ayzt09NtEH"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5whFHQDPFcs"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6jXPK0rdGU9"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lVfqqZUc0d"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VHGXGQPaHr"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6dIY6ufVyqZ"}}]},{"kind":"ontimelinetick","time":7979,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"65Z2OtQpB8s"}}]},{"kind":"ontimelinetick","time":12750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6a0vWVXybMh"}}]},{"kind":"ontimelinetick","time":19000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5l4SITyHfG9"}}]},{"kind":"ontimelinetick","time":26000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5r3n1dkaOym"}}]},{"kind":"ontimelinetick","time":31500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ht0I6wfKd3"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":0,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"5tan86yaQfz"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":72,"tabIndex":1,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":291.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":585,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":29}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":586,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":584,"resume":true,"useHandCursor":true,"id":"5ayzt09NtEH"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":2,"id":"01","url":"story_content/6Q70oTwPFfL_RC36618.png","type":"normal","altText":"target-audience.png","width":512,"height":512,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":80,"yPos":192,"tabIndex":4,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":160,"rotateYPos":160,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":320,"bottom":320,"altText":"target-audience.png","pngfb":false,"pr":{"l":"Lib","i":30}},"html5data":{"xPos":0,"yPos":0,"width":320,"height":320,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":320,"height":320,"resume":true,"useHandCursor":true,"id":"6VCYeYUEni1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69s3gqNHg1Y_-519312530","id":"01","linkId":"txt__default_69s3gqNHg1Y","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":652,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"OBJECTIVOS DO CURSO","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"linkColor":"#1888E7","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":452,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":32}}}],"shapemaskId":"","xPos":504,"yPos":104,"tabIndex":3,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":336,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":672,"bottom":40,"altText":"OBJECTIVOS DO CURSO","pngfb":false,"pr":{"l":"Lib","i":31}},"html5data":{"xPos":0,"yPos":0,"width":672,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":672,"height":40,"resume":true,"useHandCursor":true,"id":"69s3gqNHg1Y"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":2,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5lVfqqZUc0d","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_5whFHQDPFcs","type":"hiddentext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":756,"height":374,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":5,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":776,"bottom":384,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\nConhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\nIdentificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\nEntender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\nReconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\nFamiliarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\nExplorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\nCompreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","pngfb":false,"pr":{"l":"Lib","i":35}},"html5data":{"xPos":0,"yPos":0,"width":776,"height":384,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":true,"useHandCursor":true,"id":"5whFHQDPFcs"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5ht0I6wfKd3","id":"01","linkId":"txt_5ht0I6wfKd3","type":"acctext","xPos":10,"yPos":330,"xAccOffset":10,"yAccOffset":330,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":694,"bottom":376,"pngfb":false,"pr":{"l":"Lib","i":37}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":40,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":694,"bottom":376,"altText":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":694,"height":376,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5ht0I6wfKd3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5r3n1dkaOym","id":"01","linkId":"txt_5r3n1dkaOym","type":"acctext","xPos":10,"yPos":273,"xAccOffset":10,"yAccOffset":273,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":746,"bottom":319,"pngfb":false,"pr":{"l":"Lib","i":38}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":41,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":746,"bottom":319,"altText":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":746,"height":319,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5r3n1dkaOym"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5l4SITyHfG9","id":"01","linkId":"txt_5l4SITyHfG9","type":"acctext","xPos":10,"yPos":238,"xAccOffset":10,"yAccOffset":238,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":700,"bottom":261,"pngfb":false,"pr":{"l":"Lib","i":39}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":42,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":700,"bottom":261,"altText":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":700,"height":261,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5l4SITyHfG9"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6a0vWVXybMh","id":"01","linkId":"txt_6a0vWVXybMh","type":"acctext","xPos":10,"yPos":181,"xAccOffset":10,"yAccOffset":181,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":671,"bottom":227,"pngfb":false,"pr":{"l":"Lib","i":40}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":43,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":671,"bottom":227,"altText":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":671,"height":227,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6a0vWVXybMh"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_65Z2OtQpB8s","id":"01","linkId":"txt_65Z2OtQpB8s","type":"acctext","xPos":10,"yPos":123,"xAccOffset":10,"yAccOffset":123,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":742,"bottom":169,"pngfb":false,"pr":{"l":"Lib","i":41}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":44,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":742,"bottom":169,"altText":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":742,"height":169,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"65Z2OtQpB8s"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6dIY6ufVyqZ","id":"01","linkId":"txt_6dIY6ufVyqZ","type":"acctext","xPos":10,"yPos":66,"xAccOffset":10,"yAccOffset":66,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":714,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":42}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":45,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":714,"bottom":112,"altText":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":714,"height":112,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6dIY6ufVyqZ"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6VHGXGQPaHr","id":"01","linkId":"txt_6VHGXGQPaHr","type":"acctext","xPos":10,"yPos":9,"xAccOffset":10,"yAccOffset":9,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":705,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":43}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":46,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":705,"bottom":54,"altText":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":705,"height":54,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6VHGXGQPaHr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6jXPK0rdGU9","id":"01","linkId":"txt_6jXPK0rdGU9","type":"acctext","xPos":10,"yPos":-26,"xAccOffset":10,"yAccOffset":-26,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":-27,"right":610,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":44}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":47,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":-27,"right":610,"bottom":0,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":-27,"width":610,"height":27,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6jXPK0rdGU9"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6QtAxFNOoxm"},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TRpicPw6tU"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VcGQGd6sQ4"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6HniC2d4w8h"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nw6rMDcq88"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5wH7XkMdNj1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fLNGDgml4K"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5a1AGGT21Cq"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ex489sIWwL"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":6,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6TRpicPw6tU"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":64,"tabIndex":7,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":279.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":561,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":45}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":562,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":560,"resume":true,"useHandCursor":true,"id":"5VcGQGd6sQ4"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":3,"id":"01","url":"story_content/6S7pEErDZEG.png","type":"normal","altText":"Agrupar 1.png","width":959,"height":657,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":1,"yPos":80,"tabIndex":8,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":391,"rotateYPos":268,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":782,"bottom":536,"altText":"Agrupar 1.png","pngfb":false,"pr":{"l":"Lib","i":46}},"html5data":{"xPos":0,"yPos":0,"width":782,"height":536,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":782,"height":536,"resume":true,"useHandCursor":true,"id":"6HniC2d4w8h"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":16,"yPos":94,"tabIndex":10,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":40,"rotateYPos":18.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":80,"bottom":37,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":47}},"html5data":{"xPos":0,"yPos":0,"width":80,"height":37,"strokewidth":0}},"width":80,"height":37,"resume":true,"useHandCursor":true,"id":"5nw6rMDcq88"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":9,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5wH7XkMdNj1","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6fLNGDgml4K_1529763356","id":"01","linkId":"txt__default_6fLNGDgml4K","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":308,"height":190,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Play/Pause\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Timeline\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reiniciar ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Volume\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Voltar ao menu\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Objectivos do curso","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":191,"bottom":193,"pngfb":false,"pr":{"l":"Lib","i":49}}}],"shapemaskId":"","xPos":24,"yPos":128,"tabIndex":11,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":164,"rotateYPos":100,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":328,"bottom":200,"altText":"Play/Pause\\nTimeline\\nReiniciar ecrã\\nVolume\\nVoltar ao menu\\nObjectivos do curso","pngfb":false,"pr":{"l":"Lib","i":48}},"html5data":{"xPos":0,"yPos":0,"width":328,"height":200,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":328,"height":200,"resume":true,"useHandCursor":true,"id":"6fLNGDgml4K"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5a1AGGT21Cq_1013326046","id":"01","linkId":"txt__default_5a1AGGT21Cq","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":420,"height":222,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"7.     Instruções de navegação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"8.      A sua pontuação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"9.      Instruções de interacções no ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"10.   Notas/curiosidade\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"11.   Voltar ao ecrã anterior\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":30,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"12.   Paginação \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":17,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"13.   Avançar para o próximo ecrã","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":33,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":225,"pngfb":false,"pr":{"l":"Lib","i":51}}}],"shapemaskId":"","xPos":320,"yPos":128,"tabIndex":12,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":220,"rotateYPos":116,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":440,"bottom":232,"altText":"7.     Instruções de navegação\\n8.      A sua pontuação\\n9.      Instruções de interacções no ecrã\\n10.   Notas/curiosidade\\n11.   Voltar ao ecrã anterior\\n12.   Paginação \\n13.   Avançar para o próximo ecrã","pngfb":false,"pr":{"l":"Lib","i":50}},"html5data":{"xPos":0,"yPos":0,"width":440,"height":232,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":440,"height":232,"resume":true,"useHandCursor":true,"id":"5a1AGGT21Cq"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6ex489sIWwL_1502302535","id":"01","linkId":"txt__default_6ex489sIWwL","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":428,"height":363,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":136,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Ao finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":133,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":153,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":435,"bottom":368,"pngfb":false,"pr":{"l":"Lib","i":53}}}],"shapemaskId":"","xPos":808,"yPos":168,"tabIndex":13,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":224,"rotateYPos":186.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":448,"bottom":373,"altText":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\nAo finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\nA pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","pngfb":false,"pr":{"l":"Lib","i":52}},"html5data":{"xPos":0,"yPos":0,"width":448,"height":373,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":448,"height":373,"resume":true,"useHandCursor":true,"id":"6ex489sIWwL"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5Xlh34Ekb5w"}],"showAnimationId":"","lmsId":"Slide4","width":1280,"height":720,"resume":false,"background":{"type":"fill","fill":{"type":"linear","rotation":90,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}},"id":"66NJgRIDD6K","events":[{"kind":"onslidestart","actions":[{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#3_4","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5bejcmFjdEo.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5bejcmFjdEo"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5bejcmFjdEo.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5bejcmFjdEo.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5bejcmFjdEo"}}],"elseActions":[{"kind":"adjustvar","variable":"5bejcmFjdEo._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"5bejcmFjdEo._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5bejcmFjdEo.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5bejcmFjdEo"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5bejcmFjdEo"}}]}]}]},{"kind":"onbeforeslidein","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"npnxnanbnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"npnxnanbnsnfns10110000101"}]}]},{"kind":"ontimelinecomplete","actions":[{"kind":"adjustvar","variable":"_player.3_4","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"onvarchanged","varname":"_player.3_4","priority":0,"actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#3_4","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5bejcmFjdEo.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5bejcmFjdEo"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5bejcmFjdEo.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5bejcmFjdEo.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5bejcmFjdEo"}}],"elseActions":[{"kind":"adjustvar","variable":"5bejcmFjdEo._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"5bejcmFjdEo._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5bejcmFjdEo.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5bejcmFjdEo"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5bejcmFjdEo"}}]}]}]},{"kind":"ontransitionin","actions":[{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"set","value":{"type":"string","value":"_player."}},{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"add","value":{"type":"property","value":"$AbsoluteId"}}]}]}');