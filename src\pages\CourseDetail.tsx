import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Course, Student, StudentProgress, getStoredData } from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';
import { 
  BookOpen, 
  Clock, 
  User, 
  Users, 
  ArrowLeft, 
  Play, 
  CheckCircle,
  FileText,
  Award,
  Calendar
} from 'lucide-react';

const CourseDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [course, setCourse] = useState<Course | null>(null);
  const [students, setStudents] = useState<Student[]>([]);
  const [progress, setProgress] = useState<StudentProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCourseData = () => {
      if (!id) {
        navigate('/dashboard');
        return;
      }

      const courses = getStoredData<Course[]>('ecurso_courses', []);
      const foundCourse = courses.find(c => c.id === id);
      
      if (!foundCourse) {
        toast({
          title: "Curso não encontrado",
          description: "O curso solicitado não existe ou foi removido.",
          variant: "destructive",
        });
        navigate('/dashboard');
        return;
      }

      setCourse(foundCourse);

      // Carregar alunos matriculados
      const allStudents = getStoredData<Student[]>('ecurso_students', []);
      const enrolledStudents = allStudents.filter(student => 
        foundCourse.students.includes(student.id)
      );
      setStudents(enrolledStudents);

      // Carregar progresso dos alunos
      const allProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
      const courseProgress = allProgress.filter(p => p.courseId === id);
      setProgress(courseProgress);

      setLoading(false);
    };

    loadCourseData();
  }, [id, navigate, toast]);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const getStudentProgress = (studentId: string): number => {
    const studentProgress = progress.find(p => p.studentId === studentId);
    return studentProgress?.progress || 0;
  };

  const getTotalLessons = (): number => {
    if (!course) return 0;
    return course.modules.reduce((acc, module) => acc + module.lessons.length, 0);
  };

  const getCompletedLessons = (): number => {
    if (!course) return 0;
    return progress.reduce((acc, p) => acc + p.completedLessons.length, 0);
  };

  const getAverageProgress = (): number => {
    if (progress.length === 0) return 0;
    const totalProgress = progress.reduce((acc, p) => acc + p.progress, 0);
    return Math.round(totalProgress / progress.length);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 loading-spinner mx-auto mb-4" />
          <p>Carregando curso...</p>
        </div>
      </div>
    );
  }

  if (!course) {
    return null;
  }

  return (
    <>
      {/* Page Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="hover-lift focus-ring"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center hover-glow transition-all duration-300">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">{course.title}</h1>
                <p className="text-muted-foreground">{course.description}</p>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {course.instructor}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {course.duration}
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {students.length} alunos
                  </div>
                </div>
              </div>
            </div>

            {user?.type === 'admin' && (
              <div className="flex gap-2">
                <Button variant="outline" className="hover-lift focus-ring">
                  <FileText className="w-4 h-4 mr-2" />
                  Editar Curso
                </Button>
                <Button className="btn-primary-enhanced focus-ring">
                  <Award className="w-4 h-4 mr-2" />
                  Relatório
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container-responsive py-8">
        <Tabs defaultValue="overview" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="overview" className="transition-all duration-300 hover-lift focus-ring">
              Visão Geral
            </TabsTrigger>
            <TabsTrigger value="modules" className="transition-all duration-300 hover-lift focus-ring">
              Módulos
            </TabsTrigger>
            <TabsTrigger value="students" className="transition-all duration-300 hover-lift focus-ring">
              Alunos
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 animate-slide-up">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="shadow-card card-interactive animate-scale-in">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <BookOpen className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Módulos</p>
                      <p className="text-2xl font-bold">{course.modules.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.1s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <FileText className="w-6 h-6 text-secondary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Lições</p>
                      <p className="text-2xl font-bold">{getTotalLessons()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <Users className="w-6 h-6 text-success" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Alunos</p>
                      <p className="text-2xl font-bold">{students.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.3s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-warning/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <Award className="w-6 h-6 text-warning" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Progresso Médio</p>
                      <p className="text-2xl font-bold">{getAverageProgress()}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Course Description */}
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Sobre o Curso</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {course.description}
                </p>
                <div className="mt-6 flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>Criado em: {new Date(course.createdAt).toLocaleDateString('pt-PT')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>Duração: {course.duration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span>Instrutor: {course.instructor}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="modules" className="space-y-6 animate-slide-up">
            {course.modules.map((module, moduleIndex) => (
              <Card key={module.id} className="shadow-card animate-fade-in">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center text-white text-sm font-bold">
                      {moduleIndex + 1}
                    </div>
                    {module.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {module.lessons.map((lesson, lessonIndex) => (
                      <div key={lesson.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center text-xs font-medium">
                          {lessonIndex + 1}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{lesson.title}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {lesson.type === 'video' ? 'Vídeo' : 'Texto'}
                            </Badge>
                            {lesson.duration && (
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {lesson.duration}
                              </span>
                            )}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" className="hover-lift focus-ring">
                          <Play className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="students" className="space-y-6 animate-slide-up">
            <div className="grid gap-4">
              {students.map((student) => {
                const studentProgress = getStudentProgress(student.id);
                return (
                  <Card key={student.id} className="shadow-card card-interactive animate-fade-in">
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center hover-glow transition-all duration-300">
                            <User className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{student.name}</h3>
                            <p className="text-sm text-muted-foreground">{student.email}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">Progresso</p>
                            <div className="flex items-center gap-2">
                              <Progress value={studentProgress} className="w-20" />
                              <span className="text-sm font-medium">{studentProgress}%</span>
                            </div>
                          </div>
                          
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="hover-lift focus-ring"
                            onClick={() => navigate(`/student/${student.id}`)}
                          >
                            Ver Perfil
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
              
              {students.length === 0 && (
                <div className="text-center py-12 text-muted-foreground border-2 border-dashed rounded-lg">
                  <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">Nenhum aluno matriculado</p>
                  <p className="text-sm">Este curso ainda não possui alunos matriculados.</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default CourseDetail;
