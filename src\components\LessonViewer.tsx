import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Lesson, Module, Course, StudentProgress, setStoredData, getStoredData } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';
import { Play, BookOpen, CheckCircle, ChevronLeft, ChevronRight, Clock } from 'lucide-react';

interface LessonViewerProps {
  course: Course;
  currentModule: Module;
  currentLesson: Lesson;
  onBack: () => void;
  onLessonComplete: (lessonId: string) => void;
}

const LessonViewer: React.FC<LessonViewerProps> = ({
  course,
  currentModule,
  currentLesson,
  onBack,
  onLessonComplete
}) => {
  const { user } = useAuth();
  const [isCompleted, setIsCompleted] = useState(false);
  const [videoWatched, setVideoWatched] = useState(false);

  useEffect(() => {
    // Check if lesson is already completed
    const progress = getStoredData<StudentProgress[]>('ecurso_progress', []);
    const userProgress = progress.find(p => 
      p.studentId === user?.id && p.courseId === course.id
    );
    
    if (userProgress?.completedLessons.includes(currentLesson.id)) {
      setIsCompleted(true);
    }
  }, [currentLesson.id, course.id, user?.id]);

  const handleCompleteLesson = () => {
    if (!user) return;
    
    const progress = getStoredData<StudentProgress[]>('ecurso_progress', []);
    const userProgressIndex = progress.findIndex(p => 
      p.studentId === user.id && p.courseId === course.id
    );
    
    if (userProgressIndex >= 0) {
      if (!progress[userProgressIndex].completedLessons.includes(currentLesson.id)) {
        progress[userProgressIndex].completedLessons.push(currentLesson.id);
        
        // Calculate new progress percentage
        const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0);
        const completedCount = progress[userProgressIndex].completedLessons.length;
        progress[userProgressIndex].progress = Math.round((completedCount / totalLessons) * 100);
        
        setStoredData('ecurso_progress', progress);
        setIsCompleted(true);
        onLessonComplete(currentLesson.id);
      }
    }
  };

  const simulateVideoWatch = () => {
    // Simulate watching video for 3 seconds
    setTimeout(() => {
      setVideoWatched(true);
    }, 3000);
  };

  useEffect(() => {
    if (currentLesson.type === 'video') {
      simulateVideoWatch();
    }
  }, [currentLesson]);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" onClick={onBack}>
              <ChevronLeft className="w-4 h-4 mr-2" />
              Voltar ao Curso
            </Button>
          </div>
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center">
              {currentLesson.type === 'video' ? (
                <Play className="w-6 h-6 text-white" />
              ) : (
                <BookOpen className="w-6 h-6 text-white" />
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold">{currentLesson.title}</h1>
              <p className="text-muted-foreground">
                {currentModule.title} • {course.title}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card className="shadow-card">
              <CardContent className="p-6">
                {currentLesson.type === 'video' ? (
                  <div className="space-y-4">
                    {/* Simulated Video Player */}
                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-primary opacity-10"></div>
                      <div className="text-center z-10">
                        <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                          <Play className="w-8 h-8 text-white ml-1" />
                        </div>
                        <p className="text-white font-medium">
                          {videoWatched ? 'Vídeo assistido!' : 'Carregando vídeo...'}
                        </p>
                        {currentLesson.duration && (
                          <p className="text-white/80 text-sm flex items-center justify-center gap-1 mt-2">
                            <Clock className="w-4 h-4" />
                            {currentLesson.duration}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {videoWatched && !isCompleted && (
                      <div className="text-center">
                        <Button 
                          onClick={handleCompleteLesson}
                          className="bg-gradient-primary hover:shadow-primary"
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Marcar como Concluído
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="prose max-w-none">
                      <div className="text-lg leading-relaxed">
                        {currentLesson.content}
                      </div>
                    </div>
                    
                    {!isCompleted && (
                      <div className="text-center pt-6 border-t">
                        <Button 
                          onClick={handleCompleteLesson}
                          className="bg-gradient-primary hover:shadow-primary"
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Marcar como Concluído
                        </Button>
                      </div>
                    )}
                  </div>
                )}
                
                {isCompleted && (
                  <div className="text-center py-4">
                    <Badge className="bg-success/10 text-success border-success/20">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Aula Concluída
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Course Progress */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="text-lg">Progresso do Curso</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span>Concluído</span>
                    <span className="font-medium">25%</span>
                  </div>
                  <Progress value={25} className="h-2" />
                  <p className="text-sm text-muted-foreground">
                    Continue assistindo para progredir no curso
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Module Lessons */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="text-lg">{currentModule.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {currentModule.lessons.map((lesson) => (
                    <div 
                      key={lesson.id}
                      className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                        lesson.id === currentLesson.id 
                          ? 'bg-primary/10 border border-primary/20' 
                          : 'bg-muted/30 hover:bg-muted/50'
                      }`}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        lesson.id === currentLesson.id 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-secondary text-secondary-foreground'
                      }`}>
                        {lesson.type === 'video' ? (
                          <Play className="w-4 h-4" />
                        ) : (
                          <BookOpen className="w-4 h-4" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className={`font-medium text-sm ${
                          lesson.id === currentLesson.id ? 'text-primary' : ''
                        }`}>
                          {lesson.title}
                        </h4>
                        {lesson.duration && (
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {lesson.duration}
                          </p>
                        )}
                      </div>
                      {isCompleted && lesson.id === currentLesson.id && (
                        <CheckCircle className="w-4 h-4 text-success" />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LessonViewer;