<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xsd:schema xmlns = "http://www.imsglobal.org/xsd/imscp_v1p1"
	 targetNamespace = "http://www.imsglobal.org/xsd/imscp_v1p1"
	 xmlns:xsd = "http://www.w3.org/2001/XMLSchema"
	 version = "IMS CP 1.1.3"
	 elementFormDefault = "unqualified">
	<xsd:import namespace = "http://www.w3.org/XML/1998/namespace" schemaLocation = "xml.xsd"/>
	<xsd:annotation>
		<xsd:documentation xml:lang = "en">DRAFT XSD for IMS Content Packaging version 1.1 DRAFT                </xsd:documentation>
		<xsd:documentation> Copyright (c) 2001 IMS GLC, Inc.                                                    </xsd:documentation>
		<xsd:documentation>2000-04-21, Adjustments by <PERSON><PERSON><PERSON><PERSON> from CP 1.0.                                   </xsd:documentation>
		<xsd:documentation>2001-02-22, T.<PERSON><PERSON><PERSON><PERSON>: Modify for 2000-10-24 XML-Schema version.                     </xsd:documentation>
		<xsd:documentation> Modified to support extension.                                                      </xsd:documentation>
		<xsd:documentation>2001-03-12, T.D.Wason: Change filename, target and meta-data namespaces              </xsd:documentation>
		<xsd:documentation> and meta-data filename.                                                             </xsd:documentation>
		<xsd:documentation> Add meta-data to itemType, fileType and organizationType.                           </xsd:documentation>
		<xsd:documentation> Do not define namespaces for xml in XML instances generated from this xsd.          </xsd:documentation>
		<xsd:documentation> Imports IMS meta-data xsd, lower case element names.                                </xsd:documentation>
		<xsd:documentation> This XSD provides a reference to the IMS meta-data root element as imsmd:record     </xsd:documentation>
		<xsd:documentation> If the IMS meta-data is to be used in the XML instance then the instance            </xsd:documentation>
		<xsd:documentation> must definean IMS meta-data prefix with a namespace.                                </xsd:documentation>
		<xsd:documentation> The meta-data targetNamespace should be used.                                       </xsd:documentation>
		<xsd:documentation>2001-03-20, Thor Anderson: Remove manifestref, change resourceref back to            </xsd:documentation>
		<xsd:documentation> identifierref, change manifest back to contained by manifest.                       </xsd:documentation>
		<xsd:documentation> --Tom Wason: manifest may contain _none_ or more manifests.                         </xsd:documentation>
		<xsd:documentation>2001-04-13 Tom Wason: corrected attirbute name structure.  Was misnamed type.        </xsd:documentation>
		<xsd:documentation>2001-05-14 Schawn Thropp: Made all complexType extensible with the group.any         </xsd:documentation>
		<xsd:documentation> Added the anyAttribute to all complexTypes.                                         </xsd:documentation>
		<xsd:documentation> Changed the href attribute on the fileType and resourceType to xsd:string           </xsd:documentation>
		<xsd:documentation> Changed the maxLength of the href, identifierref, parameters, structure             </xsd:documentation>
		<xsd:documentation> attributes to match the Information model.                                          </xsd:documentation>
		<xsd:documentation>2001-07-25 Schawn Thropp: Changed the namespace for the Schema of Schemas to         </xsd:documentation>
		<xsd:documentation> the 5/2/2001 W3C XML Schema Recommendation.                                         </xsd:documentation>
		<xsd:documentation> attributeGroup attr.imsmd deleted, was not used anywhere.                           </xsd:documentation>
		<xsd:documentation> Any attribute declarations that have use = "default"                                </xsd:documentation>
		<xsd:documentation> changed to use="optional" - attr.structure.req.                                     </xsd:documentation>
		<xsd:documentation> Any attribute declarations that have value="somevalue" changed to                   </xsd:documentation>
		<xsd:documentation> default="somevalue" - attr.structure.req (hierarchical).                            </xsd:documentation>
		<xsd:documentation> Removed references to IMS MD Version 1.1.                                           </xsd:documentation>
		<xsd:documentation> Modified attribute group "attr.resourcetype.req" to change use from optional        </xsd:documentation>
		<xsd:documentation> to required to match the information model.  As a result the default value          </xsd:documentation>
		<xsd:documentation> also needed to be removed                                                           </xsd:documentation>
		<xsd:documentation> Name change for XSD.  Changed to match version of CP Spec                           </xsd:documentation>
		<xsd:documentation> 2001-11-04 Chris Moffatt:                                                           </xsd:documentation>
		<xsd:documentation>  1. Refer to the xml namespace using the "x" abbreviation instead of "xml".         </xsd:documentation>
		<xsd:documentation>     This changes enables the schema to work with commercial XML Tools               </xsd:documentation>
		<xsd:documentation>  2. Revert to original IMS CP version 1.1 namespace.                                </xsd:documentation>
		<xsd:documentation>     i.e. "http://www.imsglobal.org/xsd/imscp_v1p1"                                  </xsd:documentation>
		<xsd:documentation>     This change done to support the decision to only change the XML namespace with  </xsd:documentation>
		<xsd:documentation>     major revisions of the specification i.e. where the information model or binding</xsd:documentation>
		<xsd:documentation>     changes (as opposed to addressing bugs or omissions). A stable namespace is     </xsd:documentation>
		<xsd:documentation>     necessary to the increasing number of implementors.                             </xsd:documentation>
		<xsd:documentation>  3. Changed name of schema file to "imscp_v1p1p3.xsd" and                           </xsd:documentation>
		<xsd:documentation>     version attribute to "IMS CP 1.1.3" to reflect minor version change             </xsd:documentation>
		<xsd:documentation>Inclusions and Imports                                                               </xsd:documentation>
		<xsd:documentation>Attribute Declarations                                                               </xsd:documentation>
		<xsd:documentation>element groups                                                                       </xsd:documentation>
		<xsd:documentation>                                                                                     
		
		</xsd:documentation>
		<xsd:documentation>2003-03-21 Schawn Thropp                                                             </xsd:documentation>
		<xsd:documentation>The following updates were made to the Version 1.1.3 "Public Draft" version:         </xsd:documentation>
		<xsd:documentation>  1. Updated name of schema file (imscp_v1p1.xsd) to match to IMS naming guideance   </xsd:documentation>
		<xsd:documentation>  2. Updated the import statement to reference the xml.xsd found at                  </xsd:documentation>
		<xsd:documentation>       "http://www.w3.org/2001/03/xml.xsd".  This is the current W3C schema          </xsd:documentation>
		<xsd:documentation>        recommended by the W3C to reference.                                         </xsd:documentation>
		<xsd:documentation>  3. Removed all maxLength's facets.  The maxLength facets was an incorrect binding  </xsd:documentation>
		<xsd:documentation>     implementation.  These lengths were supposed, according to the information      </xsd:documentation>
		<xsd:documentation>     model, to be treated as smallest permitted maximums.                            </xsd:documentation>
		<xsd:documentation>  4. Added the variations content model to support the addition in the information   </xsd:documentation>
		<xsd:documentation>     model.                                                                          </xsd:documentation>
	</xsd:annotation>
	<xsd:group name = "grp.any">
		<xsd:annotation>
			<xsd:documentation>Any namespaced element from any namespace may be included within an "any" element.  The namespace for the imported element must be defined in the instance, and the schema must be imported.  </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:any namespace = "##other" processContents = "strict" minOccurs = "0" maxOccurs = "unbounded"/>
		</xsd:sequence>
	</xsd:group>
	<xsd:attributeGroup name = "attr.version">
		<xsd:attribute name = "version" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.structure.req">
		<xsd:attribute name = "structure" default = "hierarchical" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.resourcetype.req">
		<xsd:attribute name = "type" use = "required" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.identifierref.req">
		<xsd:attribute name = "identifierref" use = "required" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.identifierref">
		<xsd:attribute name = "identifierref" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.parameters">
		<xsd:attribute name = "parameters" type = "xsd:string"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.isvisible">
		<xsd:attribute name = "isvisible" type = "xsd:boolean"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.identifier">
		<xsd:attribute name = "identifier" type = "xsd:ID"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.identifier.req">
		<xsd:attribute name = "identifier" use = "required" type = "xsd:ID"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.href.req">
		<xsd:attribute name = "href" use = "required" type = "xsd:anyURI"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.href">
		<xsd:attribute name = "href" type = "xsd:anyURI"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.default">
		<xsd:attribute name = "default" type = "xsd:IDREF"/>
	</xsd:attributeGroup>
	<xsd:attributeGroup name = "attr.base">
		<xsd:attribute ref = "xml:base"/>
	</xsd:attributeGroup>
	
	<!-- Copyright (2) 2003 IMS Global Learning Consortium, Inc. -->
	
	
	<!-- ******************** -->
	
	
	<!-- ** Change History ** -->
	
	
	<!-- ******************** -->
	
	
	<!-- **************************** -->
	
	
	<!-- ** Attribute Declarations ** -->
	
	
	<!-- **************************** -->
	
	
	<!-- ************************** -->
	
	
	<!-- ** Element Declarations ** -->
	
	
	<!-- ************************** -->
	
	<xsd:element name = "dependency" type = "dependencyType"/>
	<xsd:element name = "file" type = "fileType"/>
	<xsd:element name = "item" type = "itemType"/>
	<xsd:element name = "manifest" type = "manifestType"/>
	<xsd:element name = "metadata" type = "metadataType"/>
	<xsd:element name = "organization" type = "organizationType"/>
	<xsd:element name = "organizations" type = "organizationsType"/>
	<xsd:element name = "resource" type = "resourceType"/>
	<xsd:element name = "resources" type = "resourcesType"/>
	<xsd:element name = "schema" type = "schemaType"/>
	<xsd:element name = "schemaversion" type = "schemaversionType"/>
	<xsd:element name = "title" type = "titleType"/>
	
	<!-- ******************* -->
	
	
	<!-- ** Complex Types ** -->
	
	
	<!-- ******************* -->
	
	
	<!-- **************** -->
	
	
	<!-- ** dependency ** -->
	
	
	<!-- **************** -->
	
	<xsd:complexType name = "dependencyType">
		<xsd:sequence>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.identifierref.req"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ********** -->
	
	
	<!-- ** file ** -->
	
	
	<!-- ********** -->
	
	<xsd:complexType name = "fileType">
		<xsd:sequence>
			<xsd:element ref = "metadata" minOccurs = "0"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.href.req"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ********** -->
	
	
	<!-- ** item ** -->
	
	
	<!-- ********** -->
	
	<xsd:complexType name = "itemType">
		<xsd:sequence>
			<xsd:element ref = "title" minOccurs = "0"/>
			<xsd:element ref = "item" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:element ref = "metadata" minOccurs = "0"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.identifier.req"/>
		<xsd:attributeGroup ref = "attr.identifierref"/>
		<xsd:attributeGroup ref = "attr.isvisible"/>
		<xsd:attributeGroup ref = "attr.parameters"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ************** -->
	
	
	<!-- ** manifest ** -->
	
	
	<!-- ************** -->
	
	<xsd:complexType name = "manifestType">
		<xsd:sequence>
			<xsd:element ref = "metadata" minOccurs = "0"/>
			<xsd:element ref = "organizations"/>
			<xsd:element ref = "resources"/>
			<xsd:element ref = "manifest" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.identifier.req"/>
		<xsd:attributeGroup ref = "attr.version"/>
		<xsd:attribute ref = "xml:base"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ************** -->
	
	
	<!-- ** metadata ** -->
	
	
	<!-- ************** -->
	
	<xsd:complexType name = "metadataType">
		<xsd:sequence>
			<xsd:element ref = "schema" minOccurs = "0"/>
			<xsd:element ref = "schemaversion" minOccurs = "0"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
	</xsd:complexType>
	
	<!-- ******************* -->
	
	
	<!-- ** organizations ** -->
	
	
	<!-- ******************* -->
	
	<xsd:complexType name = "organizationsType">
		<xsd:sequence>
			<xsd:element ref = "organization" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.default"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ****************** -->
	
	
	<!-- ** organization ** -->
	
	
	<!-- ****************** -->
	
	<xsd:complexType name = "organizationType">
		<xsd:sequence>
			<xsd:element ref = "title" minOccurs = "0"/>
			<xsd:element ref = "item" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:element ref = "metadata" minOccurs = "0"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.identifier.req"/>
		<xsd:attributeGroup ref = "attr.structure.req"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- *************** -->
	
	
	<!-- ** resources ** -->
	
	
	<!-- *************** -->
	
	<xsd:complexType name = "resourcesType">
		<xsd:sequence>
			<xsd:element ref = "resource" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.base"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- ************** -->
	
	
	<!-- ** resource ** -->
	
	
	<!-- ************** -->
	
	<xsd:complexType name = "resourceType">
		<xsd:sequence>
			<xsd:element ref = "metadata" minOccurs = "0"/>
			<xsd:element ref = "file" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:element ref = "dependency" minOccurs = "0" maxOccurs = "unbounded"/>
			<xsd:group ref = "grp.any"/>
		</xsd:sequence>
		<xsd:attributeGroup ref = "attr.identifier.req"/>
		<xsd:attributeGroup ref = "attr.resourcetype.req"/>
		<xsd:attributeGroup ref = "attr.base"/>
		<xsd:attributeGroup ref = "attr.href"/>
		<xsd:anyAttribute namespace = "##other" processContents = "strict"/>
	</xsd:complexType>
	
	<!-- *************** -->
	
	
	<!-- ** variation ** -->
	
	
	<!-- *************** -->
	
	
	<!-- ****************** -->
	
	
	<!-- ** Simple Types ** -->
	
	
	<!-- ****************** -->
	
	<xsd:simpleType name = "schemaType">
		<xsd:restriction base = "xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name = "schemaversionType">
		<xsd:restriction base = "xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name = "titleType">
		<xsd:restriction base = "xsd:string"/>
	</xsd:simpleType>
</xsd:schema>