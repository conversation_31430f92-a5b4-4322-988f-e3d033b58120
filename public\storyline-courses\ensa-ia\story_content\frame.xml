<?xml version="1.0" encoding="utf-8"?>
<bwFrame xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" default_layout="npnxnanbnsnfns10110000101" textdirection="ltr" textengine="DirectWrite" device="false" fontscale="100" captionFontScale="100" kbshortcuts="false" skip_nav_enabled="true" chromeless="false" outputtype="undefined" theme="classic">
  <layouts>
    <layout name="npnxnanbsnfns10110000101" font="Neo Sans Std" controllayout="npnxnanbsnfns10110000101" colorscheme="npnxnanbnsnfns10110000101" string_table="npnxnanbnsnfns10110000101" />
    <layout name="pxabnsnfns10110000101" font="Neo Sans Std" controllayout="pxabnsnfns10110000101" colorscheme="npnxnanbnsnfns10110000101" string_table="npnxnanbnsnfns10110000101" />
    <layout name="npxnabnsnfns10110000101" font="Neo Sans Std" controllayout="npxnabnsnfns10110000101" colorscheme="npnxnanbnsnfns10110000101" string_table="npnxnanbnsnfns10110000101" />
    <layout name="npnxnanbnsnfns10110000101" font="Neo Sans Std" controllayout="npnxnanbnsnfns10110000101" colorscheme="npnxnanbnsnfns10110000101" string_table="npnxnanbnsnfns10110000101" />
    <layout name="pxabnsnfns10110000100" font="Neo Sans Std" controllayout="pxabnsnfns10110000100" colorscheme="npnxnanbnsnfns10110000101" string_table="npnxnanbnsnfns10110000101" />
  </layouts>
  <control_options>
    <optiongroups>
      <optiongroup name="sidebaroptions">
        <options>
          <option name="title_enabled" value="false">
            <options>
              <option name="title_text" value="ENSAIntelig&amp;#234;ncia Artificial M1 20250821" />
            </options>
          </option>
          <option name="logo_enabled" value="false">
            <options />
          </option>
          <option name="sidebar_enabled" value="false">
            <options>
              <option name="sidebarpos" value="left" />
              <option name="closed" value="false" />
            </options>
          </option>
          <optionlist name="tabs">
            <listitems>
              <listitem name="resources" value="false" group="linkright" />
              <listitem name="outline" value="false" group="sidebar" />
              <listitem name="glossary" value="false" group="sidebar" />
              <listitem name="transcript" value="false" group="sidebar" />
            </listitems>
          </optionlist>
          <option name="time_enabled" value="false">
            <options>
              <option name="time_format" value="elapsed" />
            </options>
          </option>
          <option name="video_enabled" value="false">
            <options>
              <option name="height" value="135" />
            </options>
          </option>
          <option name="logoAltText" />
        </options>
      </optiongroup>
      <optiongroup name="menuoptions">
        <options>
          <option name="flow" value="free" />
          <option name="wraplistitems" value="false" />
          <option name="tooltips" value="true" />
          <option name="autocollapse" value="true" />
          <option name="autonumber" value="false" />
          <option name="enableresultsicons" value="true" />
        </options>
      </optiongroup>
      <optiongroup name="controls">
        <options>
          <option name="volume" value="true" />
          <option name="seekbar" value="true">
            <options>
              <option name="readonly" value="false" />
              <option name="readonlyOnce" value="false" />
            </options>
          </option>
          <option name="pauseplay" value="true" />
          <option name="replay" value="true" />
          <option name="search" value="false" />
          <option name="closed_captions" value="true">
            <options>
              <option name="font" value="Neo Sans Std" />
            </options>
          </option>
          <option name="settings" value="true" />
          <option name="fullScreenToggle" value="false" />
          <option name="playbackSpeedControl" value="false" />
        </options>
      </optiongroup>
      <optiongroup name="bottombaroptions">
        <options>
          <option name="bottombar_enabled" value="true" />
        </options>
      </optiongroup>
      <optiongroup name="buttonoptions">
        <options>
          <option name="next" value="icon" />
          <option name="previous" value="icon" />
          <option name="submit" value="icon" />
        </options>
      </optiongroup>
    </optiongroups>
  </control_options>
  <control_layouts>
    <control_layout name="npnxnanbsnfns10110000101">
      <control name="volume" enabled="true" />
      <control name="seekbar" enabled="true" />
      <control name="replay" enabled="true" />
      <control name="pauseplay" enabled="true" />
      <control name="logo" enabled="false" />
      <control name="previous" enabled="false" />
      <control name="next" enabled="false" />
      <control name="swipeleft" enabled="false" />
      <control name="swiperight" enabled="false" />
      <control name="submit" enabled="true" />
      <control name="finish" enabled="false" />
      <control name="submitall" enabled="false" />
      <control name="resources" enabled="false" />
      <control name="glossary" enabled="false" />
      <control name="transcript" enabled="false" />
      <control name="outline" enabled="false">
        <controls>
          <control name="search" enabled="false" />
        </controls>
      </control>
      <control name="close_btn" enabled="true" />
    </control_layout>
    <control_layout name="pxabnsnfns10110000101">
      <control name="volume" enabled="true" />
      <control name="seekbar" enabled="true" />
      <control name="replay" enabled="true" />
      <control name="pauseplay" enabled="true" />
      <control name="logo" enabled="false" />
      <control name="previous" enabled="true" />
      <control name="next" enabled="true" />
      <control name="swipeleft" enabled="true" />
      <control name="swiperight" enabled="true" />
      <control name="submit" enabled="false" />
      <control name="finish" enabled="false" />
      <control name="submitall" enabled="false" />
      <control name="resources" enabled="false" />
      <control name="glossary" enabled="false" />
      <control name="transcript" enabled="false" />
      <control name="outline" enabled="false">
        <controls>
          <control name="search" enabled="false" />
        </controls>
      </control>
      <control name="close_btn" enabled="true" />
    </control_layout>
    <control_layout name="npxnabnsnfns10110000101">
      <control name="volume" enabled="true" />
      <control name="seekbar" enabled="true" />
      <control name="replay" enabled="true" />
      <control name="pauseplay" enabled="true" />
      <control name="logo" enabled="false" />
      <control name="previous" enabled="false" />
      <control name="next" enabled="true" />
      <control name="swipeleft" enabled="false" />
      <control name="swiperight" enabled="true" />
      <control name="submit" enabled="false" />
      <control name="finish" enabled="false" />
      <control name="submitall" enabled="false" />
      <control name="resources" enabled="false" />
      <control name="glossary" enabled="false" />
      <control name="transcript" enabled="false" />
      <control name="outline" enabled="false">
        <controls>
          <control name="search" enabled="false" />
        </controls>
      </control>
      <control name="close_btn" enabled="true" />
    </control_layout>
    <control_layout name="npnxnanbnsnfns10110000101">
      <control name="volume" enabled="true" />
      <control name="seekbar" enabled="true" />
      <control name="replay" enabled="true" />
      <control name="pauseplay" enabled="true" />
      <control name="logo" enabled="false" />
      <control name="previous" enabled="false" />
      <control name="next" enabled="false" />
      <control name="swipeleft" enabled="false" />
      <control name="swiperight" enabled="false" />
      <control name="submit" enabled="false" />
      <control name="finish" enabled="false" />
      <control name="submitall" enabled="false" />
      <control name="resources" enabled="false" />
      <control name="glossary" enabled="false" />
      <control name="transcript" enabled="false" />
      <control name="outline" enabled="false">
        <controls>
          <control name="search" enabled="false" />
        </controls>
      </control>
      <control name="close_btn" enabled="true" />
    </control_layout>
    <control_layout name="pxabnsnfns10110000100">
      <control name="volume" enabled="true" />
      <control name="seekbar" enabled="true" />
      <control name="replay" enabled="true" />
      <control name="pauseplay" enabled="true" />
      <control name="logo" enabled="false" />
      <control name="previous" enabled="true" />
      <control name="next" enabled="true" />
      <control name="swipeleft" enabled="true" />
      <control name="swiperight" enabled="true" />
      <control name="submit" enabled="false" />
      <control name="finish" enabled="false" />
      <control name="submitall" enabled="false" />
      <control name="resources" enabled="false" />
      <control name="glossary" enabled="false" />
      <control name="transcript" enabled="false" />
      <control name="outline" enabled="false">
        <controls>
          <control name="search" enabled="false" />
        </controls>
      </control>
      <control name="close_btn" enabled="false" />
    </control_layout>
  </control_layouts>
  <colorschemes>
    <colorscheme name="npnxnanbnsnfns10110000101">
      <color_group name="accessibility">
        <group_colors>
          <color name="focus_rect">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFF00" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="infopanel">
        <group_colors>
          <color name="link_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x7C2280" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="link_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0000EE" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="name_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x444444" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="title_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x999999" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="infopanelpopup">
        <group_colors>
          <color name="link_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="link_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6A98C6" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x374D64" alpha="100" stop="0" />
                <color rgb="0x142D46" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="name_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6A98C6" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bio_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="timer">
        <group_colors>
          <color name="duration">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x00D7FF" alpha="100" stop="0" />
                <color rgb="0x00ABFF" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="elapsed">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFEE347" alpha="100" stop="0" />
                <color rgb="0xFCCC2A" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="stroke">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xC7C7C7" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="inner">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="window">
        <group_colors>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="inner">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x374D64" alpha="100" stop="0" />
                <color rgb="0x142D46" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x5776A2" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="diva">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x496484" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divb">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x1F3752" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x000000" alpha="20" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="editor">
        <group_colors>
          <color name="slide_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="html_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_text_shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x4A6C98" alpha="100" stop="0" />
                <color rgb="0x243C5C" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="btn_down">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x294260" alpha="100" stop="0" />
                <color rgb="0x426288" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="btn_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x436388" alpha="100" stop="0" />
                <color rgb="0x294260" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="listitem">
        <group_colors>
          <color name="selected_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="selected">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x5379A5" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="viewed">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6CA5DE" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x3F5A76" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="lines">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2F465C" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="glossary">
        <group_colors>
          <color name="selected_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="selected">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x5379A5" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x3F5A76" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="heading_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="diva">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2D4258" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divb">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2D4258" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divc">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x5479A2" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x4D6E9A" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="resource">
        <group_colors>
          <color name="separator">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2A3C50" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="outline">
        <group_colors>
          <color name="search_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6A98C6" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="search_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divc">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x31475E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divb">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x5479A2" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="diva">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x31475E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="transcript">
        <group_colors>
          <color name="heading_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="div">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x283C4E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="tabs">
        <group_colors>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6596C9" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="selected">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6A98C6" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x47658B" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="seekcontrol">
        <group_colors>
          <color name="btn_icon_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x99B2CC" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_icon_shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2D4765" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_icon_color">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="seek_position">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="seek_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x476A95" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="seek_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x14304D" alpha="100" stop="0" />
                <color rgb="0x4C6B96" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x436388" alpha="100" stop="0" />
                <color rgb="0x2A4360" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="searchinput">
        <group_colors>
          <color name="text_active">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2D2D2D" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg_active">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="search_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2D2D2D" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="hover_glow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x3C5979" alpha="0" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="button_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x294260" alpha="100" stop="0" />
                <color rgb="0x426288" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="icon">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x405E82" alpha="100" stop="0" />
                <color rgb="0x0E2A45" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="scrollarea">
        <group_colors>
          <color name="detailb">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="detaila">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x7698B4" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="icons">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="button_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="button_inner">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x587BA1" alpha="100" stop="0" />
                <color rgb="0x496184" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="button_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x486B94" alpha="100" stop="0" />
                <color rgb="0x3A5579" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x527CAD" alpha="100" stop="0" />
                <color rgb="0x527CAD" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="0">
              <colors>
                <color rgb="0x405E81" alpha="100" stop="0" />
                <color rgb="0x46678C" alpha="100" stop="128" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="menu">
        <group_colors>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x1A324A" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x47658B" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="logo">
        <group_colors>
          <color name="shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="checkbox">
        <group_colors>
          <color name="hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x6A98C6" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="check">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x2BC400" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x31475E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="button">
        <group_colors>
          <color name="btn_icon_color">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_text_shadow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A45" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="btn_hover">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x4A6C98" alpha="100" stop="0" />
                <color rgb="0x243C5C" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="btn_down">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x294260" alpha="100" stop="0" />
                <color rgb="0x426288" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="btn_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x436388" alpha="100" stop="0" />
                <color rgb="0x294260" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="btn_glow">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x253C52" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="base">
        <group_colors>
          <color name="text">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0xFFFFFF" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="diva">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x1F3752" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="divb">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x496484" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="menu_shade">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x34495E" alpha="100" stop="0" />
                <color rgb="0x142B42" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x47617F" alpha="100" stop="0" />
                <color rgb="0x47617F" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x374D64" alpha="100" stop="0" />
                <color rgb="0x142D46" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
          <color name="slide_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x374D64" alpha="100" stop="0" />
                <color rgb="0x142D46" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
      <color_group name="volume">
        <group_colors>
          <color name="thumb_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x0E2A46" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="thumb_inner">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x57799E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="thumb_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x46678E" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="slider_border">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x4D74A3" alpha="100" stop="0" />
              </colors>
            </fill>
          </color>
          <color name="slider_bg">
            <fill type="linear" rotation="90">
              <colors>
                <color rgb="0x314B68" alpha="100" stop="0" />
                <color rgb="0x365374" alpha="100" stop="128" />
                <color rgb="0x314B68" alpha="100" stop="255" />
              </colors>
            </fill>
          </color>
        </group_colors>
      </color_group>
    </colorscheme>
  </colorschemes>
  <string_tables>
    <string_table name="npnxnanbnsnfns10110000101">
      <string id="three_image_progress_audio">progresso</string>
      <string id="three_image_audio">áudio</string>
      <string id="three_image_camera_down">câmara moveu-se para baixo</string>
      <string id="three_image_camera_left">câmara moveu-se para a esquerda</string>
      <string id="three_image_camera_right">câmara moveu-se para a direita</string>
      <string id="three_image_camera_up">câmara moveu-se para cima</string>
      <string id="three_image_interaction_free">modo de exploração livre</string>
      <string id="three_image_interaction_guided">modo visita guiada</string>
      <string id="three_image_hotspot">hotspot</string>
      <string id="three_image_instructions">Utilize as teclas W, A, S, e D para se movimentar na imagem de 360 graus. Prima a tecla Tab para saltar para os marcadores e hotspots interativos.</string>
      <string id="three_image_interaction">interação de imagem de 360 graus</string>
      <string id="three_image_label">etiqueta</string>
      <string id="three_image_marker">marcador</string>
      <string id="three_image_items">artigos</string>
      <string id="three_image_interaction_next">seguinte</string>
      <string id="three_image_pause_audio">parar áudio</string>
      <string id="three_image_pause_video">parar vídeo</string>
      <string id="three_image_play_audio">ouvir áudio</string>
      <string id="three_image_play_video">reproduzir vídeo</string>
      <string id="three_image_interaction_prev">anterior</string>
      <string id="three_image_visited">visitado</string>
      <string id="three_image_of">de</string>
      <string id="three_image_count_of_total">%count% de %total%</string>
      <string id="acc_three_image_step_count_of_total">passo %count% de %total%</string>
      <string id="three_image_item">artigo</string>
      <string id="three_image_interaction_step">passo</string>
      <string id="three_image_tooltip">tooltip</string>
      <string id="three_image_total_items">%total% elementos</string>
      <string id="three_image_total_item">%total% elemento</string>
      <string id="three_image_progress_video">progresso</string>
      <string id="three_image_video">vídeo</string>
      <string id="three_image_video_volume">volume do vídeo</string>
      <string id="acc_three_image_count_of_total_items_visited">%count% de %total% elementos visitados</string>
      <string id="acc_three_image_count_of_total_item_visited">%count% de %total% elemento visitado</string>
      <string id="three_image_count_of_total_visited">%count% de %total% visitados</string>
      <string id="accessible_text">Texto acessível</string>
      <string id="acc_enter_fullscreen">Entrar no modo ecrã inteiro</string>
      <string id="acc_exit_fullscreen">Sair do modo ecrã inteiro</string>
      <string id="keyboardshortcuts_lower">Atalhos do teclado</string>
      <string id="zoom_to_fit">Zoom para ajustar</string>
      <string id="modern_video_settings_close">fechar definições de vídeo</string>
      <string id="acc_modern_video_fullscreen">vídeo em ecrã inteiro</string>
      <string id="modern_video_playback_settings">Definições de Reprodução do Vídeo</string>
      <string id="modern_video_captions_off">Desativadas</string>
      <string id="modern_video_captions_on">Ativadas</string>
      <string id="modern_video_captions_label">Legendas</string>
      <string id="acc_modern_video_settings">definições do vídeo</string>
      <string id="acc_modern_video_mute">silenciar vídeo</string>
      <string id="modern_video_normal_speed">Normal</string>
      <string id="acc_modern_video_pause">parar vídeo</string>
      <string id="acc_modern_video_pip">imagem em imagem</string>
      <string id="acc_modern_video_play">reproduzir vídeo</string>
      <string id="modern_video_playback_speed_title">Velocidade de Reprodução</string>
      <string id="acc_modern_video_captions">legendas do vídeo</string>
      <string id="acc_modern_video_playback_speed">velocidade de reprodução</string>
      <string id="acc_modern_video_volume">volume do vídeo</string>
      <string id="acc_active">ativo</string>
      <string id="alt_key">Alt</string>
      <string id="check_include">Verificar para incluir</string>
      <string id="clear">Limpar e voltar ao Menu</string>
      <string id="close">Fechar</string>
      <string id="acc_closed_captions">Legendas ocultas. Pressione a barra de espaço para ativar e desativar as legendas.</string>
      <string id="closed_captions_tip">Botão de Legendas</string>
      <string id="continue">Avançar</string>
      <string id="continueresponsive">Avançar</string>
      <string id="playback_speed_title">Velocidade de Reprodução</string>
      <string id="playback_speed_normal">Normal</string>
      <string id="acc_playback_speed">velocidade de reprodução</string>
      <string id="ctrl_key">Ctrl</string>
      <string id="enable_keyboardshortcuts">Ativar atalhos do teclado</string>
      <string id="enter_key">Enter</string>
      <string id="exit">Sair</string>
      <string id="filter">Filtrar</string>
      <string id="finish">TERMINAR</string>
      <string id="acc_finish">terminar</string>
      <string id="definition">Definição</string>
      <string id="acc_definition">definição</string>
      <string id="glossary">Glossário</string>
      <string id="terms">Termos</string>
      <string id="progress_hour">hora</string>
      <string id="progress_hours">horas</string>
      <string id="action">Ação</string>
      <string id="keyboardshortcuts">Atalhos do teclado</string>
      <string id="shortcuthelp">Lista dos atalhos de teclado</string>
      <string id="mute_shortcut">Desativar/Ativar o Som</string>
      <string id="next_shortcut">Slide Seguinte</string>
      <string id="playpause_shortcut">Reproduzir/ Pausa</string>
      <string id="previous_shortcut">Slide Anterior</string>
      <string id="replay_shortcut">Reproduzir Novamente Slide</string>
      <string id="shortcut">Atalho</string>
      <string id="submit_shortcut">Enviar Slide</string>
      <string id="acctext_shortcut">Alternar texto acessível</string>
      <string id="closedcaptions_shortcut">Alternar legendas ocultas</string>
      <string id="fullscreen_shortcut">Alternar o modo ecrã inteiro</string>
      <string id="togglezoom">Aproxime ou afaste para ajustar</string>
      <string id="outline">Menu</string>
      <string id="disabled_orientation">Rode o seu dispositivo</string>
      <string id="progress_minute">minuto</string>
      <string id="progress_minutes">minutos</string>
      <string id="mute_tip">Silenciar</string>
      <string id="next">PRÓXIMO</string>
      <string id="acc_next">próximo</string>
      <string id="next_slide_tip">Slide Seguinte</string>
      <string id="transcript_chk">Notas</string>
      <string id="transcript">Notas</string>
      <string id="bio">Bio</string>
      <string id="more_info">Mais info</string>
      <string id="closed_captions">Legendas</string>
      <string id="acc_pause">pausa</string>
      <string id="pause_tip">Pausa</string>
      <string id="acc_play">reprodução</string>
      <string id="play_tip">Reproduzir</string>
      <string id="send_an_email">Enviar um e-mail</string>
      <string id="prev">ANTERIOR</string>
      <string id="acc_previous">anterior</string>
      <string id="previous_slide_tip">Slide Anterior</string>
      <string id="question_list">Lista de perguntas</string>
      <string id="replay_tip">Reproduzir Novamente</string>
      <string id="acc_replay">voltar a reproduzir</string>
      <string id="resources">Recursos</string>
      <string id="acc_resources">recursos</string>
      <string id="restart">Reiniciar</string>
      <string id="resume">Retomar</string>
      <string id="acc_locked">bloqueado</string>
      <string id="acc_search_input">pesquisar</string>
      <string id="search_toggle">Pesquisar</string>
      <string id="sidebar_toggle">Ativar/desativar a barra lateral</string>
      <string id="acc_slide_progress">deslizar o progresso</string>
      <string id="desktop_start">Iniciar o curso</string>
      <string id="mobile_start">Iniciar o curso</string>
      <string id="acc_visited">visitado</string>
      <string id="search">Pesquisar...</string>
      <string id="search_in">Pesquisar em:</string>
      <string id="search_results">Pesquisar resultados</string>
      <string id="progress_second">segundo</string>
      <string id="progress_seconds">segundos</string>
      <string id="settings_tip">Configurações</string>
      <string id="shift_key">Shift</string>
      <string id="acc_skipnavigation">Volte ao início</string>
      <string id="slide_text_chk">Texto do diapositivo</string>
      <string id="spacebar_key">Space</string>
      <string id="streaming_video_local_playback_learn_more">Saber mais</string>
      <string id="streaming_video_local_playback_republish">Para ver este vídeo, utilize o recurso de pré-visualização do Storyline 360, carregue o seu material publicado para um servidor, publique-o no Review 360 ou mude para a qualidade de vídeo estática.</string>
      <string id="streaming_video_local_playback_title">O streaming de vídeo não está disponível para reprodução local.</string>
      <string id="submitall">SUBMETER TUDO</string>
      <string id="acc_submitall">submeter tudo</string>
      <string id="submit">SUBMETER</string>
      <string id="acc_submit">submeter</string>
      <string id="submit_tip">Enviar</string>
      <string id="unmute_tip">Ativar o Som</string>
      <string id="acc_volume">volume</string>
    </string_table>
  </string_tables>
  <fonts>
    <font name="Neo Sans Std">
      <files>
        <file bold="true" italic="false" />
        <file bold="false" italic="false" />
      </files>
    </font>
  </fonts>
  <sounds enabled="true" />
  <nav_data>
    <outline>
      <links>
        <slidelink slideid="_player.5fgE044eYLG" displaytext="Principal" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.5fgE044eYLG.6eewkVfVFzK" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fgE044eYLG.6p2DqYiZw9e" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fgE044eYLG.6HzQkIlPlrg" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fgE044eYLG.61ON0NZILus" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fgE044eYLG.6cac0NAnUiv" displaytext="Menu" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.6it7SyhOGe6" displaytext="1 Uni-1" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.6it7SyhOGe6.6Deilkvfk70" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.6PzmWn9pGMl" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.5VyrDxPA8Wk" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.5xcQnDX9XVq" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.5qnphyCTVzS" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.6CqHsC5Omot" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.5wmreSVK0A2" displaytext="Pick Many" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.6Ttmo8ZnUip" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6it7SyhOGe6.6VC7mXIBzOM" displaytext="Untitled Slide" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.5ZcIVIKkL4Z" displaytext="2Uni-2" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.5ZcIVIKkL4Z.67g2eLRrCYF" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6YjPwILVRNj" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6CMAtXXCLMj" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.66NJgRIDD6K" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6UTSUk0McK3" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.62MlCrBVjAc" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.63EypKTdQuR" displaytext="Drag and Drop" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6ZL71VFFuhQ" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6fCJmJijvW9" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5ZcIVIKkL4Z.6piZSqe604c" displaytext="Untitled Slide" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.6g0SbeuiqJC" displaytext="Uni-3" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.6g0SbeuiqJC.5so5i25vqCV" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6oMNsHa9E8N" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6oNdwa3gl0A" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6F962RVW8b7" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6SP3NNGeewp" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6BHMzN4Mw0b" displaytext="Pick Many" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6mo6fSES19V" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6g0SbeuiqJC.6LyGDUwZdq6" displaytext="Untitled Slide" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.5nwuYNGyY32" displaytext="Uni-4" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.5nwuYNGyY32.5sBxYHDfOUz" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.5lBWQZwqXbV" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.5Vv5lawfNcC" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.6frnJkas2om" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.6AIGqUII1AK" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.6RsoXZFAVSI" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.6SglX4p2jrA" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.5h5aGKVJ45J" displaytext="Drag and Drop" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.5sRRT0LRbLn" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5nwuYNGyY32.601JCXi0FLt" displaytext="Untitled Slide" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.5fM06LFOnTX" displaytext="Uni-5" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.5fM06LFOnTX.6oF0YGwgvIK" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fM06LFOnTX.6gmrfhmD7BS" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.5fM06LFOnTX.6IzOOns3XIW" displaytext="Untitled Slide" expand="false" type="slide" />
          </links>
        </slidelink>
        <slidelink slideid="_player.6rTmS2JpDep" displaytext="Uni-6" expand="true" type="slide">
          <links>
            <slidelink slideid="_player.6rTmS2JpDep.5fz6EXknKQT" displaytext="Untitled Slide" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.62RsXEvxSY0" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.60Vwgttsdkk" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.6UcTFlAC2f2" displaytext="Drag and Drop" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.62gb5ZTFShk" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.5VCNr5FHuIG" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.5hsIIThNTsT" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.5VSy6pPfBbg" displaytext="Pick Many" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.6CBjEnfAYnA" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.5owJEM2xLBR" displaytext="Pick One" expand="false" type="slide" />
            <slidelink slideid="_player.6rTmS2JpDep.6aV3LVrn62Y" displaytext="Pick One" expand="false" type="slide" />
          </links>
        </slidelink>
      </links>
    </outline>
  </nav_data>
  <resource_data description="" />
  <transcript_data />
  <glossary_data />
</bwFrame>