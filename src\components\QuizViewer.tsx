import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Quiz, Question, StudentProgress, setStoredData, getStoredData } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';
import { ChevronLeft, CheckCircle, X, ArrowRight } from 'lucide-react';

interface QuizViewerProps {
  quiz: Quiz;
  courseId: string;
  onBack: () => void;
  onQuizComplete: (score: number) => void;
}

const QuizViewer: React.FC<QuizViewerProps> = ({
  quiz,
  courseId,
  onBack,
  onQuizComplete
}) => {
  const { user } = useAuth();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: number }>({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const totalQuestions = quiz.questions.length;
  const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: answerIndex
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      finishQuiz();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const finishQuiz = () => {
    // Calculate score
    let correctAnswers = 0;
    quiz.questions.forEach((question, index) => {
      if (selectedAnswers[index] === question.correctAnswer) {
        correctAnswers++;
      }
    });

    const finalScore = Math.round((correctAnswers / totalQuestions) * 100);
    setScore(finalScore);
    setShowResults(true);

    // Save score to localStorage
    if (user) {
      const progress = getStoredData<StudentProgress[]>('ecurso_progress', []);
      const userProgressIndex = progress.findIndex(p => 
        p.studentId === user.id && p.courseId === courseId
      );
      
      if (userProgressIndex >= 0) {
        progress[userProgressIndex].quizScores[quiz.id] = finalScore;
        setStoredData('ecurso_progress', progress);
      }
    }

    onQuizComplete(finalScore);
  };

  if (showResults) {
    return (
      <div className="min-h-screen bg-background">
        <div className="border-b bg-card">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" onClick={onBack}>
                <ChevronLeft className="w-4 h-4 mr-2" />
                Voltar ao Curso
              </Button>
            </div>
            <div className="text-center">
              <h1 className="text-3xl font-bold">Resultado do Quiz</h1>
              <p className="text-muted-foreground">{quiz.title}</p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card className="shadow-card text-center">
              <CardContent className="p-8">
                <div className="mb-6">
                  <div className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    score >= 70 ? 'bg-success/20 text-success' : 'bg-destructive/20 text-destructive'
                  }`}>
                    {score >= 70 ? (
                      <CheckCircle className="w-12 h-12" />
                    ) : (
                      <X className="w-12 h-12" />
                    )}
                  </div>
                  
                  <h2 className="text-4xl font-bold mb-2">{score}%</h2>
                  <p className="text-xl text-muted-foreground mb-4">
                    {score >= 70 ? 'Parabéns! Você passou!' : 'Você precisa estudar mais'}
                  </p>
                  
                  <Badge 
                    className={score >= 70 
                      ? 'bg-success/10 text-success border-success/20' 
                      : 'bg-destructive/10 text-destructive border-destructive/20'
                    }
                  >
                    {quiz.questions.filter((_, index) => selectedAnswers[index] === quiz.questions[index].correctAnswer).length} 
                    /{totalQuestions} questões corretas
                  </Badge>
                </div>

                <div className="space-y-4">
                  <div className="text-left">
                    <h3 className="font-semibold mb-4">Revisão das Respostas:</h3>
                    <div className="space-y-3">
                      {quiz.questions.map((question, index) => {
                        const isCorrect = selectedAnswers[index] === question.correctAnswer;
                        return (
                          <div key={question.id} className="p-3 bg-muted/20 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              {isCorrect ? (
                                <CheckCircle className="w-4 h-4 text-success" />
                              ) : (
                                <X className="w-4 h-4 text-destructive" />
                              )}
                              <span className="font-medium text-sm">Questão {index + 1}</span>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">{question.text}</p>
                            <div className="text-xs">
                              <p className={isCorrect ? 'text-success' : 'text-destructive'}>
                                Sua resposta: {question.options[selectedAnswers[index]] || 'Não respondida'}
                              </p>
                              {!isCorrect && (
                                <p className="text-success">
                                  Resposta correta: {question.options[question.correctAnswer]}
                                </p>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <Button 
                    onClick={onBack}
                    className="w-full bg-gradient-primary hover:shadow-primary"
                  >
                    Continuar Estudando
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" onClick={onBack}>
              <ChevronLeft className="w-4 h-4 mr-2" />
              Voltar ao Curso
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">{quiz.title}</h1>
              <p className="text-muted-foreground">
                Questão {currentQuestionIndex + 1} de {totalQuestions}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Progresso</p>
              <p className="text-2xl font-bold">{Math.round(progress)}%</p>
            </div>
          </div>
          <div className="mt-4">
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="text-xl">{currentQuestion.text}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {currentQuestion.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(index)}
                    className={`w-full p-4 text-left rounded-lg border-2 transition-all ${
                      selectedAnswers[currentQuestionIndex] === index
                        ? 'border-primary bg-primary/10 text-primary'
                        : 'border-muted hover:border-primary/50 hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        selectedAnswers[currentQuestionIndex] === index
                          ? 'border-primary bg-primary text-primary-foreground'
                          : 'border-muted-foreground'
                      }`}>
                        {selectedAnswers[currentQuestionIndex] === index && (
                          <CheckCircle className="w-4 h-4" />
                        )}
                      </div>
                      <span>{option}</span>
                    </div>
                  </button>
                ))}
              </div>

              <div className="flex items-center justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentQuestionIndex === 0}
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Anterior
                </Button>

                <Button
                  onClick={handleNext}
                  disabled={selectedAnswers[currentQuestionIndex] === undefined}
                  className="bg-gradient-primary hover:shadow-primary"
                >
                  {currentQuestionIndex === totalQuestions - 1 ? 'Finalizar' : 'Próxima'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default QuizViewer;