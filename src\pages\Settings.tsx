import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings as SettingsIcon, 
  User, 
  Bell, 
  Shield, 
  Database,
  ArrowLeft,
  Save,
  Download,
  Upload,
  Trash2,
  AlertTriangle
} from 'lucide-react';

const Settings: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // Perfil
    name: user?.name || '',
    email: user?.email || '',
    
    // Notificações
    emailNotifications: true,
    pushNotifications: false,
    courseUpdates: true,
    progressReports: true,
    
    // Privacidade
    profileVisible: true,
    progressVisible: false,
    
    // Sistema
    autoSave: true,
    darkMode: false,
    language: 'pt-PT'
  });

  const handleBack = () => {
    navigate('/dashboard');
  };

  const handleSave = async () => {
    setLoading(true);
    
    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Aqui salvaria as configurações no localStorage ou API
      localStorage.setItem('ecurso_settings', JSON.stringify(settings));
      
      toast({
        title: "Configurações salvas!",
        description: "Suas preferências foram atualizadas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Ocorreu um erro ao salvar as configurações.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    try {
      const data = {
        courses: localStorage.getItem('ecurso_courses'),
        students: localStorage.getItem('ecurso_students'),
        progress: localStorage.getItem('ecurso_progress'),
        settings: localStorage.getItem('ecurso_settings'),
        exportDate: new Date().toISOString()
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ecurso-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Dados exportados!",
        description: "O backup foi baixado com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro na exportação",
        description: "Não foi possível exportar os dados.",
        variant: "destructive",
      });
    }
  };

  const handleClearData = () => {
    if (window.confirm('Tem certeza que deseja limpar todos os dados? Esta ação não pode ser desfeita.')) {
      localStorage.removeItem('ecurso_courses');
      localStorage.removeItem('ecurso_students');
      localStorage.removeItem('ecurso_progress');
      localStorage.removeItem('ecurso_settings');
      
      toast({
        title: "Dados limpos!",
        description: "Todos os dados foram removidos do navegador.",
      });
      
      // Recarregar a página para reinicializar
      window.location.reload();
    }
  };

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <>
      {/* Page Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="hover-lift focus-ring"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                <SettingsIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">Configurações</h1>
                <p className="text-muted-foreground">Gerencie suas preferências e dados</p>
              </div>
            </div>

            <Button 
              onClick={handleSave}
              disabled={loading}
              className="btn-primary-enhanced focus-ring"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 loading-spinner mr-2" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Salvar
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container-responsive py-8">
        <Tabs defaultValue="profile" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-4 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="profile" className="transition-all duration-300 hover-lift focus-ring">
              <User className="w-4 h-4 mr-2" />
              Perfil
            </TabsTrigger>
            <TabsTrigger value="notifications" className="transition-all duration-300 hover-lift focus-ring">
              <Bell className="w-4 h-4 mr-2" />
              Notificações
            </TabsTrigger>
            <TabsTrigger value="privacy" className="transition-all duration-300 hover-lift focus-ring">
              <Shield className="w-4 h-4 mr-2" />
              Privacidade
            </TabsTrigger>
            <TabsTrigger value="data" className="transition-all duration-300 hover-lift focus-ring">
              <Database className="w-4 h-4 mr-2" />
              Dados
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6 animate-slide-up">
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Informações do Perfil</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input
                      id="name"
                      value={settings.name}
                      onChange={(e) => updateSetting('name', e.target.value)}
                      className="focus-ring"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.email}
                      onChange={(e) => updateSetting('email', e.target.value)}
                      className="focus-ring"
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Preferências do Sistema</h3>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Salvamento Automático</Label>
                      <p className="text-sm text-muted-foreground">
                        Salvar progresso automaticamente durante o uso
                      </p>
                    </div>
                    <Switch
                      checked={settings.autoSave}
                      onCheckedChange={(checked) => updateSetting('autoSave', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Modo Escuro</Label>
                      <p className="text-sm text-muted-foreground">
                        Usar tema escuro na interface
                      </p>
                    </div>
                    <Switch
                      checked={settings.darkMode}
                      onCheckedChange={(checked) => updateSetting('darkMode', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6 animate-slide-up">
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Preferências de Notificação</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Notificações por Email</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber notificações importantes por email
                    </p>
                  </div>
                  <Switch
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => updateSetting('emailNotifications', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Notificações Push</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber notificações no navegador
                    </p>
                  </div>
                  <Switch
                    checked={settings.pushNotifications}
                    onCheckedChange={(checked) => updateSetting('pushNotifications', checked)}
                  />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Atualizações de Cursos</Label>
                    <p className="text-sm text-muted-foreground">
                      Notificar sobre novos cursos e atualizações
                    </p>
                  </div>
                  <Switch
                    checked={settings.courseUpdates}
                    onCheckedChange={(checked) => updateSetting('courseUpdates', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Relatórios de Progresso</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber relatórios semanais de progresso
                    </p>
                  </div>
                  <Switch
                    checked={settings.progressReports}
                    onCheckedChange={(checked) => updateSetting('progressReports', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="privacy" className="space-y-6 animate-slide-up">
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Configurações de Privacidade</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Perfil Visível</Label>
                    <p className="text-sm text-muted-foreground">
                      Permitir que outros usuários vejam seu perfil
                    </p>
                  </div>
                  <Switch
                    checked={settings.profileVisible}
                    onCheckedChange={(checked) => updateSetting('profileVisible', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Progresso Visível</Label>
                    <p className="text-sm text-muted-foreground">
                      Mostrar seu progresso para outros usuários
                    </p>
                  </div>
                  <Switch
                    checked={settings.progressVisible}
                    onCheckedChange={(checked) => updateSetting('progressVisible', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="data" className="space-y-6 animate-slide-up">
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle>Gestão de Dados</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Backup e Restauração</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Faça backup dos seus dados ou restaure de um backup anterior.
                    </p>
                    
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        onClick={handleExportData}
                        className="hover-lift focus-ring"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Exportar Dados
                      </Button>
                      <Button 
                        variant="outline" 
                        className="hover-lift focus-ring"
                        disabled
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Importar Dados
                      </Button>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-destructive">Zona de Perigo</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Ações irreversíveis que afetam permanentemente seus dados.
                    </p>
                    
                    <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <h4 className="font-medium text-destructive mb-1">
                            Limpar Todos os Dados
                          </h4>
                          <p className="text-sm text-destructive/80 mb-3">
                            Remove permanentemente todos os cursos, alunos, progresso e configurações 
                            armazenados no navegador. Esta ação não pode ser desfeita.
                          </p>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={handleClearData}
                            className="focus-ring"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Limpar Dados
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default Settings;
