<xs:schema targetNamespace="http://ltsc.ieee.org/xsd/LOM/extend"
           xmlns="http://ltsc.ieee.org/xsd/LOM/extend"
           xmlns:lom="http://ltsc.ieee.org/xsd/LOM"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified"
           version="IEEE LTSC LOM XML 1.0">

  <xs:annotation>
    <xs:documentation>
       This work is licensed under the Creative Commons Attribution-ShareAlike
       License.  To view a copy of this license, see the file license.txt,
       visit http://creativecommons.org/licenses/by-sa/1.0 or send a letter to
       Creative Commons, 559 Nathan Abbott Way, Stanford, California 94305, USA.
    </xs:documentation>

    <xs:documentation>
       This component schema defines the content model group customElements
       to support validation of custom metadata elements. This component XSD 
       should be used if extensions are to be supported in LOM instances.
    </xs:documentation>
    <xs:documentation>
        *****************************************************************************
        **                           CHANGE HISTORY                                **
        *****************************************************************************
        ** 03/15/2004:  1)Updated annoation describing purpose and design of the   **
        **                XSD.                                                     **
        *****************************************************************************
    </xs:documentation>
  </xs:annotation>

  <xs:import namespace="http://ltsc.ieee.org/xsd/LOM"/>

  <!-- Model group declarations -->

  <xs:group name="customElements">
    <xs:choice>
      <xs:group ref="lom:customElements"/>
    </xs:choice>
  </xs:group>

</xs:schema>
