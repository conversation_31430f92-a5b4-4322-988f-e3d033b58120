﻿window.globalProvideData('slide', '{"title":"Menu","trackViews":true,"showMenuResultIcon":false,"viewGroupId":"","historyGroupId":"","videoZoom":"","scrolling":false,"transition":"appear","transDuration":0,"transDir":1,"wipeTrans":false,"slideLock":false,"navIndex":-1,"globalAudioId":"","thumbnailid":"","slideNumberInScene":5,"includeInSlideCounts":true,"presenterRef":{"id":"none"},"slideLayers":[{"enableSeek":true,"enableReplay":true,"timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6UkaFNBUvnF"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6KtozBxTyMb"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6CF2Pc1odgA"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6C0IzSP1mMg"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6rmFzqecwVb"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6myliFNjLQ9"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"62YO7b2jrx3"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lNw3YFi0cx"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"61jub7Qy8OO"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5W3Xv2j6uLu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5hha4NXNQWL"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ctqN84HwS5"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fjXMMrmwFm"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5v2NzpQVpmK"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6KLFVJvRM9Q"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5httvvXoEIB"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5mwZuA4qmHt"}}]},{"kind":"ontimelinetick","time":1500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6cTmDU4NYDz"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fztF0NBXpJ"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5XYilhnKgm4"}}]},{"kind":"ontimelinetick","time":2750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6djpC6Eiieu"}}]}]},"objects":[{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":52,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":51,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":1}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":2}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":82,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fgE044eYLG.6cac0NAnUiv"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":60,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":59,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":3}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":4}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":86,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.5Xlh34Ekb5w"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":56,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":55,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":5}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":6}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":84,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":3,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6QtAxFNOoxm"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_6ctqN84HwS5","type":"richvartext","xPos":10,"yPos":5,"xAccOffset":0,"yAccOffset":0,"width":212,"height":30,"device":false,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Pontuação: ","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"%_player.nota%","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"/20","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":28,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":135,"yPos":672,"tabIndex":46,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":41,"altText":"Pontuação: %_player.nota%/20","pngfb":false,"pr":{"l":"Lib","i":7}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":40,"resume":true,"useHandCursor":true,"id":"6ctqN84HwS5"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PYUvVg1zLz_545423499","id":"01","linkId":"txt__default_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6dBYEI4LvKs_2042009458","id":"02","linkId":"txt__default_Disabled_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":12}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":47,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6fjXMMrmwFm","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":19,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":true,"useHandCursor":true,"id":"5v2NzpQVpmK"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":112,"yPos":8,"tabIndex":22,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":579,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1158,"bottom":40,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":14}},"html5data":{"xPos":-1,"yPos":-1,"width":1159,"height":41,"strokewidth":0}},"width":1158,"height":40,"resume":true,"useHandCursor":true,"id":"62YO7b2jrx3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5lNw3YFi0cx_608896479","id":"01","linkId":"txt__default_5lNw3YFi0cx","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":999,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"CURSO DE ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"foregroundColor":"#595959","linkColor":"#595959","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"INTELIGÊNCIA ARTIFICIAL PARA DESENVOLVIMENTO DE NEGÓCIOS","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"foregroundColor":"#5A5A5A","linkColor":"#5A5A5A","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"justification":"left","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":65,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":647,"bottom":32,"pngfb":false,"pr":{"l":"Lib","i":55}}}],"shapemaskId":"","xPos":128,"yPos":8,"tabIndex":23,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":509.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1019,"bottom":40,"altText":"CURSO DE INTELIGÊNCIA ARTIFICIAL PARA DESENVOLVIMENTO DE NEGÓCIOS","pngfb":false,"pr":{"l":"Lib","i":54}},"html5data":{"xPos":-1,"yPos":-1,"width":1020,"height":41,"strokewidth":0}},"width":1019,"height":40,"resume":true,"useHandCursor":true,"id":"5lNw3YFi0cx"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6KLFVJvRM9Q_2057410666","id":"01","linkId":"txt__default_6KLFVJvRM9Q","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1220,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"BEM-VINDO AO MÓDULO 1 DO ","style":{"fontSize":16,"fontIsBold":false,"displayCase":"asIs","foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}},{"text":"CURSO DE I","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}},{"text":"NTELIGÊNCIA ARTIFICIAL PARA DESENVOLVIMENTO DE NEGÓCIOS","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":90,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":1160,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":89}}}],"shapemaskId":"","xPos":20,"yPos":80,"tabIndex":24,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":620,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1240,"bottom":40,"altText":"BEM-VINDO AO MÓDULO 1 DO CURSO DE INTELIGÊNCIA ARTIFICIAL PARA DESENVOLVIMENTO DE NEGÓCIOS","pngfb":false,"pr":{"l":"Lib","i":88}},"html5data":{"xPos":0,"yPos":0,"width":1240,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1240,"height":40,"resume":true,"useHandCursor":true,"id":"6KLFVJvRM9Q"},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6dhN7zjL5Vx_1738441031","id":"01","linkId":"txt__default_5httvvXoEIB","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 1\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Introdução à inteligência Artificial (IA)\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":160,"pngfb":false,"pr":{"l":"Lib","i":94}}},{"kind":"textdata","uniqueId":"5ol1K8Xc6UK_42071671","id":"02","linkId":"txt__default_Disabled_5httvvXoEIB","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 1\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Introdução à inteligência Artificial (IA)\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":160,"pngfb":false,"pr":{"l":"Lib","i":95}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":25,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":90}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":92}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":93}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":92}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\nUnidade 1\\n-----------------------------------------\\nIntrodução à inteligência Artificial (IA)\\n\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5httvvXoEIB"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":27,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6AErpx6ESvr_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":26,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6AErpx6ESvr"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6AErpx6ESvr"}}],"clickdef":[{"kind":"objref","type":"string","value":"6AErpx6ESvr"}]}],"shapemaskId":"","xPos":216,"yPos":56,"tabIndex":62,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6AErpx6ESvr","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6AErpx6ESvr_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6AErpx6ESvr"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"6AErpx6ESvr_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"6AErpx6ESvr_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6AErpx6ESvr_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"5httvvXoEIB"}}],"clickdef":[{"kind":"objref","type":"string","value":"5httvvXoEIB"},{"kind":"objref","type":"string","value":"6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"},{"kind":"objref","type":"string","value":"6AErpx6ESvr.6AErpx6ESvr_expandinglabel"}]}],"shapemaskId":"","xPos":120,"yPos":168,"tabIndex":61,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":10,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"-441","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5httvvXoEIB","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"5httvvXoEIB.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6AErpx6ESvr"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6AErpx6ESvr"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6AErpx6ESvr.6AErpx6ESvr"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5httvvXoEIB"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.6it7SyhOGe6.6Deilkvfk70"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6bgpjGmuoGC_-891859181","id":"01","linkId":"txt__default_5mwZuA4qmHt","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 2\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Funcionamento da IA\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":20,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":134,"pngfb":false,"pr":{"l":"Lib","i":101}}},{"kind":"textdata","uniqueId":"68XCeCBPHmM_-677145979","id":"02","linkId":"txt__default_Disabled_5mwZuA4qmHt","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 2\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Funcionamento da IA\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":20,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":134,"pngfb":false,"pr":{"l":"Lib","i":102}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":28,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":98}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":99}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":99}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":93}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":99}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\n\\n\\n\\nUnidade 2\\n-----------------------------------------\\nFuncionamento da IA\\n\\n\\n\\n\\n","pngfb":false,"pr":{"l":"Lib","i":99}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5mwZuA4qmHt"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":30,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6CDGv6SG6lH_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":29,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6CDGv6SG6lH"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6CDGv6SG6lH"}}],"clickdef":[{"kind":"objref","type":"string","value":"6CDGv6SG6lH"}]}],"shapemaskId":"","xPos":216,"yPos":56,"tabIndex":64,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6CDGv6SG6lH","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6CDGv6SG6lH_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6CDGv6SG6lH"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"6CDGv6SG6lH_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"6CDGv6SG6lH_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6CDGv6SG6lH_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}}],"clickdef":[{"kind":"objref","type":"string","value":"5mwZuA4qmHt"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"},{"kind":"objref","type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH_expandinglabel"}]}],"shapemaskId":"","xPos":482,"yPos":168,"tabIndex":63,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":11,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"-803","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5mwZuA4qmHt","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6CDGv6SG6lH"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6CDGv6SG6lH"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6CDGv6SG6lH.6CDGv6SG6lH"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5mwZuA4qmHt"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5ZcIVIKkL4Z.67g2eLRrCYF"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6klpBq9MkFE_1205734212","id":"01","linkId":"txt__default_6cTmDU4NYDz","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 3\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aplicações de IA em Contexto corporativo","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":41,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":160,"pngfb":false,"pr":{"l":"Lib","i":103}}},{"kind":"textdata","uniqueId":"5ndBjOSMh3D_1387581903","id":"02","linkId":"txt__default_Disabled_6cTmDU4NYDz","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Unidade 3\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aplicações de IA em Contexto corporativo","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":41,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":160,"pngfb":false,"pr":{"l":"Lib","i":104}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":31,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":98}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":93}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"\\nUnidade 3\\n-----------------------------------------\\nAplicações de IA em Contexto corporativo\\n","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6cTmDU4NYDz"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":33,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5kKbY7f0URZ_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":32,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5kKbY7f0URZ"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5kKbY7f0URZ"}}],"clickdef":[{"kind":"objref","type":"string","value":"5kKbY7f0URZ"}]}],"shapemaskId":"","xPos":216,"yPos":56,"tabIndex":66,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5kKbY7f0URZ","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5kKbY7f0URZ_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5kKbY7f0URZ"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"5kKbY7f0URZ_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"5kKbY7f0URZ_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5kKbY7f0URZ_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}}],"clickdef":[{"kind":"objref","type":"string","value":"6cTmDU4NYDz"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"},{"kind":"objref","type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ_expandinglabel"}]}],"shapemaskId":"","xPos":844,"yPos":168,"tabIndex":65,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":12,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"-1165","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6cTmDU4NYDz","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5kKbY7f0URZ"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"5kKbY7f0URZ"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"5kKbY7f0URZ.5kKbY7f0URZ"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6cTmDU4NYDz"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.6g0SbeuiqJC.5so5i25vqCV"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5lZl39Af5Un_-1306765040","id":"01","linkId":"txt__default_6fztF0NBXpJ","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Unidade 4","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"Automatização e Personalização com IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"spacingBefore":9,"tagType":"P"},"runs":[{"idx":0,"len":78,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#215968","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":161,"pngfb":false,"pr":{"l":"Lib","i":105}}},{"kind":"textdata","uniqueId":"6rRjWoe2yvb_36234025","id":"02","linkId":"txt__default_Disabled_6fztF0NBXpJ","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Unidade 4\\n","style":{"fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":9,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------Automatização e Personalização com IA","style":{"fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":9,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":78,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#215968","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":161,"pngfb":false,"pr":{"l":"Lib","i":106}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":34,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":98}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":93}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 4\\n-----------------------------------------Automatização e Personalização com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6fztF0NBXpJ"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":36,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"63At4q79FKT_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":35,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"63At4q79FKT"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"63At4q79FKT"}}],"clickdef":[{"kind":"objref","type":"string","value":"63At4q79FKT"}]}],"shapemaskId":"","xPos":216,"yPos":48,"tabIndex":76,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"63At4q79FKT","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"63At4q79FKT_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"63At4q79FKT"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"63At4q79FKT_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"63At4q79FKT_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"63At4q79FKT_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}}],"clickdef":[{"kind":"objref","type":"string","value":"6fztF0NBXpJ"},{"kind":"objref","type":"string","value":"63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT"},{"kind":"objref","type":"string","value":"63At4q79FKT.63At4q79FKT_expandinglabel"}]}],"shapemaskId":"","xPos":120,"yPos":416,"tabIndex":75,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":13,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"-441","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6fztF0NBXpJ","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"63At4q79FKT"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"63At4q79FKT.63At4q79FKT"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"63At4q79FKT"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"63At4q79FKT.63At4q79FKT"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6fztF0NBXpJ"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5nwuYNGyY32.5sBxYHDfOUz"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5WMe9ILQsOb_-1564167590","id":"01","linkId":"txt__default_5XYilhnKgm4","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Unidade 5","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"Desenvolvimento de Estratégias com IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"justification":"center","spacingBefore":9,"tagType":"P"},"runs":[{"idx":0,"len":78,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#215968","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":161,"pngfb":false,"pr":{"l":"Lib","i":107}}},{"kind":"textdata","uniqueId":"5pj7aWYWhTu_-1607819547","id":"02","linkId":"txt__default_Disabled_5XYilhnKgm4","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Unidade 5","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsUnderline":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":10,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"-----------------------------------------\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":9,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":" Desenvolvimento de Estratégias com IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":38,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#215968","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":161,"pngfb":false,"pr":{"l":"Lib","i":108}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":37,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":98}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------\\n Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------\\n Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"Unidade 5\\n-----------------------------------------\\n Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":93}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------\\n Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":100}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"Unidade 5\\n-----------------------------------------\\n Desenvolvimento de Estratégias com IA","pngfb":false,"pr":{"l":"Lib","i":91}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5XYilhnKgm4"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":39,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6nk7XTKOgSK_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":38,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6nk7XTKOgSK"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6nk7XTKOgSK"}}],"clickdef":[{"kind":"objref","type":"string","value":"6nk7XTKOgSK"}]}],"shapemaskId":"","xPos":216,"yPos":48,"tabIndex":78,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6nk7XTKOgSK","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6nk7XTKOgSK_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6nk7XTKOgSK"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"6nk7XTKOgSK_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"6nk7XTKOgSK_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6nk7XTKOgSK_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"5XYilhnKgm4"}}],"clickdef":[{"kind":"objref","type":"string","value":"5XYilhnKgm4"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"},{"kind":"objref","type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK_expandinglabel"}]}],"shapemaskId":"","xPos":482,"yPos":416,"tabIndex":77,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":14,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"-803","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"5XYilhnKgm4","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6nk7XTKOgSK"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6nk7XTKOgSK"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6nk7XTKOgSK.6nk7XTKOgSK"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5XYilhnKgm4"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fM06LFOnTX.6oF0YGwgvIK"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6bnCj6SCTuN_-2140925921","id":"01","linkId":"txt__default_6djpC6Eiieu","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"TEST","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"E","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":" FINAL","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":219,"bottom":117,"pngfb":false,"pr":{"l":"Lib","i":113}}},{"kind":"textdata","uniqueId":"61b0DdpmBGM_-1266023987","id":"02","linkId":"txt__default_Disabled_6djpC6Eiieu","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"TESTE FINAL","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}},{"text":"\\r","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"(Para aceder ao teste final deverá obter um mínimo de 16 pontos e ver todas as unidades do curso)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":15,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":19.6,"descent":4.7,"leading":0,"underlinePosition":-1.02,"underlineThickness":0.98,"xHeight":11}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":109,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":311,"bottom":154,"pngfb":false,"pr":{"l":"Lib","i":114}}},{"kind":"textdata","uniqueId":"6WfWaYWYgbk_218848250","id":"04","linkId":"txt_Terminado_6djpC6Eiieu","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":296,"height":198,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"TESTE FINAL","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":12,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","spacingBefore":0,"listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":219,"bottom":117,"pngfb":false,"pr":{"l":"Lib","i":115}}}],"shapemaskId":"","xPos":0,"yPos":0,"tabIndex":40,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"TESTE FINAL","pngfb":false,"pr":{"l":"Lib","i":109}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"TESTE FINAL\\r(Para aceder ao teste final deverá obter um mínimo de 16 pontos e ver todas as unidades do curso)","pngfb":false,"pr":{"l":"Lib","i":110}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"TESTE FINAL","pngfb":false,"pr":{"l":"Lib","i":111}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"TESTE FINAL\\r(Para aceder ao teste final deverá obter um mínimo de 16 pontos e ver todas as unidades do curso)","pngfb":false,"pr":{"l":"Lib","i":110}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":316,"bottom":208,"altText":"\\nTESTE FINAL\\n","pngfb":false,"pr":{"l":"Lib","i":112}},"html5data":{"xPos":-1,"yPos":-1,"width":317,"height":209,"strokewidth":0}}},{"kind":"state","name":"Terminado_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"TESTE FINAL\\r(Para aceder ao teste final deverá obter um mínimo de 16 pontos e ver todas as unidades do curso)","pngfb":false,"pr":{"l":"Lib","i":110}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}},{"kind":"state","name":"Terminado_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":317,"bottom":209,"altText":"TESTE FINAL","pngfb":false,"pr":{"l":"Lib","i":111}},"html5data":{"xPos":-1,"yPos":-1,"width":318,"height":210,"strokewidth":0}}},{"kind":"state","name":"Terminado_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-2,"top":-2,"right":317,"bottom":209,"altText":"TESTE FINAL\\r(Para aceder ao teste final deverá obter um mínimo de 16 pontos e ver todas as unidades do curso)","pngfb":false,"pr":{"l":"Lib","i":110}},"html5data":{"xPos":-2,"yPos":-2,"width":319,"height":211,"strokewidth":1}}}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6djpC6Eiieu"},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":42,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":30,"bottom":29,"altText":"Text Box","pngfb":false,"pr":{"l":"Lib","i":96}},"html5data":{"xPos":0,"yPos":0,"width":30,"height":29,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6L2YdibHhjk_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":41,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":97}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"markerType":"pulse","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6L2YdibHhjk"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6L2YdibHhjk"}}],"clickdef":[{"kind":"objref","type":"string","value":"6L2YdibHhjk"}]}],"shapemaskId":"","xPos":232,"yPos":88,"tabIndex":80,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6L2YdibHhjk","actionGroups":{"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6L2YdibHhjk_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6L2YdibHhjk"}}]}},"events":[{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showtitle","objRef":{"type":"string","value":"6L2YdibHhjk_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidetitle","objRef":{"type":"string","value":"6L2YdibHhjk_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6L2YdibHhjk_expandinglabel"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"}]}]}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"_default_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover_Disabled"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"Terminado","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"Terminado_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Disabled"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]},{"kind":"state","name":"Terminado_Hover_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado_Hover_Disabled"},"objRef":{"type":"string","value":"6djpC6Eiieu"}}],"clickdef":[{"kind":"objref","type":"string","value":"6djpC6Eiieu"},{"kind":"objref","type":"string","value":"6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"},{"kind":"objref","type":"string","value":"6L2YdibHhjk.6L2YdibHhjk_expandinglabel"}]}],"shapemaskId":"","xPos":844,"yPos":416,"tabIndex":79,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":158,"rotateYPos":104,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":15,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"441","dy":"0"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":316,"height":208,"resume":true,"useHandCursor":true,"id":"6djpC6Eiieu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"ActGrpSetStatesFinal":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"6djpC6Eiieu.$OnStage","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6L2YdibHhjk"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"}}],"elseActions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6L2YdibHhjk"}},{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6L2YdibHhjk.6L2YdibHhjk"}}]}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6djpC6Eiieu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.6rTmS2JpDep.5fz6EXknKQT"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6YefmMfugyu_1095337071","id":"01","linkId":"txt__default_6UkaFNBUvnF","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"633VQzkFcz5_-1335726456","id":"02","linkId":"txt__default_Disabled_6UkaFNBUvnF","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":45,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":16,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6UkaFNBUvnF","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":50,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6KtozBxTyMb_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":49,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":56}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":57}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6KtozBxTyMb"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6KtozBxTyMb"}}],"clickdef":[{"kind":"objref","type":"string","value":"6KtozBxTyMb"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6KtozBxTyMb"}}],"clickdef":[{"kind":"objref","type":"string","value":"6KtozBxTyMb"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":81,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":17,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6KtozBxTyMb","variables":[{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6KtozBxTyMb_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6KtozBxTyMb"}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"6KtozBxTyMb_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"6KtozBxTyMb_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6KtozBxTyMb_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":54,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6CF2Pc1odgA_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":53,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":58}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":59}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6CF2Pc1odgA"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6CF2Pc1odgA"}}],"clickdef":[{"kind":"objref","type":"string","value":"6CF2Pc1odgA"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6CF2Pc1odgA"}}],"clickdef":[{"kind":"objref","type":"string","value":"6CF2Pc1odgA"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":83,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":18,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6CF2Pc1odgA","variables":[{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6CF2Pc1odgA_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6CF2Pc1odgA"}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"6CF2Pc1odgA_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"6CF2Pc1odgA_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6CF2Pc1odgA_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":58,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":true,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"6C0IzSP1mMg_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":57,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":60}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":61}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6C0IzSP1mMg"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6C0IzSP1mMg"}}],"clickdef":[{"kind":"objref","type":"string","value":"6C0IzSP1mMg"}]},{"kind":"state","name":"_default_Disabled","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Disabled"},"objRef":{"type":"string","value":"6C0IzSP1mMg"}}],"clickdef":[{"kind":"objref","type":"string","value":"6C0IzSP1mMg"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":85,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":19,"scrolling":true,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"6C0IzSP1mMg","variables":[{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6C0IzSP1mMg_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6C0IzSP1mMg"}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"6C0IzSP1mMg_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"6C0IzSP1mMg_expandinglabel"}}]},{"kind":"onrelease","actions":[{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"6C0IzSP1mMg_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6rmFzqecwVb_89692865","id":"01","linkId":"txt__default_6rmFzqecwVb","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":489,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"(Clique nas unidades para iniciar o curso)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":420,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":116}}}],"shapemaskId":"","xPos":376,"yPos":672,"tabIndex":43,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":254.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":20,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":509,"bottom":40,"altText":"(Clique nas unidades para iniciar o curso)","pngfb":false,"pr":{"l":"Lib","i":66}},"html5data":{"xPos":-1,"yPos":-1,"width":510,"height":41,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":509,"height":40,"resume":true,"useHandCursor":true,"id":"6rmFzqecwVb"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6myliFNjLQ9_1386032535","id":"01","linkId":"txt__default_6myliFNjLQ9","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":132,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"03/03","style":{"fontSize":14,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":5,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":62,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":117}}}],"shapemaskId":"","xPos":1072,"yPos":672,"tabIndex":48,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":76,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":21,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":152,"bottom":33,"altText":"03/03","pngfb":false,"pr":{"l":"Lib","i":70}},"html5data":{"xPos":0,"yPos":0,"width":152,"height":33,"strokewidth":0}},"width":152,"height":33,"resume":true,"useHandCursor":true,"id":"6myliFNjLQ9"}],"startTime":-1,"elapsedTimeMode":"normal","useHandCursor":false,"resume":true,"kind":"slidelayer","isBaseLayer":true},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5WlDvsH0NEk"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6gWEjzJIjCW"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5e2lNFLTMLz"}}]},{"kind":"ontimelinetick","time":2750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5d1huHYIml2"}}]},{"kind":"ontimelinetick","time":4750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6U24G6R2tkI"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":14,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":118}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6gWEjzJIjCW"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":24,"yPos":144,"tabIndex":15,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":616,"rotateYPos":244,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1233,"bottom":489,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":119}},"html5data":{"xPos":-2,"yPos":-2,"width":1235,"height":491,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1232,"height":488,"resume":true,"useHandCursor":true,"id":"5WlDvsH0NEk"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6B5id81aGsH_-1467891299","id":"01","linkId":"txt__default_5d1huHYIml2","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":212,"height":46,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Rever curso","style":{"fontSize":16,"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":177,"bottom":46,"pngfb":false,"pr":{"l":"Lib","i":125}}},{"kind":"textdata","uniqueId":"66SEB8XQMLh_-1173331664","id":"02","linkId":"txt__default_Visited_5d1huHYIml2","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":212,"height":46,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Rever curso","style":{"fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":177,"bottom":46,"pngfb":false,"pr":{"l":"Lib","i":126}}}],"shapemaskId":"","xPos":328,"yPos":532,"tabIndex":17,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":28,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":120}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":121}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":122}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Visited_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":123}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Rever curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":56,"resume":true,"useHandCursor":true,"id":"5d1huHYIml2","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_visited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_savevisited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetVisitedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"var","value":"#_visited"}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"ActGrpUnchecked"}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.6U24G6R2tkI.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.6U24G6R2tkI._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.6U24G6R2tkI"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpUnchecked"},{"kind":"exe_actiongroup","id":"_this.ActGrpSetVisitedState"},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5iIc0qTfLwt_-501658965","id":"01","linkId":"txt__default_6U24G6R2tkI","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":212,"height":46,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Sair do curso","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":182,"bottom":46,"pngfb":false,"pr":{"l":"Lib","i":127}}},{"kind":"textdata","uniqueId":"6LZBW5DUjtb_728519704","id":"02","linkId":"txt__default_Visited_6U24G6R2tkI","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":212,"height":46,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Sair do curso","style":{"fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":182,"bottom":46,"pngfb":false,"pr":{"l":"Lib","i":128}}}],"shapemaskId":"","xPos":728,"yPos":532,"tabIndex":18,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":28,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":120}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":121}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":122}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Visited_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":123}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":57,"altText":"Sair do curso","pngfb":false,"pr":{"l":"Lib","i":124}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":59,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":56,"resume":true,"useHandCursor":true,"id":"6U24G6R2tkI","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_visited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_savevisited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetVisitedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"var","value":"#_visited"}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"ActGrpUnchecked"}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5d1huHYIml2.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5d1huHYIml2._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5d1huHYIml2"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpUnchecked"},{"kind":"exe_actiongroup","id":"_this.ActGrpSetVisitedState"},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"close_player"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5e2lNFLTMLz_153272514","id":"01","linkId":"txt__default_5e2lNFLTMLz","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":780,"height":175,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Parabéns! \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":20,"foregroundColor":"#000000","linkColor":"#000000","ascent":26.133,"descent":6.267,"leading":0,"underlinePosition":-1.36,"underlineThickness":1.307,"xHeight":14.667}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Terminou o estudo do primeiro módulo do curso de\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":20,"foregroundColor":"#000000","linkColor":"#000000","ascent":26.133,"descent":6.267,"leading":0,"underlinePosition":-1.36,"underlineThickness":1.307,"xHeight":14.667}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"tagType":"P"},"runs":[{"idx":0,"len":49,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":" \\n","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"tagType":"P"},"runs":[{"idx":0,"len":2,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Inteligência Artificial para Desenvolvimento de Negócios","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":56,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":762,"bottom":184,"pngfb":false,"pr":{"l":"Lib","i":130}}}],"shapemaskId":"","xPos":240,"yPos":296,"tabIndex":16,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":400,"rotateYPos":92.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":800,"bottom":185,"altText":"Parabéns! \\nTerminou o estudo do primeiro módulo do curso de\\n \\nInteligência Artificial para Desenvolvimento de Negócios","pngfb":false,"pr":{"l":"Lib","i":129}},"html5data":{"xPos":0,"yPos":0,"width":800,"height":185,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":800,"height":185,"resume":true,"useHandCursor":true,"id":"5e2lNFLTMLz"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6K05dwUGxUO","events":[{"kind":"onslidestart","actions":[{"kind":"exe_javascript","id":"5vQaNYv0RzK"},{"kind":"adjustvar","variable":"_player.popup","operator":"set","value":{"type":"boolean","value":true}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":39500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tan86yaQfz"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VCYeYUEni1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69s3gqNHg1Y"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ayzt09NtEH"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5whFHQDPFcs"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6jXPK0rdGU9"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lVfqqZUc0d"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VHGXGQPaHr"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6dIY6ufVyqZ"}}]},{"kind":"ontimelinetick","time":7979,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"65Z2OtQpB8s"}}]},{"kind":"ontimelinetick","time":12750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6a0vWVXybMh"}}]},{"kind":"ontimelinetick","time":19000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5l4SITyHfG9"}}]},{"kind":"ontimelinetick","time":26000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5r3n1dkaOym"}}]},{"kind":"ontimelinetick","time":31500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ht0I6wfKd3"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":0,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"5tan86yaQfz"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":72,"tabIndex":1,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":291.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":585,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":29}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":586,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":584,"resume":true,"useHandCursor":true,"id":"5ayzt09NtEH"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":2,"id":"01","url":"story_content/6Q70oTwPFfL_RC36618.png","type":"normal","altText":"target-audience.png","width":512,"height":512,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":80,"yPos":192,"tabIndex":4,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":160,"rotateYPos":160,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":320,"bottom":320,"altText":"target-audience.png","pngfb":false,"pr":{"l":"Lib","i":30}},"html5data":{"xPos":0,"yPos":0,"width":320,"height":320,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":320,"height":320,"resume":true,"useHandCursor":true,"id":"6VCYeYUEni1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69s3gqNHg1Y_-519312530","id":"01","linkId":"txt__default_69s3gqNHg1Y","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":652,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"OBJECTIVOS DO CURSO","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"linkColor":"#1888E7","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":452,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":32}}}],"shapemaskId":"","xPos":504,"yPos":104,"tabIndex":3,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":336,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":672,"bottom":40,"altText":"OBJECTIVOS DO CURSO","pngfb":false,"pr":{"l":"Lib","i":31}},"html5data":{"xPos":0,"yPos":0,"width":672,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":672,"height":40,"resume":true,"useHandCursor":true,"id":"69s3gqNHg1Y"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":2,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5lVfqqZUc0d","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_5whFHQDPFcs","type":"hiddentext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":756,"height":374,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":5,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":776,"bottom":384,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\nConhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\nIdentificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\nEntender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\nReconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\nFamiliarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\nExplorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\nCompreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","pngfb":false,"pr":{"l":"Lib","i":35}},"html5data":{"xPos":0,"yPos":0,"width":776,"height":384,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":true,"useHandCursor":true,"id":"5whFHQDPFcs"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5ht0I6wfKd3","id":"01","linkId":"txt_5ht0I6wfKd3","type":"acctext","xPos":10,"yPos":330,"xAccOffset":10,"yAccOffset":330,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":694,"bottom":376,"pngfb":false,"pr":{"l":"Lib","i":37}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":67,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":694,"bottom":376,"altText":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":694,"height":376,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5ht0I6wfKd3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5r3n1dkaOym","id":"01","linkId":"txt_5r3n1dkaOym","type":"acctext","xPos":10,"yPos":273,"xAccOffset":10,"yAccOffset":273,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":746,"bottom":319,"pngfb":false,"pr":{"l":"Lib","i":38}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":68,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":746,"bottom":319,"altText":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":746,"height":319,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5r3n1dkaOym"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5l4SITyHfG9","id":"01","linkId":"txt_5l4SITyHfG9","type":"acctext","xPos":10,"yPos":238,"xAccOffset":10,"yAccOffset":238,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":700,"bottom":261,"pngfb":false,"pr":{"l":"Lib","i":39}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":69,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":700,"bottom":261,"altText":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":700,"height":261,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5l4SITyHfG9"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6a0vWVXybMh","id":"01","linkId":"txt_6a0vWVXybMh","type":"acctext","xPos":10,"yPos":181,"xAccOffset":10,"yAccOffset":181,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":671,"bottom":227,"pngfb":false,"pr":{"l":"Lib","i":40}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":70,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":671,"bottom":227,"altText":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":671,"height":227,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6a0vWVXybMh"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_65Z2OtQpB8s","id":"01","linkId":"txt_65Z2OtQpB8s","type":"acctext","xPos":10,"yPos":123,"xAccOffset":10,"yAccOffset":123,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":742,"bottom":169,"pngfb":false,"pr":{"l":"Lib","i":41}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":71,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":742,"bottom":169,"altText":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":742,"height":169,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"65Z2OtQpB8s"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6dIY6ufVyqZ","id":"01","linkId":"txt_6dIY6ufVyqZ","type":"acctext","xPos":10,"yPos":66,"xAccOffset":10,"yAccOffset":66,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":714,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":42}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":72,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":714,"bottom":112,"altText":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":714,"height":112,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6dIY6ufVyqZ"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6VHGXGQPaHr","id":"01","linkId":"txt_6VHGXGQPaHr","type":"acctext","xPos":10,"yPos":9,"xAccOffset":10,"yAccOffset":9,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":705,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":43}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":73,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":705,"bottom":54,"altText":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":705,"height":54,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6VHGXGQPaHr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6jXPK0rdGU9","id":"01","linkId":"txt_6jXPK0rdGU9","type":"acctext","xPos":10,"yPos":-26,"xAccOffset":10,"yAccOffset":-26,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":-27,"right":610,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":44}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":74,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":-27,"right":610,"bottom":0,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":-27,"width":610,"height":27,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6jXPK0rdGU9"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6QtAxFNOoxm"},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TRpicPw6tU"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VcGQGd6sQ4"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6HniC2d4w8h"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nw6rMDcq88"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5wH7XkMdNj1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fLNGDgml4K"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5a1AGGT21Cq"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ex489sIWwL"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":6,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6TRpicPw6tU"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":64,"tabIndex":7,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":279.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":561,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":45}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":562,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":560,"resume":true,"useHandCursor":true,"id":"5VcGQGd6sQ4"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":3,"id":"01","url":"story_content/6S7pEErDZEG.png","type":"normal","altText":"Agrupar 1.png","width":959,"height":657,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":1,"yPos":80,"tabIndex":8,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":391,"rotateYPos":268,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":782,"bottom":536,"altText":"Agrupar 1.png","pngfb":false,"pr":{"l":"Lib","i":46}},"html5data":{"xPos":0,"yPos":0,"width":782,"height":536,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":782,"height":536,"resume":true,"useHandCursor":true,"id":"6HniC2d4w8h"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":16,"yPos":94,"tabIndex":10,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":40,"rotateYPos":18.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":80,"bottom":37,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":47}},"html5data":{"xPos":0,"yPos":0,"width":80,"height":37,"strokewidth":0}},"width":80,"height":37,"resume":true,"useHandCursor":true,"id":"5nw6rMDcq88"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":9,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5wH7XkMdNj1","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6fLNGDgml4K_1529763356","id":"01","linkId":"txt__default_6fLNGDgml4K","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":308,"height":190,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Play/Pause\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Timeline\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reiniciar ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Volume\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Voltar ao menu\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Objectivos do curso","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":191,"bottom":193,"pngfb":false,"pr":{"l":"Lib","i":49}}}],"shapemaskId":"","xPos":24,"yPos":128,"tabIndex":11,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":164,"rotateYPos":100,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":328,"bottom":200,"altText":"Play/Pause\\nTimeline\\nReiniciar ecrã\\nVolume\\nVoltar ao menu\\nObjectivos do curso","pngfb":false,"pr":{"l":"Lib","i":48}},"html5data":{"xPos":0,"yPos":0,"width":328,"height":200,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":328,"height":200,"resume":true,"useHandCursor":true,"id":"6fLNGDgml4K"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5a1AGGT21Cq_1013326046","id":"01","linkId":"txt__default_5a1AGGT21Cq","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":420,"height":222,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"7.     Instruções de navegação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"8.      A sua pontuação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"9.      Instruções de interacções no ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"10.   Notas/curiosidade\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"11.   Voltar ao ecrã anterior\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":30,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"12.   Paginação \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":17,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"13.   Avançar para o próximo ecrã","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":33,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":225,"pngfb":false,"pr":{"l":"Lib","i":51}}}],"shapemaskId":"","xPos":320,"yPos":128,"tabIndex":12,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":220,"rotateYPos":116,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":440,"bottom":232,"altText":"7.     Instruções de navegação\\n8.      A sua pontuação\\n9.      Instruções de interacções no ecrã\\n10.   Notas/curiosidade\\n11.   Voltar ao ecrã anterior\\n12.   Paginação \\n13.   Avançar para o próximo ecrã","pngfb":false,"pr":{"l":"Lib","i":50}},"html5data":{"xPos":0,"yPos":0,"width":440,"height":232,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":440,"height":232,"resume":true,"useHandCursor":true,"id":"5a1AGGT21Cq"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6ex489sIWwL_1502302535","id":"01","linkId":"txt__default_6ex489sIWwL","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":428,"height":363,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":136,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Ao finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":133,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":153,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":435,"bottom":368,"pngfb":false,"pr":{"l":"Lib","i":53}}}],"shapemaskId":"","xPos":808,"yPos":168,"tabIndex":13,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":224,"rotateYPos":186.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":448,"bottom":373,"altText":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\nAo finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\nA pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","pngfb":false,"pr":{"l":"Lib","i":52}},"html5data":{"xPos":0,"yPos":0,"width":448,"height":373,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":448,"height":373,"resume":true,"useHandCursor":true,"id":"6ex489sIWwL"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5Xlh34Ekb5w"}],"showAnimationId":"","lmsId":"Slide5","width":1280,"height":720,"resume":true,"background":{"type":"fill","fill":{"type":"linear","rotation":90,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}},"id":"6cac0NAnUiv","events":[{"kind":"onslidestart","actions":[{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_1","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5mwZuA4qmHt"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5mwZuA4qmHt.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5mwZuA4qmHt"}}],"elseActions":[{"kind":"adjustvar","variable":"5mwZuA4qmHt._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"5mwZuA4qmHt._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5mwZuA4qmHt.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5mwZuA4qmHt"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_2","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5mwZuA4qmHt"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5mwZuA4qmHt.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5mwZuA4qmHt"}}],"elseActions":[{"kind":"adjustvar","variable":"5mwZuA4qmHt._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"5mwZuA4qmHt._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5mwZuA4qmHt.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5mwZuA4qmHt"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5mwZuA4qmHt"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_2","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6cTmDU4NYDz"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6cTmDU4NYDz.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6cTmDU4NYDz"}}],"elseActions":[{"kind":"adjustvar","variable":"6cTmDU4NYDz._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"6cTmDU4NYDz._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6cTmDU4NYDz.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6cTmDU4NYDz"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_3","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6cTmDU4NYDz"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6cTmDU4NYDz.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6cTmDU4NYDz"}}],"elseActions":[{"kind":"adjustvar","variable":"6cTmDU4NYDz._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"6cTmDU4NYDz._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6cTmDU4NYDz.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6cTmDU4NYDz"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6cTmDU4NYDz"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_3","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fztF0NBXpJ"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6fztF0NBXpJ.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6fztF0NBXpJ"}}],"elseActions":[{"kind":"adjustvar","variable":"6fztF0NBXpJ._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"6fztF0NBXpJ._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6fztF0NBXpJ.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6fztF0NBXpJ"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_4","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fztF0NBXpJ"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6fztF0NBXpJ.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6fztF0NBXpJ"}}],"elseActions":[{"kind":"adjustvar","variable":"6fztF0NBXpJ._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"6fztF0NBXpJ._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6fztF0NBXpJ.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6fztF0NBXpJ"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6fztF0NBXpJ"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_4","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5XYilhnKgm4"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5XYilhnKgm4.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5XYilhnKgm4"}}],"elseActions":[{"kind":"adjustvar","variable":"5XYilhnKgm4._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"5XYilhnKgm4._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5XYilhnKgm4.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5XYilhnKgm4"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5XYilhnKgm4"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_5","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5XYilhnKgm4"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5XYilhnKgm4.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5XYilhnKgm4"}}],"elseActions":[{"kind":"adjustvar","variable":"5XYilhnKgm4._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"5XYilhnKgm4._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5XYilhnKgm4.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5XYilhnKgm4"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5XYilhnKgm4"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade_1","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5httvvXoEIB.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5httvvXoEIB"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5httvvXoEIB.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"5httvvXoEIB.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5httvvXoEIB"}}],"elseActions":[{"kind":"adjustvar","variable":"5httvvXoEIB._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"5httvvXoEIB._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"5httvvXoEIB.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"5httvvXoEIB"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"5httvvXoEIB"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"gte","valuea":"_player.#nota","typea":"var","valueb":16,"typeb":"number"},{"kind":"compare","operator":"eq","valuea":"5httvvXoEIB.#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"5mwZuA4qmHt.#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"6cTmDU4NYDz.#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"6fztF0NBXpJ.#_state","typea":"var","valueb":"Terminado","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"5XYilhnKgm4.#_state","typea":"var","valueb":"Terminado","typeb":"string"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6djpC6Eiieu.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6djpC6Eiieu"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6djpC6Eiieu.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6djpC6Eiieu.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6djpC6Eiieu"}}],"elseActions":[{"kind":"adjustvar","variable":"6djpC6Eiieu._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"6djpC6Eiieu._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6djpC6Eiieu.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6djpC6Eiieu"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6djpC6Eiieu"}}]}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Testefinal","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"show_slidelayer","hideOthers":"oncomplete","transition":"appear","objRef":{"type":"string","value":"6K05dwUGxUO"}}]},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Testefinal","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6djpC6Eiieu.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6djpC6Eiieu"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6djpC6Eiieu.#_state","typea":"var","valueb":"Terminado","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6djpC6Eiieu.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6djpC6Eiieu"}}],"elseActions":[{"kind":"adjustvar","variable":"6djpC6Eiieu._state","operator":"set","value":{"type":"string","value":"Terminado"}},{"kind":"adjustvar","variable":"6djpC6Eiieu._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6djpC6Eiieu.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"Terminado"},"objRef":{"type":"string","value":"6djpC6Eiieu"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6djpC6Eiieu"}}]}]}]},{"kind":"onbeforeslidein","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"npnxnanbnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"npnxnanbnsnfns10110000101"}]}]},{"kind":"ontransitionin","actions":[{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"set","value":{"type":"string","value":"_player."}},{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"add","value":{"type":"property","value":"$AbsoluteId"}}]}]}');