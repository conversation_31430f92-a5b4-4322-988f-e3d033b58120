import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  BookOpen, 
  ArrowLeft, 
  Play, 
  Pause,
  RotateCcw,
  Maximize,
  Award,
  Clock,
  User,
  CheckCircle,
  Brain,
  Sparkles
} from 'lucide-react';

const ENSACourse: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(1);
  const [totalSlides] = useState(25); // Exemplo de 25 slides
  const [timeSpent, setTimeSpent] = useState(0);

  // Simular progresso baseado no slide atual
  useEffect(() => {
    const newProgress = Math.round((currentSlide / totalSlides) * 100);
    setProgress(newProgress);
co
              </CardContent>
            </Card>

            {/* Course Navigation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <BookOpen className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 1</h3>
                  <p className="text-sm text-muted-foreground">Introdução à IA</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 8 ? 'Concluído' : 'Em Progresso'}
                  </Badge>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Brain className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 2</h3>
                  <p className="text-sm text-muted-foreground">Machine Learning</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 16 ? 'Concluído' : currentSlide >= 8 ? 'Em Progresso' : 'Bloqueado'}
                  </Badge>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 3</h3>
                  <p className="text-sm text-muted-foreground">Aplicações Práticas</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 25 ? 'Concluído' : currentSlide >= 16 ? 'Em Progresso' : 'Bloqueado'}
                  </Badge>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="progress" className="space-y-6 animate-slide-up">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle>Estatísticas de Aprendizado</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Progresso Geral</span>
                    <span className="text-sm font-bold">{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Slides Vistos:</span>
                      <p className="font-medium">{currentSlide}/{totalSlides}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tempo Gasto:</span>
                      <p className="font-medium">{formatTime(timeSpent)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle>Conquistas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 1 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Primeiro Passo</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 8 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Módulo 1 Completo</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 16 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Meio Caminho</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 25 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Especialista em IA</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="info" className="space-y-6 animate-slide-up">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>Sobre o Curso</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Este curso interativo de Inteligência Artificial foi desenvolvido pela ENSA (Escola Nacional de Seguros) 
                  utilizando a tecnologia Storyline para proporcionar uma experiência de aprendizado imersiva e envolvente.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h3 className="font-semibold">Objetivos do Curso</h3>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Compreender os fundamentos da Inteligência Artificial</li>
                      <li>• Explorar técnicas de Machine Learning</li>
                      <li>• Conhecer aplicações práticas no mercado de seguros</li>
                      <li>• Desenvolver pensamento analítico e estratégico</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-3">
                    <h3 className="font-semibold">Metodologia</h3>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Conteúdo interativo com Storyline</li>
                      <li>• Exercícios práticos e simulações</li>
                      <li>• Avaliações formativas</li>
                      <li>• Certificado de conclusão</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default ENSACourse;
