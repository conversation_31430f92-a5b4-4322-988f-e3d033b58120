import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  BookOpen, 
  ArrowLeft, 
  Play, 
  Pause,
  RotateCcw,
  Maximize,
  Award,
  Clock,
  User,
  CheckCircle,
  Brain,
  Sparkles
} from 'lucide-react';

const ENSACourse: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(1);
  const [totalSlides] = useState(25); // Exemplo de 25 slides
  const [timeSpent, setTimeSpent] = useState(0);

  // Simular progresso baseado no slide atual
  useEffect(() => {
    const newProgress = Math.round((currentSlide / totalSlides) * 100);
    setProgress(newProgress);
  }, [currentSlide, totalSlides]);

  // Simular contador de tempo
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying) {
      interval = setInterval(() => {
        setTimeSpent(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying]);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
    toast({
      title: isPlaying ? "Curso pausado" : "Curso iniciado",
      description: isPlaying ? "Você pode retomar a qualquer momento." : "Boa sorte com seus estudos!",
    });
  };

  const handleRestart = () => {
    setCurrentSlide(1);
    setProgress(0);
    setTimeSpent(0);
    setIsPlaying(false);
    toast({
      title: "Curso reiniciado",
      description: "Você voltou ao início do curso.",
    });
  };

  const handleSlideChange = (direction: 'next' | 'prev') => {
    if (direction === 'next' && currentSlide < totalSlides) {
      setCurrentSlide(prev => prev + 1);
    } else if (direction === 'prev' && currentSlide > 1) {
      setCurrentSlide(prev => prev - 1);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      {/* Page Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="hover-lift focus-ring"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </div>
          
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center hover-glow transition-all duration-300">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl sm:text-3xl font-bold">Curso ENSA de Inteligência Artificial</h1>
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Especial
                  </Badge>
                </div>
                <p className="text-muted-foreground">Curso interativo desenvolvido pela ENSA com tecnologia Storyline</p>
                <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    ENSA - Escola Nacional de Seguros
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formatTime(timeSpent)} gastos
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-4 h-4" />
                    {currentSlide}/{totalSlides} slides
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={handleRestart}
                className="hover-lift focus-ring"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reiniciar
              </Button>
              <Button 
                onClick={handlePlay}
                className={`btn-primary-enhanced focus-ring ${isPlaying ? 'bg-orange-500 hover:bg-orange-600' : ''}`}
              >
                {isPlaying ? (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Pausar
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    {progress > 0 ? 'Continuar' : 'Iniciar'}
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progresso do Curso</span>
              <span className="text-sm font-bold">{progress}%</span>
            </div>
            <Progress value={progress} className="w-full h-2" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container-responsive py-8">
        <Tabs defaultValue="course" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="course" className="transition-all duration-300 hover-lift focus-ring">
              <Brain className="w-4 h-4 mr-2" />
              Curso
            </TabsTrigger>
            <TabsTrigger value="progress" className="transition-all duration-300 hover-lift focus-ring">
              <CheckCircle className="w-4 h-4 mr-2" />
              Progresso
            </TabsTrigger>
            <TabsTrigger value="info" className="transition-all duration-300 hover-lift focus-ring">
              <BookOpen className="w-4 h-4 mr-2" />
              Informações
            </TabsTrigger>
          </TabsList>

          <TabsContent value="course" className="space-y-6 animate-slide-up">
            {/* Storyline Player Container */}
            <Card className="shadow-card animate-fade-in">
              <CardContent className="p-0">
                <div className="relative bg-gradient-to-br from-slate-900 to-slate-800 rounded-lg overflow-hidden">
                  {/* Placeholder for Storyline Content */}
                  <div className="aspect-video flex items-center justify-center text-white">
                    <div className="text-center space-y-4">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto">
                        <Brain className="w-12 h-12" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold mb-2">Curso de Inteligência Artificial</h3>
                        <p className="text-slate-300 mb-4">Slide {currentSlide} de {totalSlides}</p>
                        <div className="space-y-2">
                          <p className="text-lg">🤖 Fundamentos de IA</p>
                          <p className="text-lg">🧠 Machine Learning</p>
                          <p className="text-lg">🔮 Redes Neurais</p>
                          <p className="text-lg">🚀 Aplicações Práticas</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Controls Overlay */}
                  <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleSlideChange('prev')}
                      disabled={currentSlide === 1}
                      className="bg-black/50 hover:bg-black/70 text-white border-0"
                    >
                      ← Anterior
                    </Button>
                    
                    <div className="flex items-center gap-2 bg-black/50 px-3 py-1 rounded-full text-white text-sm">
                      <span>{currentSlide}/{totalSlides}</span>
                    </div>
                    
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleSlideChange('next')}
                      disabled={currentSlide === totalSlides}
                      className="bg-black/50 hover:bg-black/70 text-white border-0"
                    >
                      Próximo →
                    </Button>
                  </div>

                  {/* Fullscreen Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white border-0"
                  >
                    <Maximize className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Course Navigation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <BookOpen className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 1</h3>
                  <p className="text-sm text-muted-foreground">Introdução à IA</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 8 ? 'Concluído' : 'Em Progresso'}
                  </Badge>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Brain className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 2</h3>
                  <p className="text-sm text-muted-foreground">Machine Learning</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 16 ? 'Concluído' : currentSlide >= 8 ? 'Em Progresso' : 'Bloqueado'}
                  </Badge>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold mb-1">Módulo 3</h3>
                  <p className="text-sm text-muted-foreground">Aplicações Práticas</p>
                  <Badge variant="outline" className="mt-2">
                    {currentSlide >= 25 ? 'Concluído' : currentSlide >= 16 ? 'Em Progresso' : 'Bloqueado'}
                  </Badge>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="progress" className="space-y-6 animate-slide-up">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle>Estatísticas de Aprendizado</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Progresso Geral</span>
                    <span className="text-sm font-bold">{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Slides Vistos:</span>
                      <p className="font-medium">{currentSlide}/{totalSlides}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tempo Gasto:</span>
                      <p className="font-medium">{formatTime(timeSpent)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle>Conquistas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 1 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Primeiro Passo</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 8 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Módulo 1 Completo</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 16 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Meio Caminho</span>
                  </div>
                  <div className={`flex items-center gap-3 p-2 rounded-lg ${currentSlide >= 25 ? 'bg-green-50 text-green-700' : 'bg-muted/50 text-muted-foreground'}`}>
                    <Award className="w-5 h-5" />
                    <span className="text-sm font-medium">Especialista em IA</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="info" className="space-y-6 animate-slide-up">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>Sobre o Curso</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Este curso interativo de Inteligência Artificial foi desenvolvido pela ENSA (Escola Nacional de Seguros) 
                  utilizando a tecnologia Storyline para proporcionar uma experiência de aprendizado imersiva e envolvente.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h3 className="font-semibold">Objetivos do Curso</h3>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Compreender os fundamentos da Inteligência Artificial</li>
                      <li>• Explorar técnicas de Machine Learning</li>
                      <li>• Conhecer aplicações práticas no mercado de seguros</li>
                      <li>• Desenvolver pensamento analítico e estratégico</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-3">
                    <h3 className="font-semibold">Metodologia</h3>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Conteúdo interativo com Storyline</li>
                      <li>• Exercícios práticos e simulações</li>
                      <li>• Avaliações formativas</li>
                      <li>• Certificado de conclusão</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default ENSACourse;
