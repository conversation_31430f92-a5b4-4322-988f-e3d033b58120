import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className }) => {
  const location = useLocation();
  
  // Auto-generate breadcrumb items based on current path if not provided
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbItems: BreadcrumbItem[] = [
      { label: 'Dashboard', href: '/dashboard' }
    ];

    // Map path segments to readable labels
    const pathLabels: { [key: string]: string } = {
      'course': 'Curso',
      'student': '<PERSON><PERSON>',
      'settings': 'Configurações',
      'reports': 'Relatórios',
      'dashboard': 'Dashboard'
    };

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Skip dashboard as it's already added
      if (segment === 'dashboard') return;
      
      const isLast = index === pathSegments.length - 1;
      const label = pathLabels[segment] || segment;
      
      breadcrumbItems.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast
      });
    });

    return breadcrumbItems;
  };

  const breadcrumbItems = items || generateBreadcrumbItems();

  // Don't show breadcrumb on dashboard root
  if (location.pathname === '/dashboard' || location.pathname === '/') {
    return null;
  }

  return (
    <nav 
      aria-label="Breadcrumb" 
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
    >
      <Link
        to="/dashboard"
        className="flex items-center hover:text-foreground transition-colors duration-200 focus-ring rounded px-1"
      >
        <Home className="w-4 h-4" />
        <span className="sr-only">Dashboard</span>
      </Link>
      
      {breadcrumbItems.length > 1 && (
        <>
          <ChevronRight className="w-4 h-4" />
          {breadcrumbItems.slice(1).map((item, index) => (
            <React.Fragment key={index}>
              {item.href && !item.current ? (
                <Link
                  to={item.href}
                  className="hover:text-foreground transition-colors duration-200 focus-ring rounded px-1"
                >
                  {item.label}
                </Link>
              ) : (
                <span 
                  className={cn(
                    "px-1",
                    item.current && "text-foreground font-medium"
                  )}
                  aria-current={item.current ? "page" : undefined}
                >
                  {item.label}
                </span>
              )}
              
              {index < breadcrumbItems.slice(1).length - 1 && (
                <ChevronRight className="w-4 h-4" />
              )}
            </React.Fragment>
          ))}
        </>
      )}
    </nav>
  );
};

export default Breadcrumb;
