import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { Course, StudentProgress, Lesson, Module, getStoredData, initializeMockData } from '@/data/mockData';
import { BookOpen, Clock, User, Play, CheckCircle } from 'lucide-react';
import LessonViewer from '@/components/LessonViewer';
import QuizViewer from '@/components/QuizViewer';

const StudentDashboard = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [progress, setProgress] = useState<StudentProgress[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [currentLesson, setCurrentLesson] = useState<{ lesson: Lesson; module: Module } | null>(null);
  const [currentQuiz, setCurrentQuiz] = useState<{ quiz: any; courseId: string } | null>(null);

  useEffect(() => {
    initializeMockData();
    const storedCourses = getStoredData<Course[]>('ecurso_courses', []);
    const storedProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
    
    // Filter courses for current student
    const studentCourses = storedCourses.filter(course => 
      course.students.includes(user?.id || '')
    );
    
    setCourses(studentCourses);
    setProgress(storedProgress);
  }, [user?.id]);

  const getProgressForCourse = (courseId: string) => {
    const courseProgress = progress.find(p => 
      p.studentId === user?.id && p.courseId === courseId
    );
    return courseProgress?.progress || 0;
  };

  const handleCourseSelect = (course: Course) => {
    setSelectedCourse(course);
  };

  const handleBackToCourses = () => {
    setSelectedCourse(null);
    setCurrentLesson(null);
    setCurrentQuiz(null);
  };

  const handleLessonClick = (lesson: Lesson, module: Module) => {
    setCurrentLesson({ lesson, module });
  };

  const handleQuizClick = (quiz: any, courseId: string) => {
    setCurrentQuiz({ quiz, courseId });
  };

  const handleLessonComplete = (lessonId: string) => {
    // Refresh progress data
    const storedProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
    setProgress(storedProgress);
  };

  const handleQuizComplete = (score: number) => {
    // Refresh progress data
    const storedProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
    setProgress(storedProgress);
  };

  // Show lesson viewer if a lesson is selected
  if (currentLesson && selectedCourse) {
    return (
      <LessonViewer
        course={selectedCourse}
        currentModule={currentLesson.module}
        currentLesson={currentLesson.lesson}
        onBack={handleBackToCourses}
        onLessonComplete={handleLessonComplete}
      />
    );
  }

  // Show quiz viewer if a quiz is selected
  if (currentQuiz) {
    return (
      <QuizViewer
        quiz={currentQuiz.quiz}
        courseId={currentQuiz.courseId}
        onBack={handleBackToCourses}
        onQuizComplete={handleQuizComplete}
      />
    );
  }

  if (selectedCourse) {
    return (
      <div className="min-h-screen bg-background">
        <div className="border-b bg-card">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" onClick={handleBackToCourses}>
                ← Voltar aos Cursos
              </Button>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">{selectedCourse.title}</h1>
                <p className="text-muted-foreground">{selectedCourse.description}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid gap-6">
            {selectedCourse.modules.map((module, moduleIndex) => (
              <Card key={module.id} className="shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <span className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                      {moduleIndex + 1}
                    </span>
                    {module.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {module.lessons.map((lesson, lessonIndex) => (
                      <button 
                        key={lesson.id} 
                        onClick={() => handleLessonClick(lesson, module)}
                        className="w-full flex items-center gap-3 p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                      >
                        <div className="w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center">
                          {lesson.type === 'video' ? <Play className="w-4 h-4" /> : <BookOpen className="w-4 h-4" />}
                        </div>
                        <div className="flex-1 text-left">
                          <h4 className="font-medium">{lesson.title}</h4>
                          {lesson.duration && (
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {lesson.duration}
                            </p>
                          )}
                        </div>
                        <Badge variant="outline" className="bg-success/10 text-success border-success/20">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Completo
                        </Badge>
                      </button>
                    ))}
                    
                    {module.quiz && (
                      <button 
                        onClick={() => handleQuizClick(module.quiz, selectedCourse.id)}
                        className="w-full flex items-center gap-3 p-3 bg-warning/10 rounded-lg border border-warning/20 hover:bg-warning/20 transition-colors cursor-pointer"
                      >
                        <div className="w-8 h-8 bg-warning text-warning-foreground rounded-full flex items-center justify-center">
                          ?
                        </div>
                        <div className="flex-1 text-left">
                          <h4 className="font-medium">{module.quiz.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {module.quiz.questions.length} questões
                          </p>
                        </div>
                        <Button size="sm" className="pointer-events-none">Iniciar Quiz</Button>
                      </button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Olá, {user?.name}! 👋</h1>
              <p className="text-muted-foreground">Continue seus estudos</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-6">
          <div>
            <h2 className="text-2xl font-bold mb-6">Seus Cursos</h2>
            
            {courses.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <BookOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Nenhum curso encontrado</h3>
                  <p className="text-muted-foreground">
                    Entre em contato com seu administrador para se inscrever em cursos.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {courses.map((course) => {
                  const courseProgress = getProgressForCourse(course.id);
                  return (
                    <Card 
                      key={course.id} 
                      className="shadow-card hover:shadow-primary/10 transition-all duration-300 cursor-pointer group"
                      onClick={() => handleCourseSelect(course)}
                    >
                      <CardHeader>
                        <div className="w-12 h-12 bg-gradient-secondary rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                          <BookOpen className="w-6 h-6 text-white" />
                        </div>
                        <CardTitle className="group-hover:text-primary transition-colors">
                          {course.title}
                        </CardTitle>
                        <CardDescription>{course.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <User className="w-4 h-4" />
                            {course.instructor}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Clock className="w-4 h-4" />
                            {course.duration}
                          </div>
                          
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>Progresso</span>
                              <span className="font-medium">{courseProgress}%</span>
                            </div>
                            <Progress value={courseProgress} className="h-2" />
                          </div>
                          
                          <Button className="w-full bg-gradient-primary hover:shadow-primary transition-all duration-300">
                            Continuar Curso
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;