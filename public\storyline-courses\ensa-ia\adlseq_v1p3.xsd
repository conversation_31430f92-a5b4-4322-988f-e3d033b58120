<?xml version = "1.0"?>
<xs:schema targetNamespace = "http://www.adlnet.org/xsd/adlseq_v1p3"
           xmlns:xs = "http://www.w3.org/2001/XMLSchema"
           xmlns = "http://www.adlnet.org/xsd/adlseq_v1p3"
           elementFormDefault = "qualified"
           version = "1.0">

   <xs:annotation>
      <xs:documentation>
          This file represents the W3C XML Schema Language Binding of the ADL namespaced elements for sequencing extensions.
      </xs:documentation>
   </xs:annotation>

   <!-- *********************** -->
   <!-- ** Change History ** -->
   <!-- *********************** -->
   <xs:annotation>
      <xs:documentation>
      *************************************************************************
      *                             Change History                            *
      *************************************************************************
      2003-18-09  Initial creation.
      2004-23-01  Final edits in preparation for release
      *************************************************************************
      </xs:documentation>
   </xs:annotation>

   <xs:element name = "constrainedChoiceConsiderations" type = "constrainChoiceConsiderationsType"/>

   <xs:element name = "rollupConsiderations" type = "rollupConsiderationsType"/>

   <xs:complexType name = "rollupConsiderationsType">
      <xs:attribute name = "requiredForSatisfied" default = "always" type = "rollupConsiderationType"/>
      <xs:attribute name = "requiredForNotSatisfied" default = "always" type = "rollupConsiderationType"/>
      <xs:attribute name = "requiredForCompleted" default = "always" type = "rollupConsiderationType"/>
      <xs:attribute name = "requiredForIncomplete" default = "always" type = "rollupConsiderationType"/>
      <xs:attribute name = "measureSatisfactionIfActive" default = "true" type = "xs:boolean"/>
   </xs:complexType>

   <xs:simpleType name = "rollupConsiderationType">
      <xs:restriction base = "xs:token">
         <xs:enumeration value = "always"/>
         <xs:enumeration value = "ifAttempted"/>
         <xs:enumeration value = "ifNotSkipped"/>
         <xs:enumeration value = "ifNotSuspended"/>
      </xs:restriction>
   </xs:simpleType>

   <xs:complexType name = "constrainChoiceConsiderationsType">
      <xs:attribute name = "preventActivation" default = "false" type = "xs:boolean"/>
      <xs:attribute name = "constrainChoice" default = "false" type = "xs:boolean"/>
   </xs:complexType>

   <xs:attribute name = "objectivesGlobalToSystem" default = "true" type = "xs:boolean" />               

</xs:schema>
