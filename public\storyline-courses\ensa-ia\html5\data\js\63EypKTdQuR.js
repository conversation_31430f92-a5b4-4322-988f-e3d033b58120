﻿window.globalProvideData('slide', '{"title":"Drag and Drop","trackViews":true,"showMenuResultIcon":false,"viewGroupId":"","historyGroupId":"","videoZoom":"","scrolling":false,"transition":"appear","transDuration":0,"transDir":1,"wipeTrans":false,"slideLock":false,"navIndex":-1,"globalAudioId":"","thumbnailid":"","slideNumberInScene":9,"includeInSlideCounts":true,"presenterRef":{"id":"none"},"slideLayers":[{"enableSeek":true,"enableReplay":true,"timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VAwI1iAR1S"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5bsExvTila6"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5UnJoTf42H8"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"65MX7lYtozV"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6NuNoX2kgNt"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5cTltgLBB40"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5zRATC8eWLk"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"68AMVtHDeON"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6lL3uPc3Vpa"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6WC4KQpwoOu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"61jub7Qy8OO"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5W3Xv2j6uLu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5hha4NXNQWL"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ctqN84HwS5"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fjXMMrmwFm"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5v2NzpQVpmK"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5lCisSsiBPi"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hI0znHlwXe"}}]},{"kind":"ontimelinetick","time":1500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tDVtCrHp0x"}}]},{"kind":"ontimelinetick","time":1750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5h0EEFoTKIg"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VGIHdXgYQp"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6H5aWIkudW5"}}]},{"kind":"ontimelinetick","time":2500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6AXLpbAqeVh"}}]},{"kind":"ontimelinetick","time":3000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6RdpVMCHVVu"}}]},{"kind":"ontimelinetick","time":3500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ODKwJK1xIo"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6CjCtsJG2FJ"}}]}]},"objects":[{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":51,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":50,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":1}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":2}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":64,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fgE044eYLG.6cac0NAnUiv"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":55,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":54,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":3}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":4}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":66,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.5Xlh34Ekb5w"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":53,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":52,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":5}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":6}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":65,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":3,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6QtAxFNOoxm"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_6ctqN84HwS5","type":"richvartext","xPos":10,"yPos":5,"xAccOffset":0,"yAccOffset":0,"width":212,"height":30,"device":false,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Pontuação: ","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"%_player.nota%","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"/20","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":28,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":135,"yPos":672,"tabIndex":47,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":41,"altText":"Pontuação: %_player.nota%/20","pngfb":false,"pr":{"l":"Lib","i":7}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":40,"resume":true,"useHandCursor":true,"id":"6ctqN84HwS5"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PYUvVg1zLz_545423499","id":"01","linkId":"txt__default_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6dBYEI4LvKs_2042009458","id":"02","linkId":"txt__default_Disabled_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":12}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":48,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6fjXMMrmwFm","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":24,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":true,"useHandCursor":true,"id":"5v2NzpQVpmK"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":15,"id":"01","url":"story_content/6aoJaGpyzi2_P_0_298_2000_1034.jpg","type":"normal","altText":"6786.jpg","width":2000,"height":1034,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":22,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":336,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":672,"altText":"6786.jpg","pngfb":false,"pr":{"l":"Lib","i":310}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":672,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":250,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":250,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":250,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":672,"resume":true,"useHandCursor":true,"id":"6lL3uPc3Vpa"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":23,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":true,"useHandCursor":true,"id":"6WC4KQpwoOu"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6JOHPLbdfL0_605550601","id":"01","linkId":"txt__default_5VAwI1iAR1S","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"6KsyrdESpOB_-1554332771","id":"02","linkId":"txt__default_Disabled_5VAwI1iAR1S","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":46,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"5VAwI1iAR1S","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5daENfe4J4j_530715345","id":"01","linkId":"txt__default_5bsExvTila6","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":33,"height":66,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"2","style":{"fontSize":13.5,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":13.77,"descent":4.23,"leading":3.6,"underlinePosition":-0.702,"underlineThickness":1.44,"xHeight":10.296}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"center","listLevel":0,"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":33,"bottom":53,"pngfb":false,"pr":{"l":"Lib","i":343}}}],"shapemaskId":"","xPos":32,"yPos":80,"tabIndex":33,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":26.5,"rotateYPos":38,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":54,"bottom":77,"altText":"2","pngfb":false,"pr":{"l":"Lib","i":311}},"html5data":{"xPos":-2,"yPos":-2,"width":56,"height":79,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":54,"bottom":77,"altText":"2","pngfb":false,"pr":{"l":"Lib","i":312}},"html5data":{"xPos":-2,"yPos":-2,"width":56,"height":79,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":53,"height":76,"resume":true,"useHandCursor":true,"id":"5bsExvTila6","variables":[{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrelease","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6jxJFnykHwR_2132942626","id":"01","linkId":"txt__default_5lCisSsiBPi","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Aprendizagem não supervisionada","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":345,"bottom":52,"pngfb":false,"pr":{"l":"Lib","i":510}}}],"shapemaskId":"","xPos":72,"yPos":248,"tabIndex":34,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":true,"dropenabled":false,"dragoverenabled":false,"snapx":184,"snapy":39,"dragreturn":false,"multidrop":false,"droptype":"snap","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem não supervisionada","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem não supervisionada","pngfb":false,"pr":{"l":"Lib","i":509}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"5lCisSsiBPi","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false},{"kind":"variable","name":"ItemWasDragged","type":"boolean","value":false,"resume":true}],"actionGroups":{"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"ondragstart","actions":[{"kind":"adjustvar","variable":"ItemWasDragged","operator":"set","value":{"type":"boolean","value":true}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5yEZPgBYNms_-473200262","id":"01","linkId":"txt__default_5tDVtCrHp0x","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Aprendizagem supervisionada","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":27,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":325,"bottom":52,"pngfb":false,"pr":{"l":"Lib","i":511}}}],"shapemaskId":"","xPos":72,"yPos":336,"tabIndex":37,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":true,"dropenabled":false,"dragoverenabled":false,"snapx":184,"snapy":39,"dragreturn":false,"multidrop":false,"droptype":"snap","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem supervisionada","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem supervisionada","pngfb":false,"pr":{"l":"Lib","i":509}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"5tDVtCrHp0x","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false},{"kind":"variable","name":"ItemWasDragged","type":"boolean","value":false,"resume":true}],"actionGroups":{"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"ondragstart","actions":[{"kind":"adjustvar","variable":"ItemWasDragged","operator":"set","value":{"type":"boolean","value":true}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6KITjiuecJY_67468618","id":"01","linkId":"txt__default_5h0EEFoTKIg","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Aprendizagem por reforço ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":25,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":311,"bottom":52,"pngfb":false,"pr":{"l":"Lib","i":512}}}],"shapemaskId":"","xPos":72,"yPos":424,"tabIndex":40,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":true,"dropenabled":false,"dragoverenabled":false,"snapx":184,"snapy":39,"dragreturn":false,"multidrop":false,"droptype":"snap","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem por reforço ","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Aprendizagem por reforço ","pngfb":false,"pr":{"l":"Lib","i":509}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"5h0EEFoTKIg","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false},{"kind":"variable","name":"ItemWasDragged","type":"boolean","value":false,"resume":true}],"actionGroups":{"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"ondragstart","actions":[{"kind":"adjustvar","variable":"ItemWasDragged","operator":"set","value":{"type":"boolean","value":true}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6Tgzt4MycBP_-1339256345","id":"01","linkId":"txt__default_5VGIHdXgYQp","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":190,"bottom":52,"pngfb":false,"pr":{"l":"Lib","i":513}}}],"shapemaskId":"","xPos":472,"yPos":248,"tabIndex":35,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":false,"dropenabled":true,"dragoverenabled":true,"snapx":184,"snapy":39,"dragreturn":true,"multidrop":true,"droptype":"stackrandom","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":" ","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Drop Correct","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":" ","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"5VGIHdXgYQp","variables":[{"kind":"variable","name":"_dropcorrect","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDropCorrectState":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropcorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropincorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropincorrect","operator":"set","value":{"type":"boolean","value":false}}]},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":472,"yPos":336,"tabIndex":38,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":15,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":false,"dropenabled":true,"dragoverenabled":true,"snapx":184,"snapy":39,"dragreturn":true,"multidrop":true,"droptype":"stackrandom","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":514}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Drop Correct","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Rectangle 4","pngfb":false,"pr":{"l":"Lib","i":514}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"6H5aWIkudW5","variables":[{"kind":"variable","name":"_dropcorrect","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDropCorrectState":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropcorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropincorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropincorrect","operator":"set","value":{"type":"boolean","value":false}}]},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":472,"yPos":424,"tabIndex":41,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":16,"scrolling":true,"shuffleLock":false,"dragdrop":{"dragenabled":false,"dropenabled":true,"dragoverenabled":true,"snapx":184,"snapy":39,"dragreturn":true,"multidrop":true,"droptype":"stackrandom","dropoffsetx":20,"dropoffsety":20,"dragdropids":["_drop_6OHjx363Ipm"],"dragoverids":["_drop_6OHjx363Ipm"]},"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":514}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Drop Correct","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"Rectangle 4","pngfb":false,"pr":{"l":"Lib","i":514}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"6AXLpbAqeVh","variables":[{"kind":"variable","name":"_dropcorrect","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetDropCorrectState":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropcorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"if_action","condition":{"statement":{"kind":"varexists","value":"_dropincorrect","type":"string"}},"thenActions":[{"kind":"adjustvar","variable":"_dropincorrect","operator":"set","value":{"type":"boolean","value":false}}]},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5bsExvTila6.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5bsExvTila6._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5bsExvTila6"}}]}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_dropcorrect","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6plurcikBGF_-1758940578","id":"01","linkId":"txt__default_6RdpVMCHVVu","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A máquina aprende com dados ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":"rotulados","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":" e faz previsões a partir dos exemplos.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"leadingMargin":0,"firstLineMargin":0,"lineSpacingRule":"single","listStyle":{"listTypeFormat":"parentheses","size":100},"tagType":"P"},"runs":[{"idx":0,"len":76,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":362,"bottom":78,"pngfb":false,"pr":{"l":"Lib","i":515}}}],"shapemaskId":"","xPos":872,"yPos":248,"tabIndex":36,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":17,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"A máquina aprende com dados rotulados e faz previsões a partir dos exemplos.","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"6RdpVMCHVVu","variables":[{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpClearStateVars":{"kind":"actiongroup","actions":[]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6LlUBtQwwXI_1543516200","id":"01","linkId":"txt__default_6ODKwJK1xIo","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A máquina aprende padrões em dados não rotulados.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":49,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":337,"bottom":65,"pngfb":false,"pr":{"l":"Lib","i":516}}}],"shapemaskId":"","xPos":872,"yPos":336,"tabIndex":39,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":18,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"A máquina aprende padrões em dados não rotulados.","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"6ODKwJK1xIo","variables":[{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpClearStateVars":{"kind":"actiongroup","actions":[]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6QZ1cvbl4QF_-1994229186","id":"01","linkId":"txt__default_6CjCtsJG2FJ","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":348,"height":68,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A máquina aprende por tentativa e erro, recebendo recompensas ou punições.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":74,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":352,"bottom":78,"pngfb":false,"pr":{"l":"Lib","i":517}}}],"shapemaskId":"","xPos":872,"yPos":424,"tabIndex":42,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":184,"rotateYPos":39,"scaleX":100,"scaleY":100,"alpha":100,"depth":19,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":369,"bottom":79,"altText":"A máquina aprende por tentativa e erro, recebendo recompensas ou punições.","pngfb":false,"pr":{"l":"Lib","i":508}},"html5data":{"xPos":-2,"yPos":-2,"width":371,"height":81,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":368,"height":78,"resume":true,"useHandCursor":true,"id":"6CjCtsJG2FJ","variables":[{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpClearStateVars":{"kind":"actiongroup","actions":[]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5UnJoTf42H8_-363439797","id":"01","linkId":"txt__default_5UnJoTf42H8","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1124,"height":66,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Relacione os tipos de aprendizagem com as suas descrições:","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":72,"trailingMargin":0,"firstLineMargin":36,"justification":"left","defaultTabStop":85.333,"listLevel":1,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":58,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":595,"bottom":52,"pngfb":false,"pr":{"l":"Lib","i":518}}}],"shapemaskId":"","xPos":96,"yPos":72,"tabIndex":32,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":572,"rotateYPos":38,"scaleX":100,"scaleY":100,"alpha":100,"depth":20,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1144,"bottom":76,"altText":"Relacione os tipos de aprendizagem com as suas descrições:","pngfb":false,"pr":{"l":"Lib","i":314}},"html5data":{"xPos":-1,"yPos":-1,"width":1145,"height":77,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1144,"height":76,"resume":true,"useHandCursor":true,"id":"5UnJoTf42H8"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"65MX7lYtozV_-1710030491","id":"01","linkId":"txt__default_65MX7lYtozV","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":132,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"09/10","style":{"fontSize":14,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":5,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":62,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":519}}}],"shapemaskId":"","xPos":1072,"yPos":672,"tabIndex":49,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":76,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":21,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":152,"bottom":33,"altText":"09/10","pngfb":false,"pr":{"l":"Lib","i":70}},"html5data":{"xPos":0,"yPos":0,"width":152,"height":33,"strokewidth":0}},"width":152,"height":33,"resume":true,"useHandCursor":true,"id":"65MX7lYtozV"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6NuNoX2kgNt_-1629262138","id":"01","linkId":"txt__default_6NuNoX2kgNt","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":529,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"          (Arraste as caixas para os espaços e clique em “submeter”)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":68,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":536,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":521}}}],"shapemaskId":"","xPos":366,"yPos":672,"tabIndex":44,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":274.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":22,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":549,"bottom":40,"altText":"          (Arraste as caixas para os espaços e clique em “submeter”)","pngfb":false,"pr":{"l":"Lib","i":520}},"html5data":{"xPos":-1,"yPos":-1,"width":550,"height":41,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":549,"height":40,"resume":true,"useHandCursor":true,"id":"6NuNoX2kgNt"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6iDllDRJgWM_-1498561952","id":"01","linkId":"txt__default_5hI0znHlwXe","type":"acctext","xPos":4,"yPos":2,"xAccOffset":4,"yAccOffset":2,"width":216,"height":44,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Submeter","style":{"fontSize":16,"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"justification":"center","tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18.667,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":162,"bottom":42,"pngfb":false,"pr":{"l":"Lib","i":329}}}],"shapemaskId":"","xPos":544,"yPos":600,"tabIndex":43,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":112,"rotateYPos":24,"scaleX":100,"scaleY":100,"alpha":100,"depth":23,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":225,"bottom":49,"altText":"Submeter","pngfb":false,"pr":{"l":"Lib","i":327}},"html5data":{"xPos":-2,"yPos":-2,"width":227,"height":51,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":225,"bottom":49,"altText":"Submeter","pngfb":false,"pr":{"l":"Lib","i":328}},"html5data":{"xPos":-2,"yPos":-2,"width":227,"height":51,"strokewidth":1}}}],"width":224,"height":48,"resume":true,"useHandCursor":true,"id":"5hI0znHlwXe","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"if_action","condition":{"statement":{"kind":"or","statements":[{"kind":"compare","operator":"eq","valuea":"_parent.5lCisSsiBPi.#ItemWasDragged","typea":"var","valueb":true,"typeb":"boolean"},{"kind":"compare","operator":"eq","valuea":"_parent.5tDVtCrHp0x.#ItemWasDragged","typea":"var","valueb":true,"typeb":"boolean"},{"kind":"compare","operator":"eq","valuea":"_parent.5h0EEFoTKIg.#ItemWasDragged","typea":"var","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"eval_interaction","id":"_parent.6RceaJBKEsR"},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6H5aWIkudW5","typea":"string","valueb":"_parent.5lCisSsiBPi.$DropTargetId","typeb":"property"}}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"5VGIHdXgYQp","typea":"string","valueb":"_parent.5tDVtCrHp0x.$DropTargetId","typeb":"property"}}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6AXLpbAqeVh","typea":"string","valueb":"_parent.5h0EEFoTKIg.$DropTargetId","typeb":"property"}}}],"elseActions":[{"kind":"gotoplay","window":"MessageWnd","wndtype":"normal","objRef":{"type":"string","value":"_player.MsgScene_6qX7xzvLyR5.InvalidPromptSlide"}}]}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5cTltgLBB40_428073250","id":"01","linkId":"txt__default_5cTltgLBB40","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: FUNCIONAMENTO DA IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":23,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":242,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":364}}}],"shapemaskId":"","xPos":128,"yPos":8,"tabIndex":28,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":24,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: FUNCIONAMENTO DA IA","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":true,"useHandCursor":true,"id":"5cTltgLBB40"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":112,"yPos":8,"tabIndex":25,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":579,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":25,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1158,"bottom":40,"altText":"Rectangle 20","pngfb":false,"pr":{"l":"Lib","i":14}},"html5data":{"xPos":-1,"yPos":-1,"width":1159,"height":41,"strokewidth":0}},"width":1158,"height":40,"resume":true,"useHandCursor":true,"id":"5zRATC8eWLk"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"68AMVtHDeON_1830503633","id":"01","linkId":"txt__default_68AMVtHDeON","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: FUNCIONAMENTO DA IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":23,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":242,"bottom":27,"pngfb":false,"pr":{"l":"Lib","i":506}}}],"shapemaskId":"","xPos":128,"yPos":12,"tabIndex":30,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":26,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: FUNCIONAMENTO DA IA","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":true,"useHandCursor":true,"id":"68AMVtHDeON"}],"startTime":-1,"elapsedTimeMode":"normal","useHandCursor":false,"resume":true,"kind":"slidelayer","isBaseLayer":true},{"kind":"slidelayer","depth":0,"modal":true,"pauseParent":true,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6AKHv7OcAvI"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5cYfrO9wZyg"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5m0lTehwM6P"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6B1OxaKchgB"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6qfZCson6Yk_1729001594","id":"01","linkId":"txt__default_6AKHv7OcAvI","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"6eNKPDFh30D_1059896074","id":"02","linkId":"txt__default_Disabled_6AKHv7OcAvI","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":17,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6AKHv7OcAvI","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5ZcIVIKkL4Z.6piZSqe604c"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5j3MsuzFZ5t_-1730058550","id":"01","linkId":"txt__default_5cYfrO9wZyg","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"606zow2LP1C_1953149285","id":"02","linkId":"txt__default_Disabled_5cYfrO9wZyg","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":18,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":23.52,"descent":5.64,"leading":0,"underlinePosition":-1.224,"underlineThickness":1.176,"xHeight":13.2}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":109,"bottom":35,"pngfb":false,"pr":{"l":"Lib","i":339}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":16,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":130,"bottom":40,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":336}},"html5data":{"xPos":-1,"yPos":-1,"width":131,"height":41,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":337}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":338}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":337}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"5cYfrO9wZyg","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":576,"tabIndex":14,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":35.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":73,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":355}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":74,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":72,"resume":true,"useHandCursor":true,"id":"5m0lTehwM6P"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6B1OxaKchgB_-1924023101","id":"01","linkId":"txt__default_6B1OxaKchgB","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":473,"height":26,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Incorrecto! Não fez a correspondência correcta.","style":{"fontSize":16,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","justification":"center","defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":47,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":466,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":522}}}],"shapemaskId":"","xPos":376,"yPos":592,"tabIndex":15,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":246.5,"rotateYPos":18,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":493,"bottom":36,"altText":"Incorrecto! Não fez a correspondência correcta.","pngfb":false,"pr":{"l":"Lib","i":356}},"html5data":{"xPos":0,"yPos":0,"width":493,"height":36,"strokewidth":0}},"width":493,"height":36,"resume":true,"useHandCursor":true,"id":"6B1OxaKchgB"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5eOadcfqXA4","events":[{"kind":"ontopmostlayer","actions":[{"kind":"setactivetimeline","objRef":{"type":"string","value":"_this"}}]},{"kind":"ontransitionin","actions":[{"kind":"enable_frame_control","name":"submit","enable":false}]},{"kind":"ontransitionout","actions":[{"kind":"enable_frame_control","name":"submit","enable":true}]},{"kind":"ontransitionincomplete","actions":[{"kind":"setfocus","showrect":false,"value":{"type":"string","value":"5m0lTehwM6P"}}]}]},{"kind":"slidelayer","depth":0,"modal":true,"pauseParent":true,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5bYwnLMN9SD"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"66UTQsqSlSo"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5pc7Duyfvzq"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6AMi8IkXPx2"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":576,"tabIndex":18,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":35.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":73,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":340}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":74,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":72,"resume":true,"useHandCursor":true,"id":"5bYwnLMN9SD"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"66UTQsqSlSo_1926993700","id":"01","linkId":"txt__default_66UTQsqSlSo","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":511,"height":26,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Correcto! Fez a correspondência correcta.","style":{"fontSize":16,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","justification":"center","defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":41,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":24.889,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":457,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":523}}}],"shapemaskId":"","xPos":360,"yPos":592,"tabIndex":19,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":265.5,"rotateYPos":18,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":531,"bottom":36,"altText":"Correcto! Fez a correspondência correcta.","pngfb":false,"pr":{"l":"Lib","i":341}},"html5data":{"xPos":0,"yPos":0,"width":531,"height":36,"strokewidth":0}},"width":531,"height":36,"resume":true,"useHandCursor":true,"id":"66UTQsqSlSo"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6o0P4kRh2Q1_-911351682","id":"01","linkId":"txt__default_5pc7Duyfvzq","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"6ltsBLLWP8r_-820903840","id":"02","linkId":"txt__default_Disabled_5pc7Duyfvzq","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":21,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"5pc7Duyfvzq","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5ZcIVIKkL4Z.6piZSqe604c"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5hcRH4jT611_-1673266525","id":"01","linkId":"txt__default_6AMi8IkXPx2","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6XV6untL7QV_-2078370847","id":"02","linkId":"txt__default_Disabled_6AMi8IkXPx2","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":18,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":23.52,"descent":5.64,"leading":0,"underlinePosition":-1.224,"underlineThickness":1.176,"xHeight":13.2}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":109,"bottom":35,"pngfb":false,"pr":{"l":"Lib","i":339}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":20,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":130,"bottom":40,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":336}},"html5data":{"xPos":-1,"yPos":-1,"width":131,"height":41,"strokewidth":0}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":337}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":338}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":337}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6AMi8IkXPx2","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5lKRJVJEafV","events":[{"kind":"onslidestart","actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"eq","valuea":"_player.#Unidade2_False","typea":"var","valueb":false,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"adjustvar","variable":"_player.nota","operator":"add","value":{"type":"number","value":1}}]},{"kind":"adjustvar","variable":"_player.Unidade2_False","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"ontopmostlayer","actions":[{"kind":"setactivetimeline","objRef":{"type":"string","value":"_this"}}]},{"kind":"ontransitionin","actions":[{"kind":"enable_frame_control","name":"submit","enable":false}]},{"kind":"ontransitionout","actions":[{"kind":"enable_frame_control","name":"submit","enable":true}]},{"kind":"ontransitionincomplete","actions":[{"kind":"setfocus","showrect":false,"value":{"type":"string","value":"5bYwnLMN9SD"}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":39500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tan86yaQfz"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VCYeYUEni1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69s3gqNHg1Y"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ayzt09NtEH"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5whFHQDPFcs"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6jXPK0rdGU9"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lVfqqZUc0d"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VHGXGQPaHr"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6dIY6ufVyqZ"}}]},{"kind":"ontimelinetick","time":7979,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"65Z2OtQpB8s"}}]},{"kind":"ontimelinetick","time":12750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6a0vWVXybMh"}}]},{"kind":"ontimelinetick","time":19000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5l4SITyHfG9"}}]},{"kind":"ontimelinetick","time":26000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5r3n1dkaOym"}}]},{"kind":"ontimelinetick","time":31500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ht0I6wfKd3"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":0,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"5tan86yaQfz"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":72,"tabIndex":1,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":291.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":585,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":29}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":586,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":584,"resume":true,"useHandCursor":true,"id":"5ayzt09NtEH"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":2,"id":"01","url":"story_content/6Q70oTwPFfL_RC36618.png","type":"normal","altText":"target-audience.png","width":512,"height":512,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":80,"yPos":192,"tabIndex":4,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":160,"rotateYPos":160,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":320,"bottom":320,"altText":"target-audience.png","pngfb":false,"pr":{"l":"Lib","i":30}},"html5data":{"xPos":0,"yPos":0,"width":320,"height":320,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":320,"height":320,"resume":true,"useHandCursor":true,"id":"6VCYeYUEni1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69s3gqNHg1Y_-519312530","id":"01","linkId":"txt__default_69s3gqNHg1Y","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":652,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"OBJECTIVOS DO CURSO","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"linkColor":"#1888E7","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":452,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":32}}}],"shapemaskId":"","xPos":504,"yPos":104,"tabIndex":3,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":336,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":672,"bottom":40,"altText":"OBJECTIVOS DO CURSO","pngfb":false,"pr":{"l":"Lib","i":31}},"html5data":{"xPos":0,"yPos":0,"width":672,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":672,"height":40,"resume":true,"useHandCursor":true,"id":"69s3gqNHg1Y"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":2,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5lVfqqZUc0d","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_5whFHQDPFcs","type":"hiddentext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":756,"height":374,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":5,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":776,"bottom":384,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\nConhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\nIdentificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\nEntender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\nReconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\nFamiliarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\nExplorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\nCompreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","pngfb":false,"pr":{"l":"Lib","i":35}},"html5data":{"xPos":0,"yPos":0,"width":776,"height":384,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":true,"useHandCursor":true,"id":"5whFHQDPFcs"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5ht0I6wfKd3","id":"01","linkId":"txt_5ht0I6wfKd3","type":"acctext","xPos":10,"yPos":330,"xAccOffset":10,"yAccOffset":330,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":694,"bottom":376,"pngfb":false,"pr":{"l":"Lib","i":37}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":56,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":694,"bottom":376,"altText":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":694,"height":376,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5ht0I6wfKd3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5r3n1dkaOym","id":"01","linkId":"txt_5r3n1dkaOym","type":"acctext","xPos":10,"yPos":273,"xAccOffset":10,"yAccOffset":273,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":746,"bottom":319,"pngfb":false,"pr":{"l":"Lib","i":38}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":57,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":746,"bottom":319,"altText":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":746,"height":319,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5r3n1dkaOym"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5l4SITyHfG9","id":"01","linkId":"txt_5l4SITyHfG9","type":"acctext","xPos":10,"yPos":238,"xAccOffset":10,"yAccOffset":238,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":700,"bottom":261,"pngfb":false,"pr":{"l":"Lib","i":39}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":58,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":700,"bottom":261,"altText":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":700,"height":261,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5l4SITyHfG9"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6a0vWVXybMh","id":"01","linkId":"txt_6a0vWVXybMh","type":"acctext","xPos":10,"yPos":181,"xAccOffset":10,"yAccOffset":181,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":671,"bottom":227,"pngfb":false,"pr":{"l":"Lib","i":40}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":59,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":671,"bottom":227,"altText":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":671,"height":227,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6a0vWVXybMh"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_65Z2OtQpB8s","id":"01","linkId":"txt_65Z2OtQpB8s","type":"acctext","xPos":10,"yPos":123,"xAccOffset":10,"yAccOffset":123,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":742,"bottom":169,"pngfb":false,"pr":{"l":"Lib","i":41}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":60,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":742,"bottom":169,"altText":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":742,"height":169,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"65Z2OtQpB8s"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6dIY6ufVyqZ","id":"01","linkId":"txt_6dIY6ufVyqZ","type":"acctext","xPos":10,"yPos":66,"xAccOffset":10,"yAccOffset":66,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":714,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":42}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":61,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":714,"bottom":112,"altText":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":714,"height":112,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6dIY6ufVyqZ"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6VHGXGQPaHr","id":"01","linkId":"txt_6VHGXGQPaHr","type":"acctext","xPos":10,"yPos":9,"xAccOffset":10,"yAccOffset":9,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":705,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":43}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":62,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":705,"bottom":54,"altText":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":705,"height":54,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6VHGXGQPaHr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6jXPK0rdGU9","id":"01","linkId":"txt_6jXPK0rdGU9","type":"acctext","xPos":10,"yPos":-26,"xAccOffset":10,"yAccOffset":-26,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":-27,"right":610,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":44}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":63,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":-27,"right":610,"bottom":0,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":-27,"width":610,"height":27,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6jXPK0rdGU9"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6QtAxFNOoxm"},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TRpicPw6tU"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VcGQGd6sQ4"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6HniC2d4w8h"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nw6rMDcq88"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5wH7XkMdNj1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fLNGDgml4K"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5a1AGGT21Cq"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ex489sIWwL"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":6,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6TRpicPw6tU"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":64,"tabIndex":7,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":279.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":561,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":45}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":562,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":560,"resume":true,"useHandCursor":true,"id":"5VcGQGd6sQ4"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":3,"id":"01","url":"story_content/6S7pEErDZEG.png","type":"normal","altText":"Agrupar 1.png","width":959,"height":657,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":1,"yPos":80,"tabIndex":8,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":391,"rotateYPos":268,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":782,"bottom":536,"altText":"Agrupar 1.png","pngfb":false,"pr":{"l":"Lib","i":46}},"html5data":{"xPos":0,"yPos":0,"width":782,"height":536,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":782,"height":536,"resume":true,"useHandCursor":true,"id":"6HniC2d4w8h"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":16,"yPos":94,"tabIndex":10,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":40,"rotateYPos":18.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":80,"bottom":37,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":47}},"html5data":{"xPos":0,"yPos":0,"width":80,"height":37,"strokewidth":0}},"width":80,"height":37,"resume":true,"useHandCursor":true,"id":"5nw6rMDcq88"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":9,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5wH7XkMdNj1","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6fLNGDgml4K_1529763356","id":"01","linkId":"txt__default_6fLNGDgml4K","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":308,"height":190,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Play/Pause\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Timeline\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reiniciar ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Volume\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Voltar ao menu\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Objectivos do curso","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":191,"bottom":193,"pngfb":false,"pr":{"l":"Lib","i":49}}}],"shapemaskId":"","xPos":24,"yPos":128,"tabIndex":11,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":164,"rotateYPos":100,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":328,"bottom":200,"altText":"Play/Pause\\nTimeline\\nReiniciar ecrã\\nVolume\\nVoltar ao menu\\nObjectivos do curso","pngfb":false,"pr":{"l":"Lib","i":48}},"html5data":{"xPos":0,"yPos":0,"width":328,"height":200,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":328,"height":200,"resume":true,"useHandCursor":true,"id":"6fLNGDgml4K"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5a1AGGT21Cq_1013326046","id":"01","linkId":"txt__default_5a1AGGT21Cq","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":420,"height":222,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"7.     Instruções de navegação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"8.      A sua pontuação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"9.      Instruções de interacções no ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"10.   Notas/curiosidade\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"11.   Voltar ao ecrã anterior\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":30,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"12.   Paginação \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":17,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"13.   Avançar para o próximo ecrã","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":33,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":225,"pngfb":false,"pr":{"l":"Lib","i":51}}}],"shapemaskId":"","xPos":320,"yPos":128,"tabIndex":12,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":220,"rotateYPos":116,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":440,"bottom":232,"altText":"7.     Instruções de navegação\\n8.      A sua pontuação\\n9.      Instruções de interacções no ecrã\\n10.   Notas/curiosidade\\n11.   Voltar ao ecrã anterior\\n12.   Paginação \\n13.   Avançar para o próximo ecrã","pngfb":false,"pr":{"l":"Lib","i":50}},"html5data":{"xPos":0,"yPos":0,"width":440,"height":232,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":440,"height":232,"resume":true,"useHandCursor":true,"id":"5a1AGGT21Cq"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6ex489sIWwL_1502302535","id":"01","linkId":"txt__default_6ex489sIWwL","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":428,"height":363,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":136,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Ao finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":133,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":153,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":435,"bottom":368,"pngfb":false,"pr":{"l":"Lib","i":53}}}],"shapemaskId":"","xPos":808,"yPos":168,"tabIndex":13,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":224,"rotateYPos":186.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":448,"bottom":373,"altText":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\nAo finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\nA pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","pngfb":false,"pr":{"l":"Lib","i":52}},"html5data":{"xPos":0,"yPos":0,"width":448,"height":373,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":448,"height":373,"resume":true,"useHandCursor":true,"id":"6ex489sIWwL"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5Xlh34Ekb5w"}],"showAnimationId":"","lmsId":"Slide9","width":1280,"height":720,"resume":true,"background":{"type":"fill","fill":{"type":"linear","rotation":90,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}},"id":"63EypKTdQuR","actionGroups":{"ReviewInt_6OHjx363Ipm":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5lCisSsiBPi"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5tDVtCrHp0x"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5h0EEFoTKIg"},"enabled":{"type":"boolean","value":false}}]},"ReviewIntCorrectIncorrect_6OHjx363Ipm":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5lCisSsiBPi"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5tDVtCrHp0x"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5h0EEFoTKIg"},"enabled":{"type":"boolean","value":false}}]},"AnsweredInt_6OHjx363Ipm":{"kind":"actiongroup","actions":[{"kind":"exe_actiongroup","id":"DisableChoices_6OHjx363Ipm"},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"pxabnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"pxabnsnfns10110000101"}]}]},"DisableChoices_6OHjx363Ipm":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5lCisSsiBPi"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5tDVtCrHp0x"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"5h0EEFoTKIg"},"enabled":{"type":"boolean","value":false}}]},"6OHjx363Ipm_CheckAnswered":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"or","statements":[{"kind":"compare","operator":"eq","valuea":"6RceaJBKEsR.$Status","typea":"property","valueb":"correct","typeb":"string"}]}},"thenActions":[{"kind":"exe_actiongroup","id":"AnsweredInt_6OHjx363Ipm"}],"elseActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6RceaJBKEsR.$Status","typea":"property","valueb":"incorrect","typeb":"string"}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"gte","valuea":"6RceaJBKEsR.$AttemptCount","typea":"property","valueb":1,"typeb":"number"}},"thenActions":[{"kind":"exe_actiongroup","id":"AnsweredInt_6OHjx363Ipm"}]}]}]}]},"CloseFeedback":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.5y4R5TkhW1I.$QuizComplete","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"5lKRJVJEafV"}},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"5eOadcfqXA4"}}]}]},"NavigationRestrictionNextSlide_63EypKTdQuR":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6RceaJBKEsR.$Answered","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6RceaJBKEsR.$Status","typea":"property","valueb":"correct","typeb":"string"}},"thenActions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6piZSqe604c"}}],"elseActions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6piZSqe604c"}}]}],"elseActions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6piZSqe604c"}}]}]},"NavigationRestrictionPreviousSlide_63EypKTdQuR":{"kind":"actiongroup","actions":[{"kind":"history_prev"}]}},"events":[{"kind":"onslidestart","actions":[{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"adjustvar","variable":"_player.Unidade2FALSE","operator":"set","value":{"type":"boolean","value":true}}]},{"kind":"onbeforeslidein","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"npnxnanbnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"npnxnanbnsnfns10110000101"}]}]},{"kind":"ontransitionin","actions":[{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"set","value":{"type":"string","value":"_player."}},{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"add","value":{"type":"property","value":"$AbsoluteId"}},{"kind":"exe_actiongroup","id":"6OHjx363Ipm_CheckAnswered"},{"kind":"exe_actiongroup","id":"CloseFeedback"}]},{"kind":"onnextslide","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#ReviewMode_6OHjx363Ipm","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6piZSqe604c"}}],"elseActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#RetryMode_6OHjx363Ipm","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6piZSqe604c"}}],"elseActions":[{"kind":"exe_actiongroup","id":"NavigationRestrictionNextSlide_63EypKTdQuR"}]}]}]},{"kind":"onprevslide","actions":[{"kind":"exe_actiongroup","id":"NavigationRestrictionPreviousSlide_63EypKTdQuR"}]}]}');