@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* e_curso Design System - ENSA Colors */
    --background: 0 0% 100%;
    --foreground: 225 50% 15%;

    --card: 0 0% 100%;
    --card-foreground: 225 50% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 225 50% 15%;

    /* Primary: ENSA Red */
    --primary: 355 95% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 355 95% 55%;

    /* Secondary: ENSA Blue */
    --secondary: 237 48% 30%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 15% 95%;
    --muted-foreground: 225 25% 45%;

    --accent: 237 48% 30%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 15% 90%;
    --input: 220 15% 95%;
    --ring: 355 95% 45%;

    /* Success color */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    /* Warning color */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(355 95% 45%), hsl(355 95% 55%));
    --gradient-secondary: linear-gradient(135deg, hsl(237 48% 30%), hsl(237 48% 40%));
    --gradient-hero: linear-gradient(135deg, hsl(237 48% 30%) 0%, hsl(355 95% 45%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(220 15% 98%) 100%);

    /* Shadows */
    --shadow-primary: 0 10px 30px -10px hsl(355 95% 45% / 0.3);
    --shadow-secondary: 0 10px 30px -10px hsl(237 48% 30% / 0.2);
    --shadow-card: 0 4px 20px -8px hsl(225 25% 45% / 0.1);

    --radius: 0.75rem;

    /* Animations and transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode colors using same ENSA palette */
    --background: 225 50% 8%;
    --foreground: 0 0% 95%;

    --card: 225 50% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 225 50% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 355 95% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 355 95% 60%;

    --secondary: 237 48% 35%;
    --secondary-foreground: 0 0% 100%;

    --muted: 225 25% 15%;
    --muted-foreground: 225 15% 65%;

    --accent: 237 48% 35%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62% 45%;
    --destructive-foreground: 0 0% 100%;

    --border: 225 25% 20%;
    --input: 225 25% 15%;
    --ring: 355 95% 50%;

    --success: 142 76% 40%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 55%;
    --warning-foreground: 0 0% 100%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(355 95% 50%), hsl(355 95% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(237 48% 35%), hsl(237 48% 45%));
    --gradient-hero: linear-gradient(135deg, hsl(225 50% 8%) 0%, hsl(237 48% 35%) 50%, hsl(355 95% 50%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(225 50% 10%) 0%, hsl(225 25% 12%) 100%);

    --shadow-primary: 0 10px 30px -10px hsl(355 95% 50% / 0.4);
    --shadow-secondary: 0 10px 30px -10px hsl(237 48% 35% / 0.3);
    --shadow-card: 0 4px 20px -8px hsl(0 0% 0% / 0.3);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes for e_curso */
@layer utilities {
  .gradient-text {
    @apply bg-gradient-primary bg-clip-text text-transparent;
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Animações melhoradas */
  .animate-fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    @apply shadow-primary;
  }

  /* Loading states */
  .loading-pulse {
    @apply animate-pulse bg-muted rounded;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  /* Responsive utilities */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Card improvements */
  .card-interactive {
    @apply hover-lift cursor-pointer transition-all duration-300 hover:shadow-card;
  }

  .card-gradient {
    @apply bg-gradient-card border-0 shadow-card;
  }

  /* Button improvements */
  .btn-primary-enhanced {
    @apply bg-gradient-primary hover:shadow-primary transition-all duration-300 transform hover:scale-105;
  }

  .btn-secondary-enhanced {
    @apply bg-gradient-secondary hover:shadow-secondary transition-all duration-300 transform hover:scale-105;
  }

  /* Keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes bounceIn {
    from {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Focus improvements */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }
}