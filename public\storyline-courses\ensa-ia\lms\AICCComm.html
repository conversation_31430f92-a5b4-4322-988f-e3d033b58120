<html>
<head>

<script language="JavaScript">
    function WriteToDebug(str){
        window.parent.WriteToDebug("AICCComm - " + str);
    }

    function DisplayError(strMessage){
        window.parent.DisplayError(strMessage);
    }

    var objXMLHTTP = null;
    var objXDomainReq = null;

    if (typeof XDomainRequest !== "undefined") {
        objXDomainReq = new XDomainRequest();
    }

    if(typeof XMLHttpRequest !== "undefined"){
        objXMLHTTP = new XMLHttpRequest();
    }

//variables to check for the IFrame's load
var intReCheckLoadedInterval = window.parent.AICC_RE_CHECK_LOADED_INTERVAL;
var intReCheckAttemptsBeforeTimeout = window.parent.AICC_RE_CHECK_ATTEMPTS_BEFORE_TIMEOUT;
var intReCheckAttempts = 0;

var IFrameLoaded_TimeOutID = "";
var blnIFrameLoaded = false;

//AICC Data - content will set these variables (through public methods) for submission to the LMS
var strLessonLocation = "";
var strLessonStatus = "i";
var strScore = "";
var strTime = "00:00:00";

var lastPostType = {};

//Communication Capabilities of the current browser - we check the actual capabilities
//rather than specific browsers to ensure forward compatibility and compatibility with untested browsers
var blnCanUseXMLHTTP;       //set in the onload event
var blnCanUseIFrame;
var blnXMLHTTPIsAvailable;      //determines if the browser supports the XmlHttp object
var blnCanUseSyncXHR;

var blnAppearsToBeCrossDomain;

//constants
var REQUEST_TYPE_GET = "GETPARAM";
var REQUEST_TYPE_PUT = "PUTPARAM";
var REQUEST_TYPE_PUT_INTERACTIONS = "PUTINTERACTIONS";
var REQUEST_TYPE_EXIT = "EXITAU";



WriteToDebug("intReCheckLoadedInterval=" + intReCheckLoadedInterval);
WriteToDebug("intReCheckAttemptsBeforeTimeout=" + intReCheckAttemptsBeforeTimeout);

//---------------------------------------------------------------------
//Public Functions
//---------------------------------------------------------------------


function MakeGetParamRequest(){

    var strAICCSID;
    var strAICCURL;

    WriteToDebug ("In MakeGetParamRequest");

    strAICCSID = GetAICCSID();
    strAICCURL = GetAICCURL();

    WriteToDebug ("Submitting Form");

    SubmitForm(strAICCURL, strAICCSID, REQUEST_TYPE_GET, "");

}


function MakePutParamRequest(strAICCData){

    var strAICCSID;
    var strAICCURL;
    var strAICCData;

    WriteToDebug ("In MakePutParamRequest");

    if (parent.blnReviewModeSoReadOnly){
        WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting.");
        return true;
    }

    strAICCSID = GetAICCSID();
    strAICCURL = GetAICCURL();

    WriteToDebug ("Submitting Form");

    SubmitForm(strAICCURL, strAICCSID, REQUEST_TYPE_PUT, strAICCData);

}


function MakePutInteractionsRequest(strAICCData){

    var strAICCSID;
    var strAICCURL;
    var strAICCData;

    WriteToDebug ("In MakePutInteractionsRequest");

    if (parent.blnReviewModeSoReadOnly){
        WriteToDebug("Mode is Review and configuration setting dictates this should be read only so exiting.");
        return true;
    }

    strAICCSID = GetAICCSID();
    strAICCURL = GetAICCURL();

    WriteToDebug ("Submitting Form");

    SubmitForm(strAICCURL, strAICCSID, REQUEST_TYPE_PUT_INTERACTIONS, strAICCData);

}

function MakeExitAURequest(){

    var strAICCSID;
    var strAICCURL;

    WriteToDebug ("In MakeExitAURequest");

    strAICCSID = GetAICCSID();
    strAICCURL = GetAICCURL();

    WriteToDebug ("Submitting Form");

    SubmitForm(strAICCURL, strAICCSID, REQUEST_TYPE_EXIT, "");

}






//---------------------------------------------------------------------
//Private Functions
//---------------------------------------------------------------------


//---------------------------------------------------------------------
//IFrame Functions
//---------------------------------------------------------------------


function CheckIFrameLoaded(strRequestType){

    WriteToDebug("In CheckIFrameLoaded strRequestType=" + strRequestType);

    if (blnIFrameLoaded){

        WriteToDebug("Frame Loaded");

        ProcessLMSResult(strRequestType, GetIFrameContents());

    }
    else{
        //re-call, check for time out

        WriteToDebug("Frame Not Loaded");

        intReCheckAttempts ++;

        if (intReCheckAttempts > intReCheckAttemptsBeforeTimeout){

            WriteToDebug("Frame Timeout Error");

            parent.InitializeExecuted(false, "The LMS timed out while responding to an AICC request.");
        }
        else{
            WriteToDebug("Resetting CheckIFrameLoaded");
            IFrameLoaded_TimeOutID = window.setTimeout("CheckIFrameLoaded('" + strRequestType + "')", intReCheckLoadedInterval);
        }

    }
}


function IFrameLoaded(){

    WriteToDebug(" IFrameLoaded ");

    blnIFrameLoaded = true;

}


function GetIFrameContents(){

    var strContents;

    WriteToDebug("In GetIFrameContents");

    try{
        strContents = window.AICCFrame.document.body.innerHTML;
    }
    catch (e){
        WriteToDebug("Error submitting form via IFrame, falling back to normal form post and returning ''. Error=" + ((e.message)?e.message:e.toString()) );
        blnCanUseIFrame = false;
        strContents = "";
    }

    WriteToDebug("strContents=" + strContents);

    return strContents;
}


function SubmitFormUsingIFrame(strAICCURL, strAICCSID, strRequestType, strAICCData){

    WriteToDebug ("In SubmitFormUsingIFrame, setting fields");

    document.frmAICC.action = strAICCURL;
    document.frmAICC.session_id.value = strAICCSID;

    document.frmAICC.command.value = URLEncode(strRequestType);
    document.frmAICC.aicc_data.value = URLEncode(strAICCData);
    // added SD 3.8.6 (JBR) - check to see if we have version from LMS already
    if(window.parent.AICC_LMS_Version!=""){
        //check to see if we already have a version from the LMS cached
        document.frmAICC.version.value = URLEncode(window.parent.AICC_LMS_Version);
    }else{
        //default to 3.5
        document.frmAICC.version.value = "3.5";
    }

    WriteToDebug ("Submitting Form");

    document.frmAICC.submit();

    blnIFrameLoaded = false;
    intReCheckAttempts = 0;

    WriteToDebug ("Clearing Timeout");

    if (IFrameLoaded_TimeOutID != ""){
        window.clearTimeout(IFrameLoaded_TimeOutID);
        IFrameLoaded_TimeOutID = "";
    }

    CheckIFrameLoaded(strRequestType);
}

//---------------------------------------------------------------------
//XDomainRequest  Object Functions
//---------------------------------------------------------------------
function SubmitFormUsingXDomainRequest(strAICCURL, strAICCSID, strRequestType, strAICCData){

    var strReturn;
    var strPostData;

    WriteToDebug ("In SubmitFormUsingXDomainRequest, opening connetion");

    objXDomainReq.open ("POST", strAICCURL);

    WriteToDebug ("Creating Post Data");

    //check to see if we should encode the session_id
    if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
        strPostData = "session_id=" + URLEncode(strAICCSID);
    }else{
        strPostData = "session_id=" + strAICCSID;
    }
    // added SD 3.8.6 (JBR) - check to see if we have version from LMS already
    if(window.parent.AICC_LMS_Version!=""){
        //check to see if we already have a version from the LMS cached
        //check to see if we should encode the version
        if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
            strPostData += "&version="+URLEncode(window.parent.AICC_LMS_Version);
        }else{
            strPostData += "&version="+window.parent.AICC_LMS_Version;
        }
    }else{
        //default to 3.5
        //check to see if we should encode the version
        if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
            strPostData += "&version="+URLEncode("3.5");
        }else{
            strPostData += "&version=3.5";
        }
    }

    //always encode the command and aicc_data values
    strPostData += "&command=" + URLEncode(strRequestType) + "&aicc_data=" + URLEncode(strAICCData);



    var fakeStatus = null;
    WriteToDebug ("Setting XDR onload");
    objXDomainReq.onload = function () {
        fakeStatus = 200;
    };
    WriteToDebug ("Setting XDR onerror");
    objXDomainReq.onerror = function () {
        fakeStatus = 400;
    };
    WriteToDebug ("Setting XDR ontimeout");
    objXDomainReq.ontimeout = function () {
        fakeStatus = 0;
    };
    WriteToDebug ("Setting XDR onprogress");
    objXDomainReq.onprogress = function () {};
    WriteToDebug ("Setting XDR timeout");
    objXDomainReq.timeout = 0;

    WriteToDebug ("Sending Post Data-" + strPostData);
    objXDomainReq.send(strPostData);

    if (!Date.now) {
        Date.now = function () {
            return +(new Date ());
        };
    }

    until = 10000 + Date.now();
    WriteToDebug("sendRequest - until: " + until   );

    while (Date.now() < until && fakeStatus === null) {
        //log("calling __delay", LOG_SRC);
        aiccDelay();
    }

    if(fakeStatus === 200){

        WriteToDebug ("Looking up Response Text");

        strReturn = objXDomainReq.responseText;

        WriteToDebug ("LMS Response=" + strReturn);

        ProcessLMSResult(strRequestType, strReturn);
    }
    else{
        throw {
            code: fakeStatus,
            mesg: "XDomainRequest Failed"
        };
    }
}

//---------------------------------------------------------------------
//XML HTTP Object Functions
//---------------------------------------------------------------------
var waitingAiccResponse = false;
var aiccQueue = [];
function FormatPostData(strAICCSID, strRequestType, strAICCData) {
    return "session_id=" + URLEncode(strAICCSID) +
        "&version=" + URLEncode(window.parent.AICC_LMS_Version!="" ? window.parent.AICC_LMS_Version : "3.5") +
        "&command=" + URLEncode(strRequestType) +
        "&aicc_data=" + URLEncode(strAICCData)
}

function SubmitFormUsingXMLHTTP(strAICCURL, strAICCSID, strRequestType, strAICCData){
    if (waitingAiccResponse) {
        aiccQueue.push([strAICCURL, strAICCSID, strRequestType, strAICCData]);
        return;
    }
    var strPostData = FormatPostData(strAICCSID, strRequestType, strAICCData);

    WriteToDebug("In SubmitFormUsingXMLHTTP, opening connetion");

    objXMLHTTP.open ("POST", strAICCURL);

    WriteToDebug("Setting Request Header");

    objXMLHTTP.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

    WriteToDebug("Sending Post Data-" + strPostData);

    waitingAiccResponse = true;

    objXMLHTTP.onload = function (e) {
        if (objXMLHTTP.readyState === 4) {
            waitingAiccResponse = false;
            WriteToDebug("LMS Response=" + objXMLHTTP.responseText);
            ProcessLMSResult(strRequestType, objXMLHTTP.responseText);
            SendNextQueue();
        }
    };

    objXMLHTTP.send(strPostData);
}

function SubmitFormUsingSyncXHR(strAICCURL, strAICCSID, strRequestType, strAICCData) {
    var strPostData = FormatPostData(strAICCSID, strRequestType, strAICCData);

    objXMLHTTP.open("POST", strAICCURL, false);

    objXMLHTTP.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

    objXMLHTTP.send(strPostData);

    ProcessLMSResult(strRequestType, objXMLHTTP.responseText);
}

function SendNextQueue() {
    if (aiccQueue.length > 0) {
        var queuedMsg = aiccQueue.shift();
        SubmitFormUsingXMLHTTP(queuedMsg[0], queuedMsg[1], queuedMsg[2], queuedMsg[3]);
    }
}

//---------------------------------------------------------------------
// Custom Comms Object Functions
//---------------------------------------------------------------------
function SubmitFormUsingCustomComms(strAICCURL, strAICCSID, strRequestType, strAICCData){

    var strReturn;
    var strPostData;

    WriteToDebug ("In SubmitFormUsingCustomComms, building request information");

    WriteToDebug ("Creating Post Data");

    //check to see if we should encode the session_id
    if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
        strPostData = "session_id=" + URLEncode(strAICCSID);
    }else{
        strPostData = "session_id=" + strAICCSID;
    }
    // added SD 3.8.6 (JBR) - check to see if we have version from LMS already
    if(window.parent.AICC_LMS_Version!=""){
        //check to see if we already have a version from the LMS cached
        //check to see if we should encode the version
        if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
            strPostData += "&version="+URLEncode(window.parent.AICC_LMS_Version);
        }else{
            strPostData += "&version="+window.parent.AICC_LMS_Version;
        }
    }else{
        //default to 3.5
        //check to see if we should encode the version
        if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
            strPostData += "&version="+URLEncode("3.5");
        }else{
            strPostData += "&version=3.5";
        }
    }

    //always encode the command and aicc_data values
    strPostData += "&command=" + URLEncode(strRequestType) + "&aicc_data=" + URLEncode(strAICCData);

    WriteToDebug ("Sending Post Data-" + strPostData);
    WriteToDebug ("Sending RequestType-" + strRequestType);
        console.log('parent.CustomAICCCommunication='+parent.CustomAICCCommunication);
    if(typeof parent.CustomAICCCommunication == 'function')
    {
        parent.CustomAICCCommunication(strAICCURL, strPostData, strRequestType, 'ProcessLMSResult');
    }else{
        WriteToDebug ("CustomAICCCommunication not found - falling back to normal mode");
        parent.AICC_USE_CUSTOM_COMMS = false;
        SubmitForm(strAICCURL, strAICCSID, strRequestType, strAICCData);
    }

}


function URLEncode(str){

    str = new String(str);

    str = escape(str);
    str = str.replace(/%20/g, "+");

    return str;
}

//---------------------------------------------------------------------
//Blind Form Submit Functions
//---------------------------------------------------------------------

function SubmitFormNormally(strAICCURL, strAICCSID, strRequestType, strAICCData){

    WriteToDebug ("In SubmitFormNormally (BlindPost), setting fields");

    document.frmAICC.target = "rusticisoftware_aicc_results";       //keep this name fairly unique to avoid a potential naming conflict with LMS frames

    document.frmAICC.action = strAICCURL;
    //check to see if we should encode the session_id
    if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
        document.frmAICC.session_id.value = strAICCSID;
    }else{
        document.frmAICC.session_id.value = URLEncode(strAICCSID);
    }

    document.frmAICC.command.value = URLEncode(strRequestType);
    document.frmAICC.aicc_data.value = URLEncode(strAICCData);
    // added SD 3.8.6 (JBR) - check to see if we have version from LMS already
    if(window.parent.AICC_LMS_Version!=""){
        //check to see if we already have a version from the LMS cached
    if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
        document.frmAICC.version.value = URLEncode(window.parent.AICC_LMS_Version);
    }else{
        document.frmAICC.version.value = window.parent.AICC_LMS_Version;
    }
    }else{
        //default to 3.5
        if(window.parent.AICC_ENCODE_PARAMETER_VALUES==true || window.parent.AICC_ENCODE_PARAMETER_VALUES=='true'){
            document.frmAICC.version.value = URLEncode("3.5");
        }else{
            document.frmAICC.version.value = "3.5";
        }
    }
    WriteToDebug ("Submitting Form");

    document.frmAICC.submit();

    ProcessLMSResult(strRequestType, "");
}



//---------------------------------------------------------------------
//Form Submission Functions
//---------------------------------------------------------------------


function DetectPreferredCommMethod(){

    //if we have an XMLHTTP object, use that
    //else, if we can see the IFrame, use that
    //else - use a blind (write only) form submit

    /*
    MR 5/30/07 - Don't do any hard and fast domain checking, we'll simply try the post in a try/catch and check for
    a permission denied error. If the post does not succeed, we'll fall back on a normal form post.
    The rules for cross domain scripting have gotten quite complicated recently, they now relate to
    how the XmlHttp object was created, the browser type/version and the security context of each site.
    */

    //if the domain of the strAICCURL does not match the current domain, then note that cross domain probably won't work

    blnAppearsToBeCrossDomain = false;

    var strContentDomain = window.document.domain;
    var strAICCURL = GetAICCURL();

    var aryUrlParts = strAICCURL.split("/");
    var strLmsDomain;

    var blnCrossDomain = false;

    blnCanUseSyncXHR = /MSIE|Trident/.test(window.navigator.userAgent);

    if (strAICCURL.toLowerCase().indexOf("http://") == 0 || strAICCURL.toLowerCase().indexOf("https://") == 0){
        strLmsDomain = aryUrlParts[2]
    }
    else{
        strLmsDomain = aryUrlParts[0];
    }

    if (strLmsDomain.toLowerCase() != strContentDomain.toLowerCase()){
        //WriteToDebug("LMS and Content Domains don't match, falling back to write-only mode using form submit. strLmsDomain=" + strLmsDomain + ", strContentDomain=" + strContentDomain);
        //blnCanUseXMLHTTP = false;
        //blnCanUseIFrame = false;
        //blnCrossDomain = true;

        blnAppearsToBeCrossDomain = true;
    }


    if (objXMLHTTP != null){
        blnXMLHTTPIsAvailable = true;
    }
    else{
        blnXMLHTTPIsAvailable = false;
    }

    //if (!blnCrossDomain)
    //{
        if (parent.AICC_COMM_DISABLE_XMLHTTP)
        {
            WriteToDebug("In DetectPreferredCommMethod, config override of XMLHTTP to false");
            blnCanUseXMLHTTP = false;
        }
        else
        {
            WriteToDebug("In DetectPreferredCommMethod, checking XMLHTTP");
            if (objXMLHTTP != null){
                blnCanUseXMLHTTP = true;
            }
            else{
                blnCanUseXMLHTTP = false;
            }
        }

        if (parent.AICC_COMM_DISABLE_IFRAME)
        {
            WriteToDebug("In DetectPreferredCommMethod, config override of IFRAME to false");
            blnCanUseIFrame = false;
        }
        else
        {
            //note use of short circuit AND to prevent error if browser doesn't recognize part of the IFrame
            //in Opera 7, there needs to be something in the body of the IFrame for the last condition to evaluate to true
            //in opera 7.1, all tests will pass, but the onload event doesn not fire due to a bug, add a check for blnIFrameLoaded
            //to ensure that the onload event fired
            WriteToDebug("Checking IFrame");
            if (window.AICCFrame &&
                window.AICCFrame.document &&
                window.AICCFrame.document.body &&
                window.AICCFrame.document.body.innerHTML &&
                blnIFrameLoaded){
                blnCanUseIFrame = true;
            }
            else{
                blnCanUseIFrame = false;
            }
        }
    //}

    WriteToDebug("blnCanUseXMLHTTP=" + blnCanUseXMLHTTP);
    WriteToDebug("blnCanUseIFrame=" + blnCanUseIFrame);
}

var courseExiting = false;
var waitingToConcede = false;
function PrepareCourseExit(actionConceded) {
    courseExiting = true;
    waitingToConcede = actionConceded;
    if (navigator.sendBeacon != null && !waitingToConcede) {
        for (var i = 0; i < aiccQueue.length; i++) {
            var queuedMsg = aiccQueue.shift();
            SubmitWithBeacon(queuedMsg[0], queuedMsg[1], queuedMsg[2], queuedMsg[3]);
        }
        aiccQueue = [];
    }
}

function SubmitWithBeacon(strAICCURL, strAICCSID, strRequestType, strAICCData) {
    var strPostData = FormatPostData(strAICCSID, strRequestType, strAICCData);

    navigator.sendBeacon(strAICCURL, new Blob([strPostData], { type: 'application/x-www-form-urlencoded' }))
}


function SubmitForm(strAICCURL, strAICCSID, strRequestType, strAICCData){
    if (lastPostType[strRequestType] === strAICCData) {
        return;
    }

    WriteToDebug ("In SubmitForm, setting fields");
    WriteToDebug ("strAICCURL = " + strAICCURL);
    WriteToDebug ("strAICCSID = " + strAICCSID);
    WriteToDebug ("strCommand = " + strRequestType);
    WriteToDebug ("strAICCData = " + strAICCData);
    WriteToDebug ("blnCanUseXMLHTTP = " + blnCanUseXMLHTTP);
    WriteToDebug ("blnCanUseIFrame = " + blnCanUseIFrame);

//  if(parent.AICC_USE_CUSTOM_COMMS && blnAppearsToBeCrossDomain){
    if (courseExiting && !waitingToConcede && navigator.sendBeacon != null) {
        SubmitWithBeacon(strAICCURL, strAICCSID, strRequestType, strAICCData);
    } else if (parent.AICC_USE_CUSTOM_COMMS) {
        WriteToDebug('Using Custom Cross Domain Communications for AICC');
        SubmitFormUsingCustomComms(strAICCURL, strAICCSID, strRequestType, strAICCData);
    }else if (blnCanUseXMLHTTP){

        // try/catch is supported in all browsers that support xmlHttp
        try{
            if (blnCanUseSyncXHR) {
                SubmitFormUsingSyncXHR(strAICCURL, strAICCSID, strRequestType, strAICCData);
            } else {
                SubmitFormUsingXMLHTTP(strAICCURL, strAICCSID, strRequestType, strAICCData);
            }
        }
        catch(e){
            waitingAiccResponse = false;
            if(objXDomainReq != null){
                try{
                    SubmitFormUsingXDomainRequest(strAICCURL, strAICCSID, strRequestType, strAICCData)
                }
                catch(e){
                    //we have a cross domain issue, so fall back on normal form posts
                    WriteToDebug("Error submitting form via XDomainRequest, falling back . Error=" + ((e.message)?e.message:e.toString()));

                    blnCanUseXMLHTTP = false;
                    blnCanUseIFrame = false;
                    SubmitForm(strAICCURL, strAICCSID, strRequestType, strAICCData);
                }
            }
            else{
                //we have a cross domain issue, so fall back on normal form posts
                WriteToDebug("Error submitting form via XmlHttp, falling back to normal form post. Error=" + ((e.message)?e.message:e.toString()));

                blnCanUseXMLHTTP = false;
                blnCanUseIFrame = false;
                SubmitForm(strAICCURL, strAICCSID, strRequestType, strAICCData);
            }
        }
    }
    else if(blnCanUseIFrame){

        //if we're in a browser that can trap errors, try to trap a permission denied error
        //otherwise, check our rudimentary domain checking to see if we should try iFrame. If it is
        //a disallowed cross domain request that results in a permission denied error
        //then we need to manually disable the iFrame post in the configuration file.

        //this try catch probably won't fire, it will be the one up in GetIFrameContents,
        //in there we just set things to fall back on normal form posts
        //no need to re-submit the form because it was just the initial Get that won't return data anyway
        try{
            SubmitFormUsingIFrame(strAICCURL, strAICCSID, strRequestType, strAICCData);
        }
        catch (e){
            //we have a cross domain issue, so fall back on normal form posts
            WriteToDebug("Error submitting form via IFrame, falling back to normal form post. Error=" + ((e.message)?e.message:e.toString()));

            blnCanUseIFrame = false;
            SubmitForm(strAICCURL, strAICCSID, strRequestType, strAICCData);
        }
    }
    else{
        SubmitFormNormally(strAICCURL, strAICCSID, strRequestType, strAICCData);
    }

    if (strRequestType != REQUEST_TYPE_GET) {
        lastPostType[strRequestType] = strAICCData;
    }
}



//---------------------------------------------------------------------
//AICC Functions
//---------------------------------------------------------------------

function ProcessLMSResult(strRequestType, strLMSResult){
    if (waitingToConcede && strRequestType === REQUEST_TYPE_EXIT) {
        parent.PerformConcedeActions();
    }

    WriteToDebug("In ProcessLMSResult, strRequestType=" + strRequestType + " strLMSResult=" + strLMSResult)

    var blnMadeBlindSubmit;

    var strAICCErrorLine = "";
    var strAICCErrorTextLine = "";

    var aryAICCResponseLines;
    var strLine;
    var strTrimmedLine;

    var intPos;

    var blnError;
    var strErrorMessage;

    //if we made a blind submit with a standard form, we can't read the result, so just proceed
    blnMadeBlindSubmit = (!blnCanUseXMLHTTP && !blnCanUseIFrame);

    WriteToDebug("blnMadeBlindSubmit=" + blnMadeBlindSubmit);


    strLMSResult = new String(unescape(strLMSResult));
    aryAICCResponseLines = strLMSResult.split("\n");    //only use \n instead of \r\n b/c some LMS's will only use one character

    for (var i=0; i < aryAICCResponseLines.length; i++){

        WriteToDebug("Processing Line #" + i + ": " + aryAICCResponseLines[i]);

        strLine = aryAICCResponseLines[i];

        strLine = strLine.replace(/\r/g, "");

        strTrimmedLine = parent.Trim(strLine);

        //need to look for the text "error" only at the start of the line since the
        //value of the field can conceivably contain the text "error"
        if (strTrimmedLine.toLowerCase().indexOf("error") == 0){
            if (strTrimmedLine.toLowerCase().indexOf("error_text") == 0){
                WriteToDebug("Found Error Text Line");
                strAICCErrorTextLine = strLine;
            }
            else{
                WriteToDebug("Found Error Number Line");
                strAICCErrorLine = strLine;
            }
        }

    }


    //check for errors and alert if found, check for AICC error, as well as HTTP error like 404
    blnError = false;
    strErrorMessage = "";

    if (!blnMadeBlindSubmit){

        if (strAICCErrorLine == ""){
            blnError = true;
            strErrorMessage = "ERROR - LMS did not return a valid status code.";
        }

        if (strAICCErrorLine != "" && strAICCErrorLine.toLowerCase().search(/error\s*=\s*0/) == -1){
            blnError = true;
            strErrorMessage = "ERROR - LMS returned an error - " + strAICCErrorLine + " - " + strAICCErrorTextLine;
        }
    }

    if (blnError){
        WriteToDebug("Found Error");

        if(objXDomainReq !== null){
            WriteToDebug("This error may be caused by a strict AICC implementation - fallback");
            objXDomainReq = null;
            throw {
                mesg: "This error may be caused by a strict AICC implementation - fallback"
            };
        }
        parent.AICC_SetErrorInfo(strAICCErrorLine, strAICCErrorTextLine);

        if (strRequestType == REQUEST_TYPE_GET){
            parent.InitializeExecuted(false, strErrorMessage);
            return;
        }
        if (strRequestType == REQUEST_TYPE_PUT){
            parent.AICC_PutParamFailed();
            return;
        }
        if (strRequestType == REQUEST_TYPE_PUT_INTERACTIONS){
            parent.AICC_PutInteractionsFailed();
            return;
        }
        else{
            DisplayError(strErrorMessage);
            return;
        }
    }

    if (strRequestType == REQUEST_TYPE_GET){

        WriteToDebug("In request type = get");

        //keep default values if we made a blind submit
        if (!blnMadeBlindSubmit){
            window.parent.ParseGetParamData(strLMSResult);
        }

        WriteToDebug("Calling InitializeExecuted");

        parent.InitializeExecuted(true, "");
    }

}



function GetAICCSID(){

    var strSID = "";

    WriteToDebug("In GetAICCSID");

    strSID = window.parent.GetQueryStringValue("AICC_SID", window.parent.document.location.search)

    WriteToDebug("GetAICCSID returning: " + strSID);

    return strSID;

}

function GetAICCURL(){

    var strURL = "";

    WriteToDebug("In GetAICCURL");

    strURL = window.parent.GetQueryStringValue("AICC_URL", window.parent.document.location.search)

    WriteToDebug("Querystring value = " + strURL);

    if (strURL != null && strURL.length > 0){

        if (window.parent.AICC_COMM_PREPEND_HTTP_IF_MISSING === undefined ||
            window.parent.AICC_COMM_PREPEND_HTTP_IF_MISSING === null ||
            window.parent.AICC_COMM_PREPEND_HTTP_IF_MISSING === true){

            WriteToDebug("Checking for presense of 'http://'");

            if (strURL.indexOf("http://") < 0 && strURL.indexOf("https://") < 0){
                WriteToDebug("Prepending 'http://'");
                strURL = "http://" + strURL;
            }
        }
    }

    WriteToDebug("GetAICCURL returning: " + strURL);

    return strURL;

}

//MR 5/31/05 - added this because this frame is always slightly visible so we have an avenue into the debug information even if the content developer doesn't build one in
window.document.onkeypress = CheckForDebugCommand;

var intQuestionCounter = 0;
var ASCII_QUESTION = 63;

function CheckForDebugCommand(e){

    var intKeyCode = 0;
    if (window.event) {
        e = window.event;
        intKeyCode = e.keyCode;
    }
    else  {
        intKeyCode = e.which;
    }

    if (intKeyCode == ASCII_QUESTION){
        intQuestionCounter++;
        if (intQuestionCounter == 3){
            intQuestionCounter = 0;

            window.parent.ShowDebugWindow();
        }
    }
    else if (intKeyCode !=0){       //in FireFox, the shift key comes through as a keypress with code of 0...we want to ignore this
        intQuestionCounter = 0;
    }
}

function getUUID() {
    /*jslint bitwise: true, eqeq: true */
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = Math.random() * 16|0, v = c == "x" ? r : (r&0x3|0x8);
                return v.toString(16);
            }
    );
}


//MR 5/31/05 - added this call in addition to the frameset onunload calls to give us a better chance of getting an AICC EXITAU call in before the frames unload
function ProcessUnload(){
    if (window.parent){
        window.parent.Unload();
    }
}
</script>
</head>
<body onload="DetectPreferredCommMethod();" onunload="ProcessUnload();" onbeforeunload="ProcessUnload();">

<iframe id="AICCFrame"
  name="AICCFrame"
  style="width:10px; height:10px; border: 0px"
  src="blank.html" onload="IFrameLoaded();"></iframe>

<form name="frmAICC" method="Post" action="blank.html" target="AICCFrame" ID="Form1">
<input type="hidden" name="session_id" value="" ID="Hidden1">
<input type="hidden" name="command" value="" ID="Hidden2">
<input type="hidden" name="version" value="3.5" ID="Hidden3">
<input type="hidden" name="aicc_data" value="" ID="Hidden4">
</form>

</body>
</html>
