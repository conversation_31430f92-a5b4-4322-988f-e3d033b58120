﻿window.globalProvideData('slide', '{"title":"Untitled Slide","trackViews":true,"showMenuResultIcon":false,"viewGroupId":"","historyGroupId":"","videoZoom":"","scrolling":false,"transition":"appear","transDuration":0,"transDir":1,"wipeTrans":false,"slideLock":false,"navIndex":-1,"globalAudioId":"","thumbnailid":"","slideNumberInScene":6,"includeInSlideCounts":true,"presenterRef":{"id":"none"},"slideLayers":[{"enableSeek":true,"enableReplay":true,"timeline":{"duration":6000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6FALjlD41IW"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"60IWhf6vmmv"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5gDjhc6eZSi"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6Qt0wQH47bG"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6irrgqpNCPg"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6irrgqpNCPg.6RQV9jKe49f"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5Zf8g4zgWXS"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6TbWydQKkDH"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6AQHtUbW082"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6krz8peGydI"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"61jub7Qy8OO"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5W3Xv2j6uLu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5hha4NXNQWL"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ctqN84HwS5"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fjXMMrmwFm"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5v2NzpQVpmK"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6oUaUG5iJUR"}}]},{"kind":"ontimelinetick","time":1750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6irrgqpNCPg.6aSwiOvDzId"}}]},{"kind":"ontimelinetick","time":3000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6irrgqpNCPg.64bjb8hr6ku"}}]},{"kind":"ontimelinetick","time":3250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6U340bcyWS7"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5iIJUEgtwxb"}}]},{"kind":"ontimelinetick","time":4500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6MOJ84E0jw1"}}]}]},"objects":[{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":51,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":50,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":1}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":2}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":64,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fgE044eYLG.6cac0NAnUiv"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":55,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":54,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":3}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":4}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":66,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.5Xlh34Ekb5w"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":53,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":52,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":5}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":6}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":65,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":3,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6QtAxFNOoxm"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_6ctqN84HwS5","type":"richvartext","xPos":10,"yPos":5,"xAccOffset":0,"yAccOffset":0,"width":212,"height":30,"device":false,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Pontuação: ","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"%_player.nota%","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"/20","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":28,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":135,"yPos":672,"tabIndex":47,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":41,"altText":"Pontuação: %_player.nota%/20","pngfb":false,"pr":{"l":"Lib","i":7}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":40,"resume":false,"useHandCursor":true,"id":"6ctqN84HwS5"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PYUvVg1zLz_545423499","id":"01","linkId":"txt__default_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6dBYEI4LvKs_2042009458","id":"02","linkId":"txt__default_Disabled_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":12}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":48,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":false,"useHandCursor":true,"id":"6fjXMMrmwFm","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":28,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":false,"useHandCursor":true,"id":"5v2NzpQVpmK"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":112,"yPos":8,"tabIndex":31,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":579,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1158,"bottom":40,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":14}},"html5data":{"xPos":-1,"yPos":-1,"width":1159,"height":41,"strokewidth":0}},"width":1158,"height":40,"resume":false,"useHandCursor":true,"id":"6AQHtUbW082"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6krz8peGydI_1913940565","id":"01","linkId":"txt__default_6krz8peGydI","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: FUNCIONAMENTO DA IA","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":23,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":242,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":364}}}],"shapemaskId":"","xPos":128,"yPos":12,"tabIndex":32,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: FUNCIONAMENTO DA IA","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":false,"useHandCursor":true,"id":"6krz8peGydI"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6FALjlD41IW_-1087952299","id":"01","linkId":"txt__default_6FALjlD41IW","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":780,"height":54,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Como os Algoritmos de IA Processam Dados e Aprendem ","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":20,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.4,"descent":6.267,"leading":5.333,"underlinePosition":-1.04,"underlineThickness":2.133,"xHeight":15.253}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":52,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":764,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":435}}}],"shapemaskId":"","xPos":240,"yPos":88,"tabIndex":33,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":400,"rotateYPos":32,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":800,"bottom":64,"altText":"Como os Algoritmos de IA Processam Dados e Aprendem ","pngfb":false,"pr":{"l":"Lib","i":197}},"html5data":{"xPos":0,"yPos":0,"width":800,"height":64,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":800,"height":64,"resume":false,"useHandCursor":true,"id":"6FALjlD41IW"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"60IWhf6vmmv_-209262971","id":"01","linkId":"txt__default_60IWhf6vmmv","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1132,"height":142,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"O coração da IA está nos seus algoritmos, que são conjuntos de regras matemáticas que guiam as máquinas a tomar decisões ou resolver problemas. Esses algoritmos processam grandes volumes de dados, identificam padrões e fazem previsões ou decisões com base nos dados aprendidos.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":277,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":1108,"bottom":115,"pngfb":false,"pr":{"l":"Lib","i":437}}}],"shapemaskId":"","xPos":88,"yPos":168,"tabIndex":34,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":576,"rotateYPos":76,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1153,"bottom":153,"altText":"O coração da IA está nos seus algoritmos, que são conjuntos de regras matemáticas que guiam as máquinas a tomar decisões ou resolver problemas. Esses algoritmos processam grandes volumes de dados, identificam padrões e fazem previsões ou decisões com base nos dados aprendidos.","pngfb":false,"pr":{"l":"Lib","i":436}},"html5data":{"xPos":-2,"yPos":-2,"width":1155,"height":155,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1152,"height":152,"resume":false,"useHandCursor":true,"id":"60IWhf6vmmv"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5gDjhc6eZSi_276712577","id":"01","linkId":"txt__default_5gDjhc6eZSi","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":532,"height":26,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A aprendizagem ocorre de três maneiras principais:","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":50,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":485,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":439}}}],"shapemaskId":"","xPos":80,"yPos":344,"tabIndex":35,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":276,"rotateYPos":18,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":552,"bottom":36,"altText":"A aprendizagem ocorre de três maneiras principais:","pngfb":false,"pr":{"l":"Lib","i":438}},"html5data":{"xPos":0,"yPos":0,"width":552,"height":36,"strokewidth":0}},"width":552,"height":36,"resume":false,"useHandCursor":true,"id":"5gDjhc6eZSi"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6Qt0wQH47bG_-2141590749","id":"01","linkId":"txt__default_6Qt0wQH47bG","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":204,"height":52,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Aprendizagem automática","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontIsBold":false,"ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":23,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":192,"bottom":61,"pngfb":false,"pr":{"l":"Lib","i":440}}}],"shapemaskId":"","xPos":64,"yPos":408,"tabIndex":37,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":112,"rotateYPos":31,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":224,"bottom":62,"altText":"Aprendizagem automática","pngfb":false,"pr":{"l":"Lib","i":397}},"html5data":{"xPos":0,"yPos":0,"width":224,"height":62,"strokewidth":0}},"width":224,"height":62,"resume":false,"useHandCursor":true,"id":"6Qt0wQH47bG"},{"kind":"objgroup","objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":9,"yPos":8,"tabIndex":39,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":61.5,"rotateYPos":-0.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":126,"bottom":2,"altText":"Line 1","pngfb":false,"pr":{"l":"Lib","i":441}},"html5data":{"xPos":-2,"yPos":-2,"width":128,"height":4,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":124,"height":0,"resume":false,"useHandCursor":true,"id":"6RQV9jKe49f"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":133,"yPos":8,"tabIndex":40,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":-0.5,"rotateYPos":31.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":2,"bottom":66,"altText":"Line 2","pngfb":false,"pr":{"l":"Lib","i":442}},"html5data":{"xPos":-2,"yPos":-2,"width":4,"height":68,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":0,"height":64,"resume":false,"useHandCursor":true,"id":"6aSwiOvDzId"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":24,"id":"01","url":"","type":"normal","width":170,"height":65,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":133,"yPos":72,"tabIndex":41,"tabEnabled":true,"xOffset":-32,"yOffset":-32,"rotateXPos":53,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":170,"bottom":65,"altText":"Arrow 1","pngfb":false,"pr":{"l":"Lib","i":443}},"html5data":{"url":"txt__default_64bjb8hr6ku.png","xPos":-32,"yPos":-32,"width":170,"height":65,"strokewidth":1,"mask":"126FO1XA7O3XA6O3XA7O3X41O69X3FO6AX3EO69XA3O3XA3O4XA3O3XA5O2X1213O"}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":106,"height":0,"resume":false,"useHandCursor":true,"id":"64bjb8hr6ku"}],"accType":"text","altText":"Group\\r\\n 1","shapemaskId":"","xPos":256,"yPos":424,"tabIndex":38,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":124,"rotateYPos":40,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":13,"scrolling":true,"shuffleLock":false,"width":248,"height":80,"resume":false,"useHandCursor":true,"id":"6irrgqpNCPg"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5g7prbDCXO8_898992466","id":"01","linkId":"txt__default_6MOJ84E0jw1","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Não Supervisionada","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}},{"text":"\\r\\n","style":{"fontSize":12,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":20,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":146,"bottom":116,"pngfb":false,"pr":{"l":"Lib","i":448}}},{"kind":"textdata","uniqueId":"5gLZQNIJsJ3_1424137931","id":"02","linkId":"txt__default_Disabled_6MOJ84E0jw1","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"foregroundColor":"#000000","linkColor":"#000000","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"foregroundColor":"#000000","linkColor":"#000000","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Não Supervisionada\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"foregroundColor":"#000000","linkColor":"#000000","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":20,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":146,"bottom":118,"pngfb":false,"pr":{"l":"Lib","i":449}}},{"kind":"textdata","uniqueId":"6HRNeZquUZL_697007283","id":"04","linkId":"txt__default_Hover_6MOJ84E0jw1","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontSize":12,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem\\n","style":{"fontSize":12,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Não Supervisionada\\r\\n","style":{"fontSize":12,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":20,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":146,"bottom":118,"pngfb":false,"pr":{"l":"Lib","i":450}}}],"shapemaskId":"","xPos":640,"yPos":472,"tabIndex":43,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":91.5,"rotateYPos":95.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":444}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":446}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":447}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\nAprendizagem\\nNão Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"wipe","settings":[{"kind":"setting","name":"direction","value":"frombottom"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":184,"height":192,"resume":true,"useHandCursor":true,"id":"6MOJ84E0jw1","variables":[{"kind":"variable","name":"_visited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_savevisited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetVisitedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"var","value":"#_visited"}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpSetVisitedState"},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6jkbcfM7xp0"}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_this.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"_this"}}]},{"kind":"adjustvar","variable":"_this._hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_this.ActGrpSetDisabledState"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"645fCT6k050_1310703952","id":"01","linkId":"txt__default_5iIJUEgtwxb","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem por Reforço\\r","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}},{"text":"\\n","style":{"fontSize":12,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":26,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":147,"bottom":116,"pngfb":false,"pr":{"l":"Lib","i":454}}},{"kind":"textdata","uniqueId":"6KP31atFeyX_-208469279","id":"02","linkId":"txt__default_Disabled_5iIJUEgtwxb","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem por Reforço\\r","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}},{"text":"\\n","style":{"fontSize":12,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":26,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":147,"bottom":116,"pngfb":false,"pr":{"l":"Lib","i":455}}}],"shapemaskId":"","xPos":472,"yPos":472,"tabIndex":42,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":91.5,"rotateYPos":95.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":15,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":444}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":451}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":452}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":453}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem por Reforço\\r\\n","pngfb":false,"pr":{"l":"Lib","i":452}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"wipe","settings":[{"kind":"setting","name":"direction","value":"frombottom"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":184,"height":192,"resume":true,"useHandCursor":true,"id":"5iIJUEgtwxb","variables":[{"kind":"variable","name":"_visited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_savevisited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetVisitedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"var","value":"#_visited"}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpSetVisitedState"},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6EXFxCs9QEt"}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_this.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"_this"}}]},{"kind":"adjustvar","variable":"_this._hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_this.ActGrpSetDisabledState"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5WF2134VuE4_-908185874","id":"01","linkId":"txt__default_6U340bcyWS7","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"justification":"center","tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"justification":"center","tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem Supervisionada\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":29,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":147,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":456}}},{"kind":"textdata","uniqueId":"5l4DWoCwz64_428055354","id":"02","linkId":"txt__default_Disabled_6U340bcyWS7","type":"acctext","xPos":10,"yPos":5,"xAccOffset":36,"yAccOffset":33,"width":112,"height":126,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"foregroundColor":"#000000","linkColor":"#000000","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Aprendizagem Supervisionada","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":12,"foregroundColor":"#000000","linkColor":"#000000","ascent":15.68,"descent":3.76,"leading":0,"underlinePosition":-0.816,"underlineThickness":0.784,"xHeight":8.8}},{"text":"\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":29,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":147,"bottom":113,"pngfb":false,"pr":{"l":"Lib","i":457}}}],"shapemaskId":"","xPos":548,"yPos":352,"tabIndex":36,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":91.5,"rotateYPos":95.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":16,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":444}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":451}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":453}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Visited_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-1,"top":-1,"right":185,"bottom":193,"altText":"\\n\\nAprendizagem Supervisionada\\r\\n","pngfb":false,"pr":{"l":"Lib","i":445}},"html5data":{"xPos":-1,"yPos":-1,"width":186,"height":194,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"wipe","settings":[{"kind":"setting","name":"direction","value":"frombottom"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":184,"height":192,"resume":true,"useHandCursor":true,"id":"6U340bcyWS7","variables":[{"kind":"variable","name":"_visited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_savevisited","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetVisitedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"var","value":"#_visited"}},{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_visited","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_savevisited","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpSetVisitedState"},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6lMuaDhny71"}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_this.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"_this"}}]},{"kind":"adjustvar","variable":"_this._hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_this.ActGrpSetDisabledState"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6OEW0bnmEX7_-868973541","id":"01","linkId":"txt__default_6oUaUG5iJUR","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"6gWGnZtj2VZ_-1820198052","id":"02","linkId":"txt__default_Disabled_6oUaUG5iJUR","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":46,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":17,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":false,"useHandCursor":true,"id":"6oUaUG5iJUR","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5ZcIVIKkL4Z.6fCJmJijvW9"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"gte","valuea":"_player.#3_6","typea":"var","valueb":3,"typeb":"number"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_this.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"_this"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_this.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}],"elseActions":[{"kind":"adjustvar","variable":"_this._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"_this._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_this.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5Zf8g4zgWXS_-127540846","id":"01","linkId":"txt__default_5Zf8g4zgWXS","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":489,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"(Clique nos círculos e em “próximo” para continuar)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":51,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":461,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":199}}}],"shapemaskId":"","xPos":386,"yPos":672,"tabIndex":44,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":254.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":18,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":509,"bottom":40,"altText":"(Clique nos círculos e em “próximo” para continuar)","pngfb":false,"pr":{"l":"Lib","i":66}},"html5data":{"xPos":-1,"yPos":-1,"width":510,"height":41,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":509,"height":40,"resume":false,"useHandCursor":true,"id":"5Zf8g4zgWXS"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6TbWydQKkDH_187528518","id":"01","linkId":"txt__default_6TbWydQKkDH","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":132,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"06/10","style":{"fontSize":14,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":5,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":62,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":458}}}],"shapemaskId":"","xPos":1072,"yPos":672,"tabIndex":49,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":76,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":19,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":152,"bottom":33,"altText":"06/10","pngfb":false,"pr":{"l":"Lib","i":70}},"html5data":{"xPos":0,"yPos":0,"width":152,"height":33,"strokewidth":0}},"width":152,"height":33,"resume":false,"useHandCursor":true,"id":"6TbWydQKkDH"}],"startTime":-1,"elapsedTimeMode":"normal","useHandCursor":false,"resume":false,"kind":"slidelayer","isBaseLayer":true},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":5000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5caTOxhiHJX"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5rr2yMmyg4V"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6AKKJrsYhPa"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lBpDkNHdGb"}}]},{"kind":"ontimelinetick","time":3000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5cZgt7pKH4T"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"5lBpDkNHdGb"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5cZgt7pKH4T_779417596","id":"01","linkId":"txt__default_5cZgt7pKH4T","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":292,"height":142,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A máquina aprende através de tentativa e erro, recebendo recompensas ou punições com base nas acções tomadas. Este tipo é amplamente usado em jogos e robótica.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":159,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":306,"bottom":145,"pngfb":false,"pr":{"l":"Lib","i":460}}}],"shapemaskId":"","xPos":72,"yPos":504,"tabIndex":16,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":156,"rotateYPos":76,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":312,"bottom":152,"altText":"A máquina aprende através de tentativa e erro, recebendo recompensas ou punições com base nas acções tomadas. Este tipo é amplamente usado em jogos e robótica.","pngfb":false,"pr":{"l":"Lib","i":459}},"html5data":{"xPos":-1,"yPos":-1,"width":313,"height":153,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":312,"height":152,"resume":true,"useHandCursor":true,"id":"5cZgt7pKH4T"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":392,"yPos":560,"tabIndex":18,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":19.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-2,"right":81,"bottom":41,"altText":"Right Arrow 1","pngfb":false,"pr":{"l":"Lib","i":461}},"html5data":{"xPos":-1,"yPos":-2,"width":82,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}}]}],"width":80,"height":40,"resume":true,"useHandCursor":true,"id":"5caTOxhiHJX"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":456,"yPos":520,"tabIndex":17,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":99.5,"rotateYPos":71.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":200,"bottom":144,"altText":"Oval Hotspot 1","pngfb":false,"pr":{"l":"Lib","i":462}},"html5data":{"xPos":0,"yPos":0,"width":200,"height":144,"strokewidth":0}},"width":200,"height":144,"resume":true,"useHandCursor":true,"id":"5rr2yMmyg4V"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":368,"yPos":432,"tabIndex":15,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":95.5,"rotateYPos":71.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":192,"bottom":144,"altText":"Oval Hotspot 2","pngfb":false,"pr":{"l":"Lib","i":463}},"html5data":{"xPos":0,"yPos":0,"width":192,"height":144,"strokewidth":0}},"width":192,"height":144,"resume":true,"useHandCursor":true,"id":"6AKKJrsYhPa"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":448,"yPos":328,"tabIndex":14,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":191.5,"rotateYPos":167.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":384,"bottom":336,"altText":"Rectangular Hotspot 3","pngfb":false,"pr":{"l":"Lib","i":464}},"html5data":{"xPos":0,"yPos":0,"width":384,"height":336,"strokewidth":0}},"width":384,"height":336,"resume":true,"useHandCursor":true,"id":"5lBpDkNHdGb"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6EXFxCs9QEt","events":[{"kind":"ontimelinecomplete","actions":[{"kind":"adjustvar","variable":"_player.3_6","operator":"add","value":{"type":"number","value":1}}]},{"kind":"ontopmostlayer","actions":[{"kind":"setactivetimeline","objRef":{"type":"string","value":"_this"}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":372,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":5000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5ikW70UvWOE"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6KDMUVr0bcp"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TE0vj7OFez"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6HvRpO9gJ1x"}}]},{"kind":"ontimelinetick","time":3000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6NY71uhTGrT"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"6KDMUVr0bcp"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6NY71uhTGrT_-1266885115","id":"01","linkId":"txt__default_6NY71uhTGrT","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":292,"height":142,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A máquina trabalha com dados sem rótulos e tenta encontrar padrões ou agrupamentos nos dados.\\r","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":94,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":303,"bottom":111,"pngfb":false,"pr":{"l":"Lib","i":465}}}],"shapemaskId":"","xPos":928,"yPos":504,"tabIndex":21,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":156,"rotateYPos":76,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":312,"bottom":152,"altText":"\\nA máquina trabalha com dados sem rótulos e tenta encontrar padrões ou agrupamentos nos dados.\\r","pngfb":false,"pr":{"l":"Lib","i":459}},"html5data":{"xPos":-1,"yPos":-1,"width":313,"height":153,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":312,"height":152,"resume":true,"useHandCursor":true,"id":"6NY71uhTGrT"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":688,"yPos":480,"tabIndex":20,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":67.5,"rotateYPos":99.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":136,"bottom":200,"altText":"Oval Hotspot 1","pngfb":false,"pr":{"l":"Lib","i":466}},"html5data":{"xPos":0,"yPos":0,"width":136,"height":200,"strokewidth":0}},"width":136,"height":200,"resume":true,"useHandCursor":true,"id":"5ikW70UvWOE"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":448,"yPos":328,"tabIndex":19,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":199.5,"rotateYPos":167.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":400,"bottom":336,"altText":"Rectangular Hotspot 2","pngfb":false,"pr":{"l":"Lib","i":467}},"html5data":{"xPos":0,"yPos":0,"width":400,"height":336,"strokewidth":0}},"width":400,"height":336,"resume":true,"useHandCursor":true,"id":"6KDMUVr0bcp"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":832,"yPos":560,"tabIndex":23,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":19.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":41,"altText":"Right Arrow 2","pngfb":false,"pr":{"l":"Lib","i":468}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":42,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}},{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"165"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":80,"height":40,"resume":true,"useHandCursor":true,"id":"6TE0vj7OFez"},{"kind":"vectorshape","rotation":127.962,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":662,"yPos":513,"tabIndex":22,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":78.5,"rotateYPos":99.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":159,"bottom":200,"altText":"Oval Hotspot 3","pngfb":false,"pr":{"l":"Lib","i":469}},"html5data":{"xPos":0,"yPos":0,"width":159,"height":200,"strokewidth":0}},"width":158,"height":200,"resume":true,"useHandCursor":true,"id":"6HvRpO9gJ1x"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":744,"resume":true,"useHandCursor":false,"id":"6jkbcfM7xp0","events":[{"kind":"ontimelinecomplete","actions":[{"kind":"adjustvar","variable":"_player.3_6","operator":"add","value":{"type":"number","value":1}}]},{"kind":"ontopmostlayer","actions":[{"kind":"setactivetimeline","objRef":{"type":"string","value":"_this"}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":4250,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6hJs2qNdy36"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5aGWBqk4Sx1"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"60icgvUS43U"}}]},{"kind":"ontimelinetick","time":3000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6degQvkFRff"}}]},{"kind":"ontimelinetick","time":3500,"actions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"60icgvUS43U"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6degQvkFRff_1965966812","id":"01","linkId":"txt__default_6degQvkFRff","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":292,"height":142,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"A máquina aprende com dados rotulados, onde as respostas correctas, são fornecidas. O algoritmo aprende a fazer previsões a partir desses exemplos.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":147,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":301,"bottom":133,"pngfb":false,"pr":{"l":"Lib","i":470}}}],"shapemaskId":"","xPos":848,"yPos":336,"tabIndex":25,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":156,"rotateYPos":76,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":312,"bottom":152,"altText":"A máquina aprende com dados rotulados, onde as respostas correctas, são fornecidas. O algoritmo aprende a fazer previsões a partir desses exemplos.","pngfb":false,"pr":{"l":"Lib","i":459}},"html5data":{"xPos":-1,"yPos":-1,"width":313,"height":153,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":312,"height":152,"resume":true,"useHandCursor":true,"id":"6degQvkFRff"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":744,"yPos":400,"tabIndex":27,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":19.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":41,"altText":"Right Arrow 1","pngfb":false,"pr":{"l":"Lib","i":468}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":42,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"mask":{"type":"split","settings":[{"kind":"setting","name":"direction","value":"verticalin"}],"duration":750,"easing":"linear","easingdir":"easeinout"}},{"kind":"tween","time":0,"duration":750,"position":{"relativerotation":false,"relativestartpoint":false,"path":[{"kind":"segment","type":"line","anchora":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"325"},"anchorb":{"x":"$RawXPos","y":"$RawYPos","dx":"0","dy":"0"}}],"duration":750,"easing":"cubic","easingdir":"easeout"}}]}],"width":80,"height":40,"resume":true,"useHandCursor":true,"id":"6hJs2qNdy36"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":544,"yPos":352,"tabIndex":26,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":95.5,"rotateYPos":95.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":192,"bottom":192,"altText":"Oval Hotspot 1","pngfb":false,"pr":{"l":"Lib","i":471}},"html5data":{"xPos":0,"yPos":0,"width":192,"height":192,"strokewidth":0}},"width":192,"height":192,"resume":true,"useHandCursor":true,"id":"5aGWBqk4Sx1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":448,"yPos":328,"tabIndex":24,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":191.5,"rotateYPos":171.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":384,"bottom":344,"altText":"Rectangular Hotspot 2","pngfb":false,"pr":{"l":"Lib","i":472}},"html5data":{"xPos":0,"yPos":0,"width":384,"height":344,"strokewidth":0}},"width":384,"height":344,"resume":true,"useHandCursor":true,"id":"60icgvUS43U"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6lMuaDhny71","events":[{"kind":"ontimelinecomplete","actions":[{"kind":"adjustvar","variable":"_player.3_6","operator":"add","value":{"type":"number","value":1}}]},{"kind":"ontopmostlayer","actions":[{"kind":"setactivetimeline","objRef":{"type":"string","value":"_this"}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":39500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tan86yaQfz"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VCYeYUEni1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69s3gqNHg1Y"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ayzt09NtEH"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5whFHQDPFcs"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6jXPK0rdGU9"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lVfqqZUc0d"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VHGXGQPaHr"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6dIY6ufVyqZ"}}]},{"kind":"ontimelinetick","time":7979,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"65Z2OtQpB8s"}}]},{"kind":"ontimelinetick","time":12750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6a0vWVXybMh"}}]},{"kind":"ontimelinetick","time":19000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5l4SITyHfG9"}}]},{"kind":"ontimelinetick","time":26000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5r3n1dkaOym"}}]},{"kind":"ontimelinetick","time":31500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ht0I6wfKd3"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":0,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"5tan86yaQfz"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":72,"tabIndex":1,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":291.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":585,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":29}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":586,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":584,"resume":true,"useHandCursor":true,"id":"5ayzt09NtEH"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":2,"id":"01","url":"story_content/6Q70oTwPFfL_RC36618.png","type":"normal","altText":"target-audience.png","width":512,"height":512,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":80,"yPos":192,"tabIndex":4,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":160,"rotateYPos":160,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":320,"bottom":320,"altText":"target-audience.png","pngfb":false,"pr":{"l":"Lib","i":30}},"html5data":{"xPos":0,"yPos":0,"width":320,"height":320,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":320,"height":320,"resume":true,"useHandCursor":true,"id":"6VCYeYUEni1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69s3gqNHg1Y_-519312530","id":"01","linkId":"txt__default_69s3gqNHg1Y","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":652,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"OBJECTIVOS DO CURSO","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"linkColor":"#1888E7","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":452,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":32}}}],"shapemaskId":"","xPos":504,"yPos":104,"tabIndex":3,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":336,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":672,"bottom":40,"altText":"OBJECTIVOS DO CURSO","pngfb":false,"pr":{"l":"Lib","i":31}},"html5data":{"xPos":0,"yPos":0,"width":672,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":672,"height":40,"resume":true,"useHandCursor":true,"id":"69s3gqNHg1Y"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":2,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5lVfqqZUc0d","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_5whFHQDPFcs","type":"hiddentext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":756,"height":374,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":5,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":776,"bottom":384,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\nConhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\nIdentificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\nEntender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\nReconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\nFamiliarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\nExplorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\nCompreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","pngfb":false,"pr":{"l":"Lib","i":35}},"html5data":{"xPos":0,"yPos":0,"width":776,"height":384,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":true,"useHandCursor":true,"id":"5whFHQDPFcs"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5ht0I6wfKd3","id":"01","linkId":"txt_5ht0I6wfKd3","type":"acctext","xPos":10,"yPos":330,"xAccOffset":10,"yAccOffset":330,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":694,"bottom":376,"pngfb":false,"pr":{"l":"Lib","i":37}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":56,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":694,"bottom":376,"altText":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":694,"height":376,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5ht0I6wfKd3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5r3n1dkaOym","id":"01","linkId":"txt_5r3n1dkaOym","type":"acctext","xPos":10,"yPos":273,"xAccOffset":10,"yAccOffset":273,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":746,"bottom":319,"pngfb":false,"pr":{"l":"Lib","i":38}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":57,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":746,"bottom":319,"altText":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":746,"height":319,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5r3n1dkaOym"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5l4SITyHfG9","id":"01","linkId":"txt_5l4SITyHfG9","type":"acctext","xPos":10,"yPos":238,"xAccOffset":10,"yAccOffset":238,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":700,"bottom":261,"pngfb":false,"pr":{"l":"Lib","i":39}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":58,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":700,"bottom":261,"altText":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":700,"height":261,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5l4SITyHfG9"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6a0vWVXybMh","id":"01","linkId":"txt_6a0vWVXybMh","type":"acctext","xPos":10,"yPos":181,"xAccOffset":10,"yAccOffset":181,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":671,"bottom":227,"pngfb":false,"pr":{"l":"Lib","i":40}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":59,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":671,"bottom":227,"altText":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":671,"height":227,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6a0vWVXybMh"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_65Z2OtQpB8s","id":"01","linkId":"txt_65Z2OtQpB8s","type":"acctext","xPos":10,"yPos":123,"xAccOffset":10,"yAccOffset":123,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":742,"bottom":169,"pngfb":false,"pr":{"l":"Lib","i":41}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":60,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":742,"bottom":169,"altText":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":742,"height":169,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"65Z2OtQpB8s"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6dIY6ufVyqZ","id":"01","linkId":"txt_6dIY6ufVyqZ","type":"acctext","xPos":10,"yPos":66,"xAccOffset":10,"yAccOffset":66,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":714,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":42}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":61,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":714,"bottom":112,"altText":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":714,"height":112,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6dIY6ufVyqZ"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6VHGXGQPaHr","id":"01","linkId":"txt_6VHGXGQPaHr","type":"acctext","xPos":10,"yPos":9,"xAccOffset":10,"yAccOffset":9,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":705,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":43}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":62,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":705,"bottom":54,"altText":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":705,"height":54,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6VHGXGQPaHr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6jXPK0rdGU9","id":"01","linkId":"txt_6jXPK0rdGU9","type":"acctext","xPos":10,"yPos":-26,"xAccOffset":10,"yAccOffset":-26,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":-27,"right":610,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":44}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":63,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":-27,"right":610,"bottom":0,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":-27,"width":610,"height":27,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6jXPK0rdGU9"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6QtAxFNOoxm"},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TRpicPw6tU"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VcGQGd6sQ4"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6HniC2d4w8h"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nw6rMDcq88"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5wH7XkMdNj1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fLNGDgml4K"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5a1AGGT21Cq"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ex489sIWwL"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":6,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6TRpicPw6tU"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":64,"tabIndex":7,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":279.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":561,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":45}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":562,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":560,"resume":true,"useHandCursor":true,"id":"5VcGQGd6sQ4"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":3,"id":"01","url":"story_content/6S7pEErDZEG.png","type":"normal","altText":"Agrupar 1.png","width":959,"height":657,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":1,"yPos":80,"tabIndex":8,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":391,"rotateYPos":268,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":782,"bottom":536,"altText":"Agrupar 1.png","pngfb":false,"pr":{"l":"Lib","i":46}},"html5data":{"xPos":0,"yPos":0,"width":782,"height":536,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":782,"height":536,"resume":true,"useHandCursor":true,"id":"6HniC2d4w8h"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":16,"yPos":94,"tabIndex":10,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":40,"rotateYPos":18.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":80,"bottom":37,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":47}},"html5data":{"xPos":0,"yPos":0,"width":80,"height":37,"strokewidth":0}},"width":80,"height":37,"resume":true,"useHandCursor":true,"id":"5nw6rMDcq88"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":9,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5wH7XkMdNj1","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6fLNGDgml4K_1529763356","id":"01","linkId":"txt__default_6fLNGDgml4K","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":308,"height":190,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Play/Pause\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Timeline\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reiniciar ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Volume\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Voltar ao menu\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Objectivos do curso","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":191,"bottom":193,"pngfb":false,"pr":{"l":"Lib","i":49}}}],"shapemaskId":"","xPos":24,"yPos":128,"tabIndex":11,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":164,"rotateYPos":100,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":328,"bottom":200,"altText":"Play/Pause\\nTimeline\\nReiniciar ecrã\\nVolume\\nVoltar ao menu\\nObjectivos do curso","pngfb":false,"pr":{"l":"Lib","i":48}},"html5data":{"xPos":0,"yPos":0,"width":328,"height":200,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":328,"height":200,"resume":true,"useHandCursor":true,"id":"6fLNGDgml4K"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5a1AGGT21Cq_1013326046","id":"01","linkId":"txt__default_5a1AGGT21Cq","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":420,"height":222,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"7.     Instruções de navegação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"8.      A sua pontuação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"9.      Instruções de interacções no ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"10.   Notas/curiosidade\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"11.   Voltar ao ecrã anterior\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":30,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"12.   Paginação \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":17,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"13.   Avançar para o próximo ecrã","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":33,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":225,"pngfb":false,"pr":{"l":"Lib","i":51}}}],"shapemaskId":"","xPos":320,"yPos":128,"tabIndex":12,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":220,"rotateYPos":116,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":440,"bottom":232,"altText":"7.     Instruções de navegação\\n8.      A sua pontuação\\n9.      Instruções de interacções no ecrã\\n10.   Notas/curiosidade\\n11.   Voltar ao ecrã anterior\\n12.   Paginação \\n13.   Avançar para o próximo ecrã","pngfb":false,"pr":{"l":"Lib","i":50}},"html5data":{"xPos":0,"yPos":0,"width":440,"height":232,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":440,"height":232,"resume":true,"useHandCursor":true,"id":"5a1AGGT21Cq"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6ex489sIWwL_1502302535","id":"01","linkId":"txt__default_6ex489sIWwL","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":428,"height":363,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":136,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Ao finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":133,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":153,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":435,"bottom":368,"pngfb":false,"pr":{"l":"Lib","i":53}}}],"shapemaskId":"","xPos":808,"yPos":168,"tabIndex":13,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":224,"rotateYPos":186.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":448,"bottom":373,"altText":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\nAo finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\nA pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","pngfb":false,"pr":{"l":"Lib","i":52}},"html5data":{"xPos":0,"yPos":0,"width":448,"height":373,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":448,"height":373,"resume":true,"useHandCursor":true,"id":"6ex489sIWwL"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5Xlh34Ekb5w"}],"showAnimationId":"","lmsId":"Slide6","width":1280,"height":720,"resume":false,"background":{"type":"fill","fill":{"type":"linear","rotation":90,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}},"id":"6ZL71VFFuhQ","events":[{"kind":"onslidestart","actions":[{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"gte","valuea":"_player.#3_6","typea":"var","valueb":3,"typeb":"number"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6oUaUG5iJUR.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6oUaUG5iJUR"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6oUaUG5iJUR.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6oUaUG5iJUR.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6oUaUG5iJUR"}}],"elseActions":[{"kind":"adjustvar","variable":"6oUaUG5iJUR._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"6oUaUG5iJUR._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6oUaUG5iJUR.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6oUaUG5iJUR"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6oUaUG5iJUR"}}]}]}]},{"kind":"onbeforeslidein","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"npnxnanbnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"npnxnanbnsnfns10110000101"}]}]},{"kind":"onvarchanged","varname":"_player.3_6","priority":0,"actions":[{"kind":"if_action","condition":{"statement":{"kind":"and","statements":[{"kind":"and","statements":[{"kind":"compare","operator":"gte","valuea":"_player.#3_6","typea":"var","valueb":3,"typeb":"number"},{"kind":"compare","operator":"eq","valuea":"6U340bcyWS7.#_visited","typea":"var","valueb":true,"typeb":"boolean"},{"kind":"compare","operator":"eq","valuea":"6MOJ84E0jw1.#_visited","typea":"var","valueb":true,"typeb":"boolean"},{"kind":"compare","operator":"eq","valuea":"5iIJUEgtwxb.#_visited","typea":"var","valueb":true,"typeb":"boolean"}]}]}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6oUaUG5iJUR.$OnStage","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6oUaUG5iJUR"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6oUaUG5iJUR.#_state","typea":"var","valueb":"_default","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"6oUaUG5iJUR.ActGrpClearStateVars"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6oUaUG5iJUR"}}],"elseActions":[{"kind":"adjustvar","variable":"6oUaUG5iJUR._state","operator":"set","value":{"type":"string","value":"_default"}},{"kind":"adjustvar","variable":"6oUaUG5iJUR._disabled","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"6oUaUG5iJUR.ActGrpClearStateVars"},{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"6oUaUG5iJUR"}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"6oUaUG5iJUR"}}]}]}]},{"kind":"ontransitionin","actions":[{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"set","value":{"type":"string","value":"_player."}},{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"add","value":{"type":"property","value":"$AbsoluteId"}}]}]}');