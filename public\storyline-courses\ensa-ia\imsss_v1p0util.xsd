<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:simpleType name = "childActivityType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "all"/>
			<xs:enumeration value = "any"/>
			<xs:enumeration value = "none"/>
			<xs:enumeration value = "atLeastCount"/>
			<xs:enumeration value = "atLeastPercent"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "conditionCombinationType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "all"/>
			<xs:enumeration value = "any"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "conditionOperatorType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "not"/>
			<xs:enumeration value = "noOp"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "measureType">
		<xs:annotation>
			<xs:documentation>A decimal value with AT LEAST 4 significant decimal digits between -1 and 1</xs:documentation>
		</xs:annotation>
		<xs:restriction base = "xs:decimal">
			<xs:maxInclusive value = "1"/>
			<xs:minInclusive value = "-1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "percentType">
		<xs:restriction base = "xs:decimal">
			<xs:maxInclusive value = "1"/>
			<xs:minInclusive value = "0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "rollupRuleConditionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "satisfied"/>
			<xs:enumeration value = "objectiveStatusKnown"/>
			<xs:enumeration value = "objectiveMeasureKnown"/>
			<xs:enumeration value = "completed"/>
			<xs:enumeration value = "activityProgressKnown"/>
			<xs:enumeration value = "attempted"/>
			<xs:enumeration value = "attemptLimitExceeded"/>
			<xs:enumeration value = "timeLimitExceeded"/>
			<xs:enumeration value = "outsideAvailableTimeRange"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "rollupActionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "satisfied"/>
			<xs:enumeration value = "notSatisfied"/>
			<xs:enumeration value = "completed"/>
			<xs:enumeration value = "incomplete"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "sequencingRuleConditionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "satisfied"/>
			<xs:enumeration value = "objectiveStatusKnown"/>
			<xs:enumeration value = "objectiveMeasureKnown"/>
			<xs:enumeration value = "objectiveMeasureGreaterThan"/>
			<xs:enumeration value = "objectiveMeasureLessThan"/>
			<xs:enumeration value = "completed"/>
			<xs:enumeration value = "activityProgressKnown"/>
			<xs:enumeration value = "attempted"/>
			<xs:enumeration value = "attemptLimitExceeded"/>
			<xs:enumeration value = "timeLimitExceeded"/>
			<xs:enumeration value = "outsideAvailableTimeRange"/>
			<xs:enumeration value = "always"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "weightType">
		<xs:restriction base = "xs:decimal">
			<xs:maxInclusive value = "1"/>
			<xs:minInclusive value = "0"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "randomTimingType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "never"/>
			<xs:enumeration value = "once"/>
			<xs:enumeration value = "onEachNewAttempt"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>