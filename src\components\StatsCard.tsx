import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  iconColor: string;
  delay?: string;
}

const StatsCard: React.FC<StatsCardProps> = React.memo(({ 
  title, 
  value, 
  icon: Icon, 
  iconColor,
  delay = '0s'
}) => {
  return (
    <Card 
      className="shadow-card card-interactive animate-scale-in" 
      style={{ animationDelay: delay }}
    >
      <CardContent className="p-6">
        <div className="flex items-center gap-4">
          <div className={`w-12 h-12 ${iconColor} rounded-xl flex items-center justify-center hover-glow transition-all duration-300`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

StatsCard.displayName = 'StatsCard';

export default StatsCard;
