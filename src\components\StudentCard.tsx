import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Student } from '@/data/mockData';
import { User, Eye, Edit, MoreHorizontal, Trash2 } from 'lucide-react';

interface StudentCardProps {
  student: Student;
  onEdit: (student: Student) => void;
  onDelete: (student: Student) => void;
}

const StudentCard: React.FC<StudentCardProps> = React.memo(({ 
  student, 
  onEdit, 
  onDelete 
}) => {
  const handleEdit = React.useCallback(() => {
    onEdit(student);
  }, [student, onEdit]);

  const handleDelete = React.useCallback(() => {
    onDelete(student);
  }, [student, onDelete]);

  const handleView = React.useCallback(() => {
    window.open(`/student/${student.id}`, '_blank');
  }, [student.id]);

  const getStatusColor = React.useMemo(() => {
    switch (student.status) {
      case 'active': return 'bg-success/10 text-success border-success/20';
      case 'inactive': return 'bg-muted/50 text-muted-foreground border-muted';
      case 'suspended': return 'bg-destructive/10 text-destructive border-destructive/20';
      default: return 'bg-muted/50 text-muted-foreground border-muted';
    }
  }, [student.status]);

  const getStatusLabel = React.useMemo(() => {
    switch (student.status) {
      case 'active': return 'Ativo';
      case 'inactive': return 'Inativo';
      case 'suspended': return 'Suspenso';
      default: return student.status;
    }
  }, [student.status]);

  return (
    <Card className="shadow-card animate-fade-in">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center hover-glow transition-all duration-300">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold">{student.name}</h3>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getStatusColor}`}
                >
                  {getStatusLabel}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">{student.email}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="text-right">
              <Button 
                variant="outline" 
                size="sm" 
                className="hover-lift focus-ring"
                onClick={handleView}
              >
                <Eye className="w-4 h-4 mr-2" />
                Ver
              </Button>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="hover-lift focus-ring">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40 bg-popover border shadow-lg animate-scale-in">
                <DropdownMenuItem 
                  onClick={handleEdit}
                  className="cursor-pointer hover:bg-accent transition-colors duration-200 focus-ring"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="cursor-pointer text-destructive focus:text-destructive hover:bg-destructive/10 transition-colors duration-200 focus-ring"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <div className="mt-4 flex flex-wrap gap-4 text-sm text-muted-foreground">
          <div>Cursos: {student.enrolledCourses.length}</div>
          <div>Cadastrado: {new Date(student.createdAt).toLocaleDateString('pt-PT')}</div>
          {student.lastAccess && (
            <div>Último acesso: {new Date(student.lastAccess).toLocaleDateString('pt-PT')}</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

StudentCard.displayName = 'StudentCard';

export default StudentCard;
