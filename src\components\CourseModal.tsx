import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Course, getStoredData, setStoredData } from '@/data/mockData';
import { BookOpen, Clock, User, Plus, X } from 'lucide-react';

interface CourseModalProps {
  isOpen: boolean;
  onClose: () => void;
  course?: Course | null;
  onSave: (course: Course) => void;
}

interface ModuleForm {
  id: string;
  title: string;
  lessons: LessonForm[];
}

interface LessonForm {
  id: string;
  title: string;
  content: string;
  type: 'video' | 'text';
  duration?: string;
}

const CourseModal: React.FC<CourseModalProps> = ({ isOpen, onClose, course, onSave }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  
  // Formulário principal
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    duration: '',
  });
  
  // Módulos e lições
  const [modules, setModules] = useState<ModuleForm[]>([]);
  
  // Carregar dados do curso para edição
  useEffect(() => {
    if (course) {
      setFormData({
        title: course.title,
        description: course.description,
        instructor: course.instructor,
        duration: course.duration,
      });
      
      setModules(course.modules.map(module => ({
        id: module.id,
        title: module.title,
        lessons: module.lessons.map(lesson => ({
          id: lesson.id,
          title: lesson.title,
          content: lesson.content,
          type: lesson.type,
          duration: lesson.duration || '',
        }))
      })));
    } else {
      // Resetar formulário para novo curso
      setFormData({
        title: '',
        description: '',
        instructor: '',
        duration: '',
      });
      setModules([]);
    }
  }, [course, isOpen]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addModule = () => {
    const newModule: ModuleForm = {
      id: `m${Date.now()}`,
      title: '',
      lessons: []
    };
    setModules(prev => [...prev, newModule]);
  };

  const updateModule = (moduleIndex: number, field: string, value: string) => {
    setModules(prev => prev.map((module, index) => 
      index === moduleIndex ? { ...module, [field]: value } : module
    ));
  };

  const removeModule = (moduleIndex: number) => {
    setModules(prev => prev.filter((_, index) => index !== moduleIndex));
  };

  const addLesson = (moduleIndex: number) => {
    const newLesson: LessonForm = {
      id: `l${Date.now()}`,
      title: '',
      content: '',
      type: 'text',
      duration: ''
    };
    
    setModules(prev => prev.map((module, index) => 
      index === moduleIndex 
        ? { ...module, lessons: [...module.lessons, newLesson] }
        : module
    ));
  };

  const updateLesson = (moduleIndex: number, lessonIndex: number, field: string, value: string) => {
    setModules(prev => prev.map((module, mIndex) => 
      mIndex === moduleIndex 
        ? {
            ...module,
            lessons: module.lessons.map((lesson, lIndex) => 
              lIndex === lessonIndex ? { ...lesson, [field]: value } : lesson
            )
          }
        : module
    ));
  };

  const removeLesson = (moduleIndex: number, lessonIndex: number) => {
    setModules(prev => prev.map((module, mIndex) => 
      mIndex === moduleIndex 
        ? { ...module, lessons: module.lessons.filter((_, lIndex) => lIndex !== lessonIndex) }
        : module
    ));
  };

  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      toast({
        title: "Erro de validação",
        description: "O título do curso é obrigatório.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.description.trim()) {
      toast({
        title: "Erro de validação", 
        description: "A descrição do curso é obrigatória.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.instructor.trim()) {
      toast({
        title: "Erro de validação",
        description: "O nome do instrutor é obrigatório.",
        variant: "destructive",
      });
      return false;
    }

    if (modules.length === 0) {
      toast({
        title: "Erro de validação",
        description: "O curso deve ter pelo menos um módulo.",
        variant: "destructive",
      });
      return false;
    }

    for (let i = 0; i < modules.length; i++) {
      const module = modules[i];
      if (!module.title.trim()) {
        toast({
          title: "Erro de validação",
          description: `O título do módulo ${i + 1} é obrigatório.`,
          variant: "destructive",
        });
        return false;
      }

      if (module.lessons.length === 0) {
        toast({
          title: "Erro de validação",
          description: `O módulo "${module.title}" deve ter pelo menos uma lição.`,
          variant: "destructive",
        });
        return false;
      }

      for (let j = 0; j < module.lessons.length; j++) {
        const lesson = module.lessons[j];
        if (!lesson.title.trim()) {
          toast({
            title: "Erro de validação",
            description: `O título da lição ${j + 1} no módulo "${module.title}" é obrigatório.`,
            variant: "destructive",
          });
          return false;
        }
      }
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const courseData: Course = {
        id: course?.id || `course_${Date.now()}`,
        title: formData.title.trim(),
        description: formData.description.trim(),
        instructor: formData.instructor.trim(),
        duration: formData.duration.trim() || '0 horas',
        students: course?.students || [],
        createdAt: course?.createdAt || new Date().toISOString(),
        modules: modules.map(module => ({
          id: module.id,
          title: module.title.trim(),
          lessons: module.lessons.map(lesson => ({
            id: lesson.id,
            title: lesson.title.trim(),
            content: lesson.content.trim() || 'Conteúdo da lição...',
            type: lesson.type,
            duration: lesson.duration || undefined,
            completed: false
          }))
        }))
      };

      // Salvar no localStorage
      const courses = getStoredData<Course[]>('ecurso_courses', []);
      const existingIndex = courses.findIndex(c => c.id === courseData.id);
      
      if (existingIndex >= 0) {
        courses[existingIndex] = courseData;
      } else {
        courses.push(courseData);
      }
      
      setStoredData('ecurso_courses', courses);

      onSave(courseData);
      
      toast({
        title: course ? "Curso atualizado!" : "Curso criado!",
        description: course 
          ? "As alterações foram salvas com sucesso." 
          : "O novo curso foi adicionado à plataforma.",
      });

      onClose();
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Ocorreu um erro ao salvar o curso. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto animate-scale-in">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-primary" />
            {course ? 'Editar Curso' : 'Novo Curso'}
          </DialogTitle>
          <DialogDescription>
            {course 
              ? 'Edite as informações do curso e seus módulos.' 
              : 'Preencha as informações para criar um novo curso.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informações básicas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Título do Curso *</Label>
              <Input
                id="title"
                placeholder="Ex: Introdução ao React"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="focus-ring"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="instructor">Instrutor *</Label>
              <Input
                id="instructor"
                placeholder="Ex: Prof. João Silva"
                value={formData.instructor}
                onChange={(e) => handleInputChange('instructor', e.target.value)}
                className="focus-ring"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição *</Label>
            <Textarea
              id="description"
              placeholder="Descreva o que os alunos aprenderão neste curso..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="min-h-[100px] focus-ring"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="duration">Duração Estimada</Label>
            <Input
              id="duration"
              placeholder="Ex: 40 horas"
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', e.target.value)}
              className="focus-ring"
            />
          </div>

          {/* Módulos */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Módulos do Curso</h3>
              <Button
                type="button"
                onClick={addModule}
                variant="outline"
                size="sm"
                className="hover-lift focus-ring"
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Módulo
              </Button>
            </div>

            {modules.map((module, moduleIndex) => (
              <div key={module.id} className="border rounded-lg p-4 space-y-4 card-gradient">
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-2">
                    <Label>Título do Módulo {moduleIndex + 1} *</Label>
                    <Input
                      placeholder="Ex: Fundamentos do React"
                      value={module.title}
                      onChange={(e) => updateModule(moduleIndex, 'title', e.target.value)}
                      className="focus-ring"
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={() => removeModule(moduleIndex)}
                    variant="ghost"
                    size="sm"
                    className="ml-2 text-destructive hover:bg-destructive/10 focus-ring"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                {/* Lições do módulo */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Lições</h4>
                    <Button
                      type="button"
                      onClick={() => addLesson(moduleIndex)}
                      variant="outline"
                      size="sm"
                      className="hover-lift focus-ring"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Lição
                    </Button>
                  </div>

                  {module.lessons.map((lesson, lessonIndex) => (
                    <div key={lesson.id} className="border rounded p-3 bg-background/50 space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Lição {lessonIndex + 1}</span>
                        <Button
                          type="button"
                          onClick={() => removeLesson(moduleIndex, lessonIndex)}
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:bg-destructive/10 focus-ring"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="space-y-1">
                          <Label>Título da Lição *</Label>
                          <Input
                            placeholder="Ex: Componentes e Props"
                            value={lesson.title}
                            onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'title', e.target.value)}
                            className="focus-ring"
                          />
                        </div>

                        <div className="space-y-1">
                          <Label>Tipo</Label>
                          <Select
                            value={lesson.type}
                            onValueChange={(value) => updateLesson(moduleIndex, lessonIndex, 'type', value)}
                          >
                            <SelectTrigger className="focus-ring">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="text">Texto</SelectItem>
                              <SelectItem value="video">Vídeo</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {lesson.type === 'video' && (
                        <div className="space-y-1">
                          <Label>Duração do Vídeo</Label>
                          <Input
                            placeholder="Ex: 15 min"
                            value={lesson.duration}
                            onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'duration', e.target.value)}
                            className="focus-ring"
                          />
                        </div>
                      )}

                      <div className="space-y-1">
                        <Label>Conteúdo</Label>
                        <Textarea
                          placeholder="Conteúdo da lição..."
                          value={lesson.content}
                          onChange={(e) => updateLesson(moduleIndex, lessonIndex, 'content', e.target.value)}
                          className="min-h-[80px] focus-ring"
                        />
                      </div>
                    </div>
                  ))}

                  {module.lessons.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      <p>Nenhuma lição adicionada ainda.</p>
                      <p className="text-sm">Clique em "Lição" para adicionar a primeira lição.</p>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {modules.length === 0 && (
              <div className="text-center py-8 text-muted-foreground border-2 border-dashed rounded-lg">
                <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Nenhum módulo adicionado ainda.</p>
                <p className="text-sm">Clique em "Adicionar Módulo" para começar.</p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="focus-ring"
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={loading}
            className="btn-primary-enhanced focus-ring"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 loading-spinner mr-2" />
                Salvando...
              </>
            ) : (
              <>
                <BookOpen className="w-4 h-4 mr-2" />
                {course ? 'Salvar Alterações' : 'Criar Curso'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CourseModal;
