import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Course } from '@/data/mockData';
import { BookOpen, Eye, Edit, MoreHorizontal, Trash2 } from 'lucide-react';

interface CourseCardProps {
  course: Course;
  onEdit: (course: Course) => void;
  onDelete: (course: Course) => void;
}

const CourseCard: React.FC<CourseCardProps> = React.memo(({ 
  course, 
  onEdit, 
  onDelete 
}) => {
  const handleEdit = React.useCallback(() => {
    onEdit(course);
  }, [course, onEdit]);

  const handleDelete = React.useCallback(() => {
    onDelete(course);
  }, [course, onDelete]);

  const handleView = React.useCallback(() => {
    window.open(`/course/${course.id}`, '_blank');
  }, [course.id]);

  return (
    <Card className="shadow-card animate-fade-in">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-primary" />
                {course.title}
              </h3>
              <p className="text-sm text-muted-foreground">{course.description}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="text-right">
              <Button 
                variant="outline" 
                size="sm" 
                className="hover-lift focus-ring"
                onClick={handleView}
              >
                <Eye className="w-4 h-4 mr-2" />
                Ver
              </Button>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="hover-lift focus-ring">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40 bg-popover border shadow-lg animate-scale-in">
                <DropdownMenuItem 
                  onClick={handleEdit}
                  className="cursor-pointer hover:bg-accent transition-colors duration-200 focus-ring"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="cursor-pointer text-destructive focus:text-destructive hover:bg-destructive/10 transition-colors duration-200 focus-ring"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <div className="mt-4 flex flex-wrap gap-4 text-sm text-muted-foreground">
          <div>Instrutor: {course.instructor}</div>
          <div>Duração: {course.duration}</div>
          <div>Alunos: {course.students.length}</div>
          <div>{course.modules.length} módulos</div>
        </div>
      </CardContent>
    </Card>
  );
});

CourseCard.displayName = 'CourseCard';

export default CourseCard;
