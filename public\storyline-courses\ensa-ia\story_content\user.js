function ExecuteScript(strId)
{
  switch (strId)
  {
      case "6JVkPJ5NBlo":
        Script1();
        break;
      case "5vQaNYv0RzK":
        Script2();
        break;
  }
}

function Script1()
{
  var player = GetPlayer();

function findLMSAPI(win) {
if (win.hasOwnProperty("GetStudentID")) return win;

else if (win.parent == win) return null;

else return findLM<PERSON>PI(win.parent);
}

var lmsAPI = findLMSAPI(this);
var myName = lmsAPI.GetStudentName();
var array = myName.split(',');
var Formando = array[1];
player.SetVar("Formando", Formando);
}

function Script2()
{
  var end = Date.now() + (15 * 1000);

// go Buckeyes!

var colors = ['#d35b40', '#00897b'];


(function frame() {
  
confetti({
    particleCount: 3,
    angle: 60,
    spread: 55,
    origin: { x: 0 },
    colors: colors
  
});
  
confetti({
    particleCount: 3,
    angle: 120,
    spread: 55,
    origin: { x: 1 },
    colors: colors
  });

  
if (Date.now() < end) {
    requestAnimationFrame(frame);
  }
}());
}

