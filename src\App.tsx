import React, { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import Layout from "@/components/Layout";
import LoadingSpinner from "@/components/LoadingSpinner";

// Lazy load pages for better performance
const Index = React.lazy(() => import("./pages/Index"));
const Login = React.lazy(() => import("./pages/Login"));
const StudentDashboard = React.lazy(() => import("./pages/StudentDashboard"));
const AdminDashboard = React.lazy(() => import("./pages/AdminDashboard"));
const CourseDetail = React.lazy(() => import("./pages/CourseDetail"));
const StudentProfile = React.lazy(() => import("./pages/StudentProfile"));
const Settings = React.lazy(() => import("./pages/Settings"));
const Reports = React.lazy(() => import("./pages/Reports"));
const ENSACourse = React.lazy(() => import("./pages/ENSACourse"));
const NotFound = React.lazy(() => import("./pages/NotFound"));

const queryClient = new QueryClient();

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Carregando...</p>
        </div>
      </div>
    );
  }
  
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  return <Layout>{children}</Layout>;
};

const DashboardRoute = () => {
  const { user } = useAuth();
  
  if (user?.type === 'admin') {
    return <AdminDashboard />;
  }
  
  return <StudentDashboard />;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Suspense fallback={<LoadingSpinner text="Carregando aplicação..." />}>
            <Routes>
              <Route
                path="/login"
                element={
                  <Suspense fallback={<LoadingSpinner text="Carregando login..." />}>
                    <Login />
                  </Suspense>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando dashboard..." />}>
                      <DashboardRoute />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/course/ensa-ia"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando curso ENSA..." />}>
                      <ENSACourse />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/course/:id"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando curso..." />}>
                      <CourseDetail />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/student/:id"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando perfil..." />}>
                      <StudentProfile />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando configurações..." />}>
                      <Settings />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/reports"
                element={
                  <ProtectedRoute>
                    <Suspense fallback={<LoadingSpinner text="Carregando relatórios..." />}>
                      <Reports />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Navigate to="/dashboard" replace />
                  </ProtectedRoute>
                }
              />
              <Route
                path="*"
                element={
                  <Suspense fallback={<LoadingSpinner text="Carregando página..." />}>
                    <NotFound />
                  </Suspense>
                }
              />
            </Routes>
          </Suspense>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
