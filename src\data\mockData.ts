export interface Course {
  id: string;
  title: string;
  description: string;
  instructor: string;
  duration: string;
  modules: Module[];
  students: string[];
  createdAt: string;
}

export interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
  quiz?: Quiz;
}

export interface Lesson {
  id: string;
  title: string;
  content: string;
  type: 'video' | 'text';
  duration?: string;
  completed?: boolean;
}

export interface Quiz {
  id: string;
  title: string;
  questions: Question[];
}

export interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: number;
}

export interface Student {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  enrolledCourses: string[];
  createdAt: string;
  lastAccess?: string;
  status: 'active' | 'inactive' | 'suspended';
}

export interface StudentProgress {
  studentId: string;
  courseId: string;
  completedLessons: string[];
  quizScores: { [quizId: string]: number };
  progress: number;
  lastAccessed?: string;
  timeSpent?: number; // em minutos
}

// Mock data
export const mockCourses: Course[] = [
  {
    id: '1',
    title: 'Introdução ao JavaScript',
    description: 'Aprenda os fundamentos da linguagem JavaScript do zero.',
    instructor: 'Prof. <PERSON>',
    duration: '40 horas',
    students: ['1'],
    createdAt: '2024-01-01',
    modules: [
      {
        id: 'm1',
        title: 'Fundamentos',
        lessons: [
          {
            id: 'l1',
            title: 'O que é JavaScript?',
            content: 'JavaScript é uma linguagem de programação versátil...',
            type: 'text'
          },
          {
            id: 'l2',
            title: 'Variáveis e Tipos',
            content: 'Nesta aula vamos aprender sobre variáveis...',
            type: 'video',
            duration: '15 min'
          }
        ],
        quiz: {
          id: 'q1',
          title: 'Quiz - Fundamentos',
          questions: [
            {
              id: 'q1_1',
              text: 'O que significa JavaScript?',
              options: [
                'Uma linguagem de programação',
                'Uma linguagem de marcação',
                'Um banco de dados',
                'Um sistema operacional'
              ],
              correctAnswer: 0
            }
          ]
        }
      }
    ]
  },
  {
    id: '2',
    title: 'React Avançado',
    description: 'Técnicas avançadas para desenvolvimento com React.',
    instructor: 'Prof. Ana Costa',
    duration: '60 horas',
    students: ['1'],
    createdAt: '2024-02-01',
    modules: [
      {
        id: 'm2',
        title: 'Hooks Avançados',
        lessons: [
          {
            id: 'l3',
            title: 'useCallback e useMemo',
            content: 'Otimização de performance com hooks...',
            type: 'video',
            duration: '25 min'
          }
        ]
      }
    ]
  },
  {
    id: 'ensa-ia',
    title: 'Curso ENSA de Inteligência Artificial',
    description: 'Curso interativo desenvolvido pela ENSA com tecnologia Storyline sobre fundamentos de IA, Machine Learning e aplicações práticas.',
    instructor: 'ENSA - Escola Nacional de Seguros',
    duration: '30 horas',
    students: ['1', '2', '4'],
    createdAt: '2024-08-01',
    modules: [
      {
        id: 'ensa-m1',
        title: 'Introdução à Inteligência Artificial',
        lessons: [
          {
            id: 'ensa-l1',
            title: 'Fundamentos de IA',
            content: 'Conteúdo interativo Storyline sobre IA...',
            type: 'interactive',
            duration: '20 min'
          },
          {
            id: 'ensa-l2',
            title: 'História e Evolução da IA',
            content: 'Timeline interativa da evolução da IA...',
            type: 'interactive',
            duration: '15 min'
          }
        ]
      },
      {
        id: 'ensa-m2',
        title: 'Machine Learning',
        lessons: [
          {
            id: 'ensa-l3',
            title: 'Algoritmos de Aprendizado',
            content: 'Simulações interativas de algoritmos...',
            type: 'interactive',
            duration: '25 min'
          },
          {
            id: 'ensa-l4',
            title: 'Redes Neurais',
            content: 'Visualização interativa de redes neurais...',
            type: 'interactive',
            duration: '30 min'
          }
        ]
      },
      {
        id: 'ensa-m3',
        title: 'Aplicações Práticas',
        lessons: [
          {
            id: 'ensa-l5',
            title: 'IA no Mercado de Seguros',
            content: 'Casos práticos no setor de seguros...',
            type: 'interactive',
            duration: '20 min'
          },
          {
            id: 'ensa-l6',
            title: 'Projeto Final',
            content: 'Desenvolvimento de projeto prático...',
            type: 'interactive',
            duration: '40 min'
          }
        ]
      }
    ]
  }
];

// Mock students data
export const mockStudents: Student[] = [
  {
    id: '1',
    name: 'João Silva',
    email: '<EMAIL>',
    phone: '+351 912 345 678',
    enrolledCourses: ['1', '2', 'ensa-ia'],
    createdAt: '2024-01-15',
    lastAccess: '2024-08-25',
    status: 'active'
  },
  {
    id: '2',
    name: 'Ana Costa',
    email: '<EMAIL>',
    phone: '+351 923 456 789',
    enrolledCourses: ['1', 'ensa-ia'],
    createdAt: '2024-02-10',
    lastAccess: '2024-08-24',
    status: 'active'
  },
  {
    id: '3',
    name: 'Pedro Santos',
    email: '<EMAIL>',
    phone: '+351 934 567 890',
    enrolledCourses: [],
    createdAt: '2024-03-05',
    lastAccess: '2024-08-20',
    status: 'inactive'
  }
];

export const getStoredData = <T>(key: string, defaultValue: T): T => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch {
    return defaultValue;
  }
};

export const setStoredData = <T>(key: string, data: T): void => {
  localStorage.setItem(key, JSON.stringify(data));
};

// Cached version of getStoredData for better performance
export const getCachedData = <T>(key: string, defaultValue: T, ttl: number = 5 * 60 * 1000): T => {
  try {
    // Try to get from cache first
    const cached = localStorage.getItem(`cache_${key}`);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < ttl) {
        return data;
      }
    }

    // If not in cache or expired, get from storage and cache it
    const data = getStoredData(key, defaultValue);
    localStorage.setItem(`cache_${key}`, JSON.stringify({
      data,
      timestamp: Date.now()
    }));

    return data;
  } catch (error) {
    console.error(`Error with cached data for ${key}:`, error);
    return getStoredData(key, defaultValue);
  }
};

// Initialize localStorage with mock data if empty
export const initializeMockData = () => {
  if (!localStorage.getItem('ecurso_courses')) {
    setStoredData('ecurso_courses', mockCourses);
  }

  if (!localStorage.getItem('ecurso_students')) {
    setStoredData('ecurso_students', mockStudents);
  }

  if (!localStorage.getItem('ecurso_progress')) {
    const mockProgress: StudentProgress[] = [
      {
        studentId: '1',
        courseId: '1',
        completedLessons: ['l1'],
        quizScores: {},
        progress: 25,
        lastAccessed: '2024-08-25',
        timeSpent: 45
      },
      {
        studentId: '1',
        courseId: '2',
        completedLessons: [],
        quizScores: {},
        progress: 0,
        lastAccessed: '2024-08-24',
        timeSpent: 15
      },
      {
        studentId: '2',
        courseId: '1',
        completedLessons: ['l1', 'l2'],
        quizScores: { 'q1': 85 },
        progress: 75,
        lastAccessed: '2024-08-24',
        timeSpent: 120
      }
    ];
    setStoredData('ecurso_progress', mockProgress);
  }
};