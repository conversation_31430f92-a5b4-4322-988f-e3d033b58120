import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Course, Student, StudentProgress, getStoredData, initializeMockData } from '@/data/mockData';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  BookOpen, 
  ArrowLeft,
  Download,
  Calendar,
  Clock,
  Award,
  Target,
  Activity
} from 'lucide-react';

const Reports: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [courses, setCourses] = useState<Course[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [progress, setProgress] = useState<StudentProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = () => {
      initializeMockData();
      
      const storedCourses = getStoredData<Course[]>('ecurso_courses', []);
      const storedStudents = getStoredData<Student[]>('ecurso_students', []);
      const storedProgress = getStoredData<StudentProgress[]>('ecurso_progress', []);
      
      setCourses(storedCourses);
      setStudents(storedStudents);
      setProgress(storedProgress);
      setLoading(false);
    };

    loadData();
  }, []);

  const handleBack = () => {
    navigate('/dashboard');
  };

  // Cálculos de estatísticas
  const getOverallStats = () => {
    const totalLessons = courses.reduce((acc, course) => 
      acc + course.modules.reduce((moduleAcc, module) => 
        moduleAcc + module.lessons.length, 0
      ), 0
    );

    const completedLessons = progress.reduce((acc, p) => 
      acc + p.completedLessons.length, 0
    );

    const averageProgress = progress.length > 0 
      ? Math.round(progress.reduce((acc, p) => acc + p.progress, 0) / progress.length)
      : 0;

    const activeStudents = students.filter(s => s.status === 'active').length;

    return {
      totalCourses: courses.length,
      totalStudents: students.length,
      activeStudents,
      totalLessons,
      completedLessons,
      averageProgress,
      completionRate: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
    };
  };

  const getCourseStats = () => {
    return courses.map(course => {
      const courseProgress = progress.filter(p => p.courseId === course.id);
      const enrolledStudents = students.filter(s => s.enrolledCourses.includes(course.id));
      
      const totalLessons = course.modules.reduce((acc, module) => 
        acc + module.lessons.length, 0
      );
      
      const completedLessons = courseProgress.reduce((acc, p) => 
        acc + p.completedLessons.length, 0
      );
      
      const averageProgress = courseProgress.length > 0 
        ? Math.round(courseProgress.reduce((acc, p) => acc + p.progress, 0) / courseProgress.length)
        : 0;

      return {
        ...course,
        enrolledCount: enrolledStudents.length,
        totalLessons,
        completedLessons,
        averageProgress,
        completionRate: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
      };
    });
  };

  const getStudentStats = () => {
    return students.map(student => {
      const studentProgress = progress.filter(p => p.studentId === student.id);
      const enrolledCourses = courses.filter(c => student.enrolledCourses.includes(c.id));
      
      const totalLessons = enrolledCourses.reduce((acc, course) => 
        acc + course.modules.reduce((moduleAcc, module) => 
          moduleAcc + module.lessons.length, 0
        ), 0
      );
      
      const completedLessons = studentProgress.reduce((acc, p) => 
        acc + p.completedLessons.length, 0
      );
      
      const averageProgress = studentProgress.length > 0 
        ? Math.round(studentProgress.reduce((acc, p) => acc + p.progress, 0) / studentProgress.length)
        : 0;

      const totalTimeSpent = studentProgress.reduce((acc, p) => 
        acc + (p.timeSpent || 0), 0
      );

      return {
        ...student,
        enrolledCoursesCount: enrolledCourses.length,
        totalLessons,
        completedLessons,
        averageProgress,
        totalTimeSpent,
        completionRate: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
      };
    });
  };

  const stats = getOverallStats();
  const courseStats = getCourseStats();
  const studentStats = getStudentStats();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 loading-spinner mx-auto mb-4" />
          <p>Carregando relatórios...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Page Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="hover-lift focus-ring"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">Relatórios</h1>
                <p className="text-muted-foreground">Análise detalhada de desempenho e progresso</p>
              </div>
            </div>

            <Button className="btn-primary-enhanced focus-ring">
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container-responsive py-8">
        <Tabs defaultValue="overview" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="overview" className="transition-all duration-300 hover-lift focus-ring">
              <Activity className="w-4 h-4 mr-2" />
              Visão Geral
            </TabsTrigger>
            <TabsTrigger value="courses" className="transition-all duration-300 hover-lift focus-ring">
              <BookOpen className="w-4 h-4 mr-2" />
              Cursos
            </TabsTrigger>
            <TabsTrigger value="students" className="transition-all duration-300 hover-lift focus-ring">
              <Users className="w-4 h-4 mr-2" />
              Alunos
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 animate-slide-up">
            {/* Overall Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="shadow-card card-interactive animate-scale-in">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <BookOpen className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total de Cursos</p>
                      <p className="text-2xl font-bold">{stats.totalCourses}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.1s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <Users className="w-6 h-6 text-secondary" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Alunos Ativos</p>
                      <p className="text-2xl font-bold">{stats.activeStudents}/{stats.totalStudents}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <Target className="w-6 h-6 text-success" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Taxa de Conclusão</p>
                      <p className="text-2xl font-bold">{stats.completionRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card card-interactive animate-scale-in" style={{animationDelay: '0.3s'}}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-warning/10 rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                      <TrendingUp className="w-6 h-6 text-warning" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Progresso Médio</p>
                      <p className="text-2xl font-bold">{stats.averageProgress}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="shadow-card animate-fade-in">
                <CardHeader>
                  <CardTitle>Resumo de Atividade</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Lições Totais</span>
                    <span className="text-sm font-bold">{stats.totalLessons}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Lições Concluídas</span>
                    <span className="text-sm font-bold">{stats.completedLessons}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Taxa de Conclusão</span>
                    <span className="text-sm font-bold">{stats.completionRate}%</span>
                  </div>
                  <Progress value={stats.completionRate} className="w-full" />
                </CardContent>
              </Card>

              <Card className="shadow-card animate-fade-in">
                <CardHeader>
                  <CardTitle>Distribuição de Alunos</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm font-medium">Ativos</span>
                    </div>
                    <span className="text-sm font-bold">{stats.activeStudents}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
                      <span className="text-sm font-medium">Inativos</span>
                    </div>
                    <span className="text-sm font-bold">{stats.totalStudents - stats.activeStudents}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total</span>
                    <span className="text-sm font-bold">{stats.totalStudents}</span>
                  </div>
                  <Progress 
                    value={(stats.activeStudents / stats.totalStudents) * 100} 
                    className="w-full" 
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="courses" className="space-y-6 animate-slide-up">
            <div className="grid gap-4">
              {courseStats.map((course) => (
                <Card key={course.id} className="shadow-card card-interactive animate-fade-in">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                          <BookOpen className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold">{course.title}</h3>
                          <p className="text-sm text-muted-foreground">{course.instructor}</p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                            <span>{course.duration}</span>
                            <span>{course.totalLessons} lições</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                        <div>
                          <p className="text-sm text-muted-foreground">Alunos</p>
                          <p className="font-bold">{course.enrolledCount}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Progresso</p>
                          <p className="font-bold">{course.averageProgress}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Conclusão</p>
                          <p className="font-bold">{course.completionRate}%</p>
                        </div>
                        <div>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="hover-lift focus-ring"
                            onClick={() => navigate(`/course/${course.id}`)}
                          >
                            Ver Detalhes
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="students" className="space-y-6 animate-slide-up">
            <div className="grid gap-4">
              {studentStats.map((student) => (
                <Card key={student.id} className="shadow-card card-interactive animate-fade-in">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center hover-glow transition-all duration-300">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold">{student.name}</h3>
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                student.status === 'active' 
                                  ? 'bg-success/10 text-success border-success/20'
                                  : 'bg-muted/50 text-muted-foreground border-muted'
                              }`}
                            >
                              {student.status === 'active' ? 'Ativo' : 'Inativo'}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{student.email}</p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                            <span>{student.enrolledCoursesCount} cursos</span>
                            <span>{student.totalTimeSpent}min gastos</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                        <div>
                          <p className="text-sm text-muted-foreground">Progresso</p>
                          <p className="font-bold">{student.averageProgress}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Lições</p>
                          <p className="font-bold">{student.completedLessons}/{student.totalLessons}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Conclusão</p>
                          <p className="font-bold">{student.completionRate}%</p>
                        </div>
                        <div>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="hover-lift focus-ring"
                            onClick={() => navigate(`/student/${student.id}`)}
                          >
                            Ver Perfil
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default Reports;
