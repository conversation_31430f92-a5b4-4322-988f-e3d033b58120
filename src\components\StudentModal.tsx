import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { Student, Course, getStoredData, setStoredData } from '@/data/mockData';
import { User, Phone, Mail, BookOpen, UserCheck } from 'lucide-react';

interface StudentModalProps {
  isOpen: boolean;
  onClose: () => void;
  student?: Student | null;
  onSave: (student: Student) => void;
}

const StudentModal: React.FC<StudentModalProps> = ({ isOpen, onClose, student, onSave }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  
  // Formulário principal
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
  });
  
  // Cursos selecionados
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);

  // Carregar cursos disponíveis
  useEffect(() => {
    const courses = getStoredData<Course[]>('ecurso_courses', []);
    setAvailableCourses(courses);
  }, []);
  
  // Carregar dados do aluno para edição
  useEffect(() => {
    if (student) {
      setFormData({
        name: student.name,
        email: student.email,
        phone: student.phone || '',
        status: student.status,
      });
      setSelectedCourses(student.enrolledCourses);
    } else {
      // Resetar formulário para novo aluno
      setFormData({
        name: '',
        email: '',
        phone: '',
        status: 'active',
      });
      setSelectedCourses([]);
    }
  }, [student, isOpen]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCourseToggle = (courseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCourses(prev => [...prev, courseId]);
    } else {
      setSelectedCourses(prev => prev.filter(id => id !== courseId));
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast({
        title: "Erro de validação",
        description: "O nome do aluno é obrigatório.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.email.trim()) {
      toast({
        title: "Erro de validação", 
        description: "O email do aluno é obrigatório.",
        variant: "destructive",
      });
      return false;
    }

    // Validação básica de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: "Erro de validação",
        description: "Por favor, insira um email válido.",
        variant: "destructive",
      });
      return false;
    }

    // Verificar se email já existe (apenas para novos alunos ou se mudou o email)
    const students = getStoredData<Student[]>('ecurso_students', []);
    const emailExists = students.some(s => 
      s.email.toLowerCase() === formData.email.toLowerCase() && 
      s.id !== student?.id
    );
    
    if (emailExists) {
      toast({
        title: "Erro de validação",
        description: "Este email já está sendo usado por outro aluno.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const studentData: Student = {
        id: student?.id || `student_${Date.now()}`,
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim() || undefined,
        enrolledCourses: selectedCourses,
        createdAt: student?.createdAt || new Date().toISOString().split('T')[0],
        lastAccess: student?.lastAccess,
        status: formData.status,
      };

      // Salvar no localStorage
      const students = getStoredData<Student[]>('ecurso_students', []);
      const existingIndex = students.findIndex(s => s.id === studentData.id);
      
      if (existingIndex >= 0) {
        students[existingIndex] = studentData;
      } else {
        students.push(studentData);
      }
      
      setStoredData('ecurso_students', students);

      // Atualizar cursos com novos alunos matriculados
      const courses = getStoredData<Course[]>('ecurso_courses', []);
      const updatedCourses = courses.map(course => {
        const wasEnrolled = student?.enrolledCourses.includes(course.id) || false;
        const isEnrolled = selectedCourses.includes(course.id);
        
        let updatedStudents = [...course.students];
        
        if (!wasEnrolled && isEnrolled) {
          // Adicionar aluno ao curso
          if (!updatedStudents.includes(studentData.id)) {
            updatedStudents.push(studentData.id);
          }
        } else if (wasEnrolled && !isEnrolled) {
          // Remover aluno do curso
          updatedStudents = updatedStudents.filter(id => id !== studentData.id);
        }
        
        return { ...course, students: updatedStudents };
      });
      
      setStoredData('ecurso_courses', updatedCourses);

      onSave(studentData);
      
      toast({
        title: student ? "Aluno atualizado!" : "Aluno criado!",
        description: student 
          ? "As alterações foram salvas com sucesso." 
          : "O novo aluno foi adicionado à plataforma.",
      });

      onClose();
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Ocorreu um erro ao salvar o aluno. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Ativo';
      case 'inactive': return 'Inativo';
      case 'suspended': return 'Suspenso';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-success';
      case 'inactive': return 'text-muted-foreground';
      case 'suspended': return 'text-destructive';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto animate-scale-in">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5 text-primary" />
            {student ? 'Editar Aluno' : 'Novo Aluno'}
          </DialogTitle>
          <DialogDescription>
            {student 
              ? 'Edite as informações do aluno e seus cursos matriculados.' 
              : 'Preencha as informações para adicionar um novo aluno.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informações básicas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome Completo *</Label>
              <Input
                id="name"
                placeholder="Ex: João Silva"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="focus-ring"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                placeholder="Ex: <EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="focus-ring"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Telefone</Label>
              <Input
                id="phone"
                placeholder="Ex: +351 912 345 678"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="focus-ring"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger className="focus-ring">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">
                    <span className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-success rounded-full"></div>
                      Ativo
                    </span>
                  </SelectItem>
                  <SelectItem value="inactive">
                    <span className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                      Inativo
                    </span>
                  </SelectItem>
                  <SelectItem value="suspended">
                    <span className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-destructive rounded-full"></div>
                      Suspenso
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Cursos */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">Cursos Matriculados</h3>
            </div>

            {availableCourses.length > 0 ? (
              <div className="grid grid-cols-1 gap-3">
                {availableCourses.map((course) => (
                  <div key={course.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <Checkbox
                      id={`course-${course.id}`}
                      checked={selectedCourses.includes(course.id)}
                      onCheckedChange={(checked) => handleCourseToggle(course.id, checked as boolean)}
                      className="focus-ring"
                    />
                    <div className="flex-1">
                      <label 
                        htmlFor={`course-${course.id}`}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {course.title}
                      </label>
                      <p className="text-xs text-muted-foreground">
                        {course.instructor} • {course.duration}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground border-2 border-dashed rounded-lg">
                <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Nenhum curso disponível.</p>
                <p className="text-sm">Crie cursos primeiro para poder matricular alunos.</p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="focus-ring"
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={loading}
            className="btn-primary-enhanced focus-ring"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 loading-spinner mr-2" />
                Salvando...
              </>
            ) : (
              <>
                <UserCheck className="w-4 h-4 mr-2" />
                {student ? 'Salvar Alterações' : 'Criar Aluno'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StudentModal;
