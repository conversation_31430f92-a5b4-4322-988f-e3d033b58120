﻿window.globalProvideData('slide', '{"title":"Pick One","trackViews":true,"showMenuResultIcon":false,"viewGroupId":"","historyGroupId":"","videoZoom":"","scrolling":false,"transition":"appear","transDuration":0,"transDir":1,"wipeTrans":false,"slideLock":false,"navIndex":-1,"globalAudioId":"","thumbnailid":"","slideNumberInScene":10,"includeInSlideCounts":true,"presenterRef":{"id":"none"},"slideLayers":[{"enableSeek":true,"enableReplay":true,"timeline":{"duration":10750,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ZAEiIkViQt"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"62AXVY022VE"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5rjnGv43iTf"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5bTxdaqkbYB"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6lpPuOj2JI8"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5oAWIYCxwV0"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6DZBLA5Oyzc"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61P6lGLLWX1"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"62UoWz85XLd"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6lL3uPc3Vpa"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6WC4KQpwoOu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"61jub7Qy8OO"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5W3Xv2j6uLu"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5hha4NXNQWL"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ctqN84HwS5"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fjXMMrmwFm"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5v2NzpQVpmK"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5w10urQzp0f"}}]},{"kind":"ontimelinetick","time":1500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6j5RK56qg3w"}}]}]},"objects":[{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":37,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"61jub7Qy8OO_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":36,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":1}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":2}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"61jub7Qy8OO"}}],"clickdef":[{"kind":"objref","type":"string","value":"61jub7Qy8OO"}]}],"shapemaskId":"","xPos":10,"yPos":678,"tabIndex":50,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"61jub7Qy8OO","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"61jub7Qy8OO"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.5fgE044eYLG.6cac0NAnUiv"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"61jub7Qy8OO_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":41,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5W3Xv2j6uLu_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":40,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":3}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":4}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5W3Xv2j6uLu"}}],"clickdef":[{"kind":"objref","type":"string","value":"5W3Xv2j6uLu"}]}],"shapemaskId":"","xPos":90,"yPos":678,"tabIndex":52,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":2,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5W3Xv2j6uLu","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5W3Xv2j6uLu"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.5Xlh34Ekb5w"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5W3Xv2j6uLu_expandinglabel"}}]}]},{"kind":"stategroup","objects":[{"kind":"expandinglabel","animationtype":"full","showclosebutton":false,"contentheight":0,"borderwidth":0,"arrowxpos":20,"arrowypos":-18,"shapemaskId":"","xPos":-6,"yPos":32,"tabIndex":39,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":0,"rotateYPos":0,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":1,"scrolling":false,"shuffleLock":false,"colors":[{"kind":"color","name":"border","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}},{"kind":"color","name":"bg","fill":{"type":"linear","rotation":0,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":0,"stop":0}]}}],"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":0,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":0}},"html5data":{"xPos":0,"yPos":0,"width":0,"height":0,"strokewidth":0}},"width":40,"height":40,"resume":false,"useHandCursor":true,"id":"5hha4NXNQWL_expandinglabel","events":[{"kind":"onclickoutside","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$Expanded","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"_this"}}]}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":38,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":5}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":29,"bottom":29,"altText":"Marker","pngfb":false,"pr":{"l":"Lib","i":6}},"html5data":{"xPos":-1,"yPos":-1,"width":30,"height":30,"strokewidth":1}}}],"markerType":"none","width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL"}],"actionstates":[{"kind":"state","name":"_default","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]},{"kind":"state","name":"_default_Hover","actions":[{"kind":"setobjstate","stateRef":{"type":"string","value":"_default_Hover"},"objRef":{"type":"string","value":"5hha4NXNQWL"}}],"clickdef":[{"kind":"objref","type":"string","value":"5hha4NXNQWL"}]}],"shapemaskId":"","xPos":48,"yPos":678,"tabIndex":51,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":14,"rotateYPos":14,"scaleX":100,"scaleY":100,"alpha":100,"rotation":0,"depth":3,"scrolling":false,"shuffleLock":false,"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":28,"height":28,"resume":true,"useHandCursor":true,"id":"5hha4NXNQWL","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]},"_show":{"kind":"actiongroup","actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5hha4NXNQWL"}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"togglecontent","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}},{"kind":"show_slidelayer","hideOthers":"never","transition":"appear","objRef":{"type":"string","value":"_parent.6QtAxFNOoxm"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_show"},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"bringtofront","value":{"type":"string","value":"_this"}},{"kind":"object_action","command":"showcomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}},{"kind":"object_action","command":"hidecomplete","objRef":{"type":"string","value":"5hha4NXNQWL_expandinglabel"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_6ctqN84HwS5","type":"richvartext","xPos":10,"yPos":5,"xAccOffset":0,"yAccOffset":0,"width":212,"height":30,"device":false,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Pontuação: ","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"%_player.nota%","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}},{"text":"/20","style":{"fontSize":14,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":28,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":135,"yPos":672,"tabIndex":33,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":116,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":233,"bottom":41,"altText":"Pontuação: %_player.nota%/20","pngfb":false,"pr":{"l":"Lib","i":7}},"html5data":{"xPos":-2,"yPos":-2,"width":235,"height":43,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":232,"height":40,"resume":true,"useHandCursor":true,"id":"6ctqN84HwS5"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PYUvVg1zLz_545423499","id":"01","linkId":"txt__default_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":11}}},{"kind":"textdata","uniqueId":"6dBYEI4LvKs_2042009458","id":"02","linkId":"txt__default_Disabled_6fjXMMrmwFm","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Anterior","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":12}}}],"shapemaskId":"","xPos":944,"yPos":672,"tabIndex":34,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Anterior","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"6fjXMMrmwFm","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"history_prev"}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":17,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":true,"useHandCursor":true,"id":"5v2NzpQVpmK"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":15,"id":"01","url":"story_content/6aoJaGpyzi2_P_0_298_2000_1034.jpg","type":"normal","altText":"6786.jpg","width":2000,"height":1034,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":15,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":336,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":672,"altText":"6786.jpg","pngfb":false,"pr":{"l":"Lib","i":310}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":672,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":250,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":250,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":250,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":672,"resume":true,"useHandCursor":true,"id":"6lL3uPc3Vpa"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":0,"id":"01","url":"story_content/6o0jwAN0kJY.png","type":"normal","altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","width":472,"height":297,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":0,"yPos":-8,"tabIndex":16,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":47.5,"rotateYPos":30,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":95,"bottom":60,"altText":"f6995df4ca1be0633e823b05018bfaf0d7619e82-72.png","pngfb":false,"pr":{"l":"Lib","i":13}},"html5data":{"xPos":0,"yPos":0,"width":95,"height":60,"strokewidth":0}},"width":95,"height":60,"resume":true,"useHandCursor":true,"id":"6WC4KQpwoOu"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6VRwpTRwAwr_-1288622077","id":"01","linkId":"txt__default_5ZAEiIkViQt","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":62}}},{"kind":"textdata","uniqueId":"5t2LekqrAS3_-1680436045","id":"02","linkId":"txt__default_Disabled_5ZAEiIkViQt","type":"acctext","xPos":7,"yPos":2,"xAccOffset":7,"yAccOffset":2,"width":116,"height":36,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Próximo","style":{"fontSize":16,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#984807","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":104,"bottom":33,"pngfb":false,"pr":{"l":"Lib","i":63}}}],"shapemaskId":"","xPos":1141,"yPos":672,"tabIndex":32,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":65,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":8}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}},"states":[{"kind":"state","name":"_default_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":10}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}},{"kind":"state","name":"_default_Hover_Disabled","data":{"hotlinkId":"","accState":1,"vectorData":{"left":-3,"top":-3,"right":132,"bottom":42,"altText":"Próximo","pngfb":false,"pr":{"l":"Lib","i":9}},"html5data":{"xPos":-3,"yPos":-3,"width":135,"height":45,"strokewidth":3}}}],"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":130,"height":40,"resume":true,"useHandCursor":true,"id":"5ZAEiIkViQt","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":true,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpSetDisabledState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_disabled","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.6rTmS2JpDep.6aV3LVrn62Y"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5qiKmWHQWx7_1748208654","id":"01","linkId":"txt__default_62AXVY022VE","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":33,"height":66,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"9","style":{"fontSize":13.5,"fontIsBold":false,"foregroundColor":"#000000","linkColor":"#000000","fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":13.77,"descent":4.23,"leading":3.6,"underlinePosition":-0.702,"underlineThickness":1.44,"xHeight":10.296}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"center","listLevel":0,"tagType":"P"},"runs":[{"idx":0,"len":1,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":12,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":33,"bottom":53,"pngfb":false,"pr":{"l":"Lib","i":891}}}],"shapemaskId":"","xPos":32,"yPos":80,"tabIndex":25,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":26.5,"rotateYPos":38,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":54,"bottom":77,"altText":"9","pngfb":false,"pr":{"l":"Lib","i":311}},"html5data":{"xPos":-2,"yPos":-2,"width":56,"height":79,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":54,"bottom":77,"altText":"9","pngfb":false,"pr":{"l":"Lib","i":312}},"html5data":{"xPos":-2,"yPos":-2,"width":56,"height":79,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":53,"height":76,"resume":true,"useHandCursor":true,"id":"62AXVY022VE","variables":[{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"ActGrpUnchecked"}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5w10urQzp0f.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5w10urQzp0f._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5w10urQzp0f"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.6j5RK56qg3w.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.6j5RK56qg3w._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.6j5RK56qg3w"}}]}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpUnchecked"},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"radio","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5gA1sPKKFVT_-1673727136","id":"01","linkId":"txt__default_5w10urQzp0f","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1036,"height":49,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}},{"text":" Verdadeiro ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":590,"bottom":43,"pngfb":false,"pr":{"l":"Lib","i":501}}},{"kind":"textdata","uniqueId":"5pTxy5eio7z_-1537530960","id":"02","linkId":"txt__default_Selected_5w10urQzp0f","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1036,"height":49,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"  Verdadeiro ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"multiple","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":13,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":590,"bottom":43,"pngfb":false,"pr":{"l":"Lib","i":502}}}],"shapemaskId":"","xPos":112,"yPos":208,"tabIndex":27,"tabEnabled":true,"radioGroup":"Pick One","xOffset":0,"yOffset":0,"rotateXPos":528,"rotateYPos":29.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"  Verdadeiro ","pngfb":false,"pr":{"l":"Lib","i":316}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"  Verdadeiro ","pngfb":false,"pr":{"l":"Lib","i":317}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"  Verdadeiro ","pngfb":false,"pr":{"l":"Lib","i":318}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"  Verdadeiro ","pngfb":false,"pr":{"l":"Lib","i":318}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1056,"height":59,"resume":true,"useHandCursor":true,"id":"5w10urQzp0f","variables":[{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"ActGrpUnchecked"}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.62AXVY022VE.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.62AXVY022VE._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.62AXVY022VE"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.6j5RK56qg3w.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.6j5RK56qg3w._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.6j5RK56qg3w"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpUnchecked"},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"radio","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"5qI4JxLseB2_1614431358","id":"01","linkId":"txt__default_6j5RK56qg3w","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1036,"height":49,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Falso ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#000000","linkColor":"#000000","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":6,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":558,"bottom":43,"pngfb":false,"pr":{"l":"Lib","i":503}}},{"kind":"textdata","uniqueId":"5u2jUhIa5dE_308980059","id":"02","linkId":"txt__default_Selected_6j5RK56qg3w","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":1036,"height":49,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Falso ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"foregroundColor":"#FFFFFF","linkColor":"#FFFFFF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","lineSpacingRule":"multiple","lineSpacing":20,"spacingBefore":0,"spacingAfter":0,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":6,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":558,"bottom":43,"pngfb":false,"pr":{"l":"Lib","i":504}}}],"shapemaskId":"","xPos":112,"yPos":285,"tabIndex":28,"tabEnabled":true,"radioGroup":"Pick One","xOffset":0,"yOffset":0,"rotateXPos":528,"rotateYPos":29.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"Falso ","pngfb":false,"pr":{"l":"Lib","i":316}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"Falso ","pngfb":false,"pr":{"l":"Lib","i":317}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}},{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"Falso ","pngfb":false,"pr":{"l":"Lib","i":318}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}},{"kind":"state","name":"_default_Hover_Selected","data":{"hotlinkId":"","accState":16,"vectorData":{"left":-2,"top":-2,"right":1057,"bottom":60,"altText":"Falso ","pngfb":false,"pr":{"l":"Lib","i":318}},"html5data":{"xPos":-2,"yPos":-2,"width":1059,"height":62,"strokewidth":1}}}],"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1056,"height":59,"resume":true,"useHandCursor":true,"id":"6j5RK56qg3w","variables":[{"kind":"variable","name":"_checked","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetCheckedState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}},{"kind":"exe_actiongroup","id":"ActGrpUnchecked"}]},"ActGrpUnchecked":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.62AXVY022VE.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.62AXVY022VE._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.62AXVY022VE"}}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_parent.5w10urQzp0f.#_checked","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"adjustvar","variable":"_parent.5w10urQzp0f._checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_parent.5w10urQzp0f"}}]}]},"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":false}},{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpUnchecked"},{"kind":"adjustvar","variable":"_checked","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"6PLTD9R3Flv_729253435","id":"01","linkId":"txt__default_5rjnGv43iTf","type":"acctext","xPos":4,"yPos":2,"xAccOffset":4,"yAccOffset":2,"width":216,"height":44,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Submeter","style":{"fontSize":16,"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"justification":"center","tagType":"P"},"runs":[{"idx":0,"len":8,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":162,"bottom":42,"pngfb":false,"pr":{"l":"Lib","i":329}}}],"shapemaskId":"","xPos":544,"yPos":600,"tabIndex":29,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":112,"rotateYPos":24,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":225,"bottom":49,"altText":"Submeter","pngfb":false,"pr":{"l":"Lib","i":327}},"html5data":{"xPos":-2,"yPos":-2,"width":227,"height":51,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":225,"bottom":49,"altText":"Submeter","pngfb":false,"pr":{"l":"Lib","i":328}},"html5data":{"xPos":-2,"yPos":-2,"width":227,"height":51,"strokewidth":1}}}],"width":224,"height":48,"resume":true,"useHandCursor":true,"id":"5rjnGv43iTf","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"if_action","condition":{"statement":{"kind":"or","statements":[{"kind":"compare","operator":"eq","valuea":"_parent.5w10urQzp0f.#_checked","typea":"var","valueb":true,"typeb":"boolean"},{"kind":"compare","operator":"eq","valuea":"_parent.6j5RK56qg3w.#_checked","typea":"var","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"eval_interaction","id":"_parent.6SF9pMHnwVK"}],"elseActions":[{"kind":"gotoplay","window":"MessageWnd","wndtype":"normal","objRef":{"type":"string","value":"_player.MsgScene_6qX7xzvLyR5.InvalidPromptSlide"}}]}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5bTxdaqkbYB_-1679841786","id":"01","linkId":"txt__default_5bTxdaqkbYB","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":932,"height":55,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"\\tAs empresas de telecomunicações utilizam a IA para monitorizar redes e prever falhas técnicas.","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"flowDirection":"leftToRight","leadingMargin":72,"trailingMargin":0,"firstLineMargin":36,"justification":"left","listLevel":1,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":95,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":863,"bottom":60,"pngfb":false,"pr":{"l":"Lib","i":893}}}],"shapemaskId":"","xPos":88,"yPos":80,"tabIndex":26,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":476,"rotateYPos":32.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":952,"bottom":65,"altText":"\\tAs empresas de telecomunicações utilizam a IA para monitorizar redes e prever falhas técnicas.","pngfb":false,"pr":{"l":"Lib","i":892}},"html5data":{"xPos":0,"yPos":0,"width":952,"height":65,"strokewidth":0}},"width":952,"height":65,"resume":true,"useHandCursor":true,"id":"5bTxdaqkbYB"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6lpPuOj2JI8_-1514610011","id":"01","linkId":"txt__default_6lpPuOj2JI8","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":489,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"(Seleccione a opção correcta e clique em “submeter”)","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#000000","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":85.333,"lineSpacingRule":"multiple","lineSpacing":21,"spacingBefore":0,"spacingAfter":8,"listStyle":{"listType":"none","listTypeFormat":"parentheses","size":100,"bulletFont":"Arial"},"tagType":"P"},"runs":[{"idx":0,"len":52,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":470,"bottom":31,"pngfb":false,"pr":{"l":"Lib","i":330}}}],"shapemaskId":"","xPos":386,"yPos":672,"tabIndex":30,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":254.5,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":15,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":509,"bottom":40,"altText":"(Seleccione a opção correcta e clique em “submeter”)","pngfb":false,"pr":{"l":"Lib","i":66}},"html5data":{"xPos":-1,"yPos":-1,"width":510,"height":41,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":500,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":500,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":500,"easing":"linear","easingdir":"easein"}}]}],"width":509,"height":40,"resume":true,"useHandCursor":true,"id":"6lpPuOj2JI8"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5oAWIYCxwV0_212046182","id":"01","linkId":"txt__default_5oAWIYCxwV0","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":132,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"10/12","style":{"fontSize":14,"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":5,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":62,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":894}}}],"shapemaskId":"","xPos":1072,"yPos":672,"tabIndex":35,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":76,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":16,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":152,"bottom":33,"altText":"10/12","pngfb":false,"pr":{"l":"Lib","i":70}},"html5data":{"xPos":0,"yPos":0,"width":152,"height":33,"strokewidth":0}},"width":152,"height":33,"resume":true,"useHandCursor":true,"id":"5oAWIYCxwV0"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6DZBLA5Oyzc_-1355700797","id":"01","linkId":"txt__default_6DZBLA5Oyzc","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: TESTE FINAL","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":151,"bottom":28,"pngfb":false,"pr":{"l":"Lib","i":813}}}],"shapemaskId":"","xPos":128,"yPos":16,"tabIndex":23,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":17,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: TESTE FINAL","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":true,"useHandCursor":true,"id":"6DZBLA5Oyzc"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":112,"yPos":8,"tabIndex":18,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":579,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":18,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1158,"bottom":40,"altText":"Rectangle 6","pngfb":false,"pr":{"l":"Lib","i":14}},"html5data":{"xPos":-1,"yPos":-1,"width":1159,"height":41,"strokewidth":0}},"width":1158,"height":40,"resume":true,"useHandCursor":true,"id":"61P6lGLLWX1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"62UoWz85XLd_-924706184","id":"01","linkId":"txt__default_62UoWz85XLd","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":620,"height":23,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"M1: TESTE FINAL","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"fontIsBold":false,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":151,"bottom":27,"pngfb":false,"pr":{"l":"Lib","i":814}}}],"shapemaskId":"","xPos":128,"yPos":12,"tabIndex":22,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":320,"rotateYPos":16.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":19,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":640,"bottom":33,"altText":"M1: TESTE FINAL","pngfb":false,"pr":{"l":"Lib","i":15}},"html5data":{"xPos":0,"yPos":0,"width":640,"height":33,"strokewidth":0}},"width":640,"height":33,"resume":true,"useHandCursor":true,"id":"62UoWz85XLd"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6c51XNI6rQi_IncorrectReview","id":"01","linkId":"6c51XNI6rQi_IncorrectReview","type":"vectortext","xPos":0,"yPos":0,"xAccOffset":0,"yAccOffset":0,"width":0,"height":0,"shadowIndex":-1,"scrollOverflow":false,"vectortext":{"left":0,"top":0,"right":694,"bottom":35,"pngfb":false,"pr":{"l":"Lib","i":827}}}],"shapemaskId":"","xPos":0,"yPos":680,"tabIndex":54,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":20,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1280,"bottom":40,"altText":"Incorrecto","pngfb":false,"pr":{"l":"Lib","i":826}},"html5data":{"xPos":1,"yPos":1,"width":1277,"height":37,"strokewidth":2}},"width":1280,"height":40,"resume":false,"useHandCursor":true,"id":"6c51XNI6rQi_IncorrectReview","events":[{"kind":"onrelease","actions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6c51XNI6rQi_CorrectReview","id":"01","linkId":"6c51XNI6rQi_CorrectReview","type":"vectortext","xPos":0,"yPos":0,"xAccOffset":0,"yAccOffset":0,"width":0,"height":0,"shadowIndex":-1,"scrollOverflow":false,"vectortext":{"left":0,"top":0,"right":685,"bottom":35,"pngfb":false,"pr":{"l":"Lib","i":829}}}],"shapemaskId":"","xPos":0,"yPos":680,"tabIndex":53,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":21,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1280,"bottom":40,"altText":"Correcto","pngfb":false,"pr":{"l":"Lib","i":828}},"html5data":{"xPos":1,"yPos":1,"width":1277,"height":37,"strokewidth":2}},"width":1280,"height":40,"resume":false,"useHandCursor":true,"id":"6c51XNI6rQi_CorrectReview","events":[{"kind":"onrelease","actions":[{"kind":"hide","transition":"appear","objRef":{"type":"string","value":"_this"}}]}]}],"startTime":-1,"elapsedTimeMode":"normal","useHandCursor":false,"resume":true,"kind":"slidelayer","isBaseLayer":true},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":5000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5tRBlOMOcpk"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","textLib":[{"kind":"textdata","uniqueId":"67oAN6HZfp9_1752810627","id":"01","linkId":"txt__default_5tRBlOMOcpk","type":"acctext","xPos":4,"yPos":2,"xAccOffset":4,"yAccOffset":2,"width":216,"height":44,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Continuar","style":{"fontSize":16,"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203,"fontIsBold":false}}],"style":{"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":18.667,"fontIsBold":true,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#FFFFFF","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":162,"bottom":42,"pngfb":false,"pr":{"l":"Lib","i":831}}}],"shapemaskId":"","xPos":544,"yPos":600,"tabIndex":14,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":112,"rotateYPos":24,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":false,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-2,"top":-2,"right":225,"bottom":49,"altText":"Continuar","pngfb":false,"pr":{"l":"Lib","i":327}},"html5data":{"xPos":-2,"yPos":-2,"width":227,"height":51,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-3,"top":-3,"right":226,"bottom":50,"altText":"Continuar","pngfb":false,"pr":{"l":"Lib","i":830}},"html5data":{"xPos":-3,"yPos":-3,"width":229,"height":53,"strokewidth":3}}}],"width":224,"height":48,"resume":false,"useHandCursor":true,"id":"5tRBlOMOcpk","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_player.6rTmS2JpDep.6aV3LVrn62Y"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":false,"useHandCursor":false,"id":"6KzukPLYT8S","events":[{"kind":"ontransitionin","actions":[{"kind":"enable_frame_control","name":"submit","enable":false}]},{"kind":"ontransitionout","actions":[{"kind":"enable_frame_control","name":"submit","enable":true}]},{"kind":"ontransitionincomplete","actions":[{"kind":"setfocus","showrect":false,"value":{"type":"string","value":"5tRBlOMOcpk"}}]}]},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":39500,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5tan86yaQfz"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VCYeYUEni1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"69s3gqNHg1Y"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ayzt09NtEH"}}]},{"kind":"ontimelinetick","time":1000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5whFHQDPFcs"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6jXPK0rdGU9"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5lVfqqZUc0d"}}]},{"kind":"ontimelinetick","time":2250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6VHGXGQPaHr"}}]},{"kind":"ontimelinetick","time":4000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6dIY6ufVyqZ"}}]},{"kind":"ontimelinetick","time":7979,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"65Z2OtQpB8s"}}]},{"kind":"ontimelinetick","time":12750,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6a0vWVXybMh"}}]},{"kind":"ontimelinetick","time":19000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5l4SITyHfG9"}}]},{"kind":"ontimelinetick","time":26000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5r3n1dkaOym"}}]},{"kind":"ontimelinetick","time":31500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5ht0I6wfKd3"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":0,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"5tan86yaQfz"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":72,"tabIndex":1,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":291.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":585,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":29}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":586,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":584,"resume":true,"useHandCursor":true,"id":"5ayzt09NtEH"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":2,"id":"01","url":"story_content/6Q70oTwPFfL_RC36618.png","type":"normal","altText":"target-audience.png","width":512,"height":512,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":80,"yPos":192,"tabIndex":4,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":160,"rotateYPos":160,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":320,"bottom":320,"altText":"target-audience.png","pngfb":false,"pr":{"l":"Lib","i":30}},"html5data":{"xPos":0,"yPos":0,"width":320,"height":320,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":320,"height":320,"resume":true,"useHandCursor":true,"id":"6VCYeYUEni1"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"69s3gqNHg1Y_-519312530","id":"01","linkId":"txt__default_69s3gqNHg1Y","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":652,"height":30,"valign":"center","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"OBJECTIVOS DO CURSO","style":{"fontFamily":"\\"Neo Sans StdBold ChBold2FC9632B\\",\\"Neo Sans Std\\"","fontSize":16,"fontIsBold":false,"linkColor":"#1888E7","ascent":16.32,"descent":5.013,"leading":4.267,"underlinePosition":-0.832,"underlineThickness":1.707,"xHeight":12.203}}],"style":{"justification":"center","defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":452,"bottom":38,"pngfb":false,"pr":{"l":"Lib","i":32}}}],"shapemaskId":"","xPos":504,"yPos":104,"tabIndex":3,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":336,"rotateYPos":20,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":672,"bottom":40,"altText":"OBJECTIVOS DO CURSO","pngfb":false,"pr":{"l":"Lib","i":31}},"html5data":{"xPos":0,"yPos":0,"width":672,"height":40,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":672,"height":40,"resume":true,"useHandCursor":true,"id":"69s3gqNHg1Y"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":2,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5lVfqqZUc0d","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","id":"01","linkId":"txt__default_5whFHQDPFcs","type":"hiddentext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":756,"height":374,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":5,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":776,"bottom":384,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\nConhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\nIdentificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\nEntender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\nReconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\nFamiliarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\nExplorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\nCompreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","pngfb":false,"pr":{"l":"Lib","i":35}},"html5data":{"xPos":0,"yPos":0,"width":776,"height":384,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":true,"useHandCursor":true,"id":"5whFHQDPFcs"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5ht0I6wfKd3","id":"01","linkId":"txt_5ht0I6wfKd3","type":"acctext","xPos":10,"yPos":330,"xAccOffset":10,"yAccOffset":330,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":143,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":694,"bottom":376,"pngfb":false,"pr":{"l":"Lib","i":37}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":42,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":694,"bottom":376,"altText":"Compreender os princípios éticos e legais, bem como os desafios relacionados à segurança e privacidade no uso de IA em ambientes corporativos.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":694,"height":376,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5ht0I6wfKd3"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5r3n1dkaOym","id":"01","linkId":"txt_5r3n1dkaOym","type":"acctext","xPos":10,"yPos":273,"xAccOffset":10,"yAccOffset":273,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":114,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":746,"bottom":319,"pngfb":false,"pr":{"l":"Lib","i":38}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":43,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":746,"bottom":319,"altText":"Explorar ferramentas modernas de IA, como ChatGPT, Copilot, Gemini e Claude AI, e as suas aplicações em negócios.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":746,"height":319,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5r3n1dkaOym"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_5l4SITyHfG9","id":"01","linkId":"txt_5l4SITyHfG9","type":"acctext","xPos":10,"yPos":238,"xAccOffset":10,"yAccOffset":238,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":80,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":700,"bottom":261,"pngfb":false,"pr":{"l":"Lib","i":39}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":44,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":9,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":700,"bottom":261,"altText":"Familiarizar-se com produtos e serviços que utilizam IA actualmente no mercado.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":700,"height":261,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"5l4SITyHfG9"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6a0vWVXybMh","id":"01","linkId":"txt_6a0vWVXybMh","type":"acctext","xPos":10,"yPos":181,"xAccOffset":10,"yAccOffset":181,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":88,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":671,"bottom":227,"pngfb":false,"pr":{"l":"Lib","i":40}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":45,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":10,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":671,"bottom":227,"altText":"Reconhecer as aplicações práticas de IA em diferentes sectores do ambiente corporativo.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":671,"height":227,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6a0vWVXybMh"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_65Z2OtQpB8s","id":"01","linkId":"txt_65Z2OtQpB8s","type":"acctext","xPos":10,"yPos":123,"xAccOffset":10,"yAccOffset":123,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":124,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":742,"bottom":169,"pngfb":false,"pr":{"l":"Lib","i":41}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":46,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":11,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":742,"bottom":169,"altText":"Entender o funcionamento básico de sistemas de IA, incluindo os tipos de IA, como os algoritmos processam dados e aprendem.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":742,"height":169,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"65Z2OtQpB8s"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6dIY6ufVyqZ","id":"01","linkId":"txt_6dIY6ufVyqZ","type":"acctext","xPos":10,"yPos":66,"xAccOffset":10,"yAccOffset":66,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":139,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":714,"bottom":112,"pngfb":false,"pr":{"l":"Lib","i":42}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":47,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":12,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":714,"bottom":112,"altText":"Identificar as principais áreas de pesquisa em IA, como Aprendizagem Automática, processamento de Linguagem Natural e Visão Computacional.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":714,"height":112,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6dIY6ufVyqZ"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6VHGXGQPaHr","id":"01","linkId":"txt_6VHGXGQPaHr","type":"acctext","xPos":10,"yPos":9,"xAccOffset":10,"yAccOffset":9,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":125,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":48,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":705,"bottom":54,"pngfb":false,"pr":{"l":"Lib","i":43}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":48,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":13,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":705,"bottom":54,"altText":"Conhecer a história e evolução da IA, incluindo os principais marcos históricos e a presença da IA na ficção e na realidade.","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":0,"width":705,"height":54,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6VHGXGQPaHr"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"txt_6jXPK0rdGU9","id":"01","linkId":"txt_6jXPK0rdGU9","type":"acctext","xPos":10,"yPos":-26,"xAccOffset":10,"yAccOffset":-26,"width":756,"height":374,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":14,"ascent":18.293,"descent":4.387,"leading":0,"underlinePosition":-0.952,"underlineThickness":0.915,"xHeight":10.267}}],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[{"idx":0,"len":72,"flowDirection":"leftToRight","cursive":false}]},{"spans":[],"style":{"flowDirection":"leftToRight","leadingMargin":41,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"listLevel":0,"lineSpacingRule":"single","lineSpacing":20,"spacingBefore":0,"spacingAfter":12,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletChar":252,"bulletFont":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","bulletPicture":{"w":0,"h":0,"base64":0},"color":"#000000"},"tagType":"P"},"runs":[]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"center","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":16,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":-27,"right":610,"bottom":0,"pngfb":false,"pr":{"l":"Lib","i":44}}}],"shapemaskId":"","xPos":496,"yPos":216,"tabIndex":49,"tabEnabled":false,"xOffset":0,"yOffset":0,"rotateXPos":388,"rotateYPos":192,"scaleX":100,"scaleY":100,"alpha":100,"depth":14,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":-27,"right":610,"bottom":0,"altText":" Compreender os conceitos fundamentais de Inteligência Artificial (IA).","pngfb":false,"pr":{"l":"Lib","i":36}},"html5data":{"xPos":0,"yPos":-27,"width":610,"height":27,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":776,"height":384,"resume":false,"useHandCursor":true,"id":"6jXPK0rdGU9"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"6QtAxFNOoxm"},{"kind":"slidelayer","depth":0,"modal":false,"pauseParent":false,"rotateXPos":640,"rotateYPos":360,"tabIndex":-1,"presentAs":"layer","labeledById":"","describedById":"","enableSeek":true,"enableReplay":true,"lmsId":"","timeline":{"duration":10000,"events":[{"kind":"ontimelinetick","time":0,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6TRpicPw6tU"}}]},{"kind":"ontimelinetick","time":500,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5VcGQGd6sQ4"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6HniC2d4w8h"}},{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5nw6rMDcq88"}}]},{"kind":"ontimelinetick","time":1250,"actions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"5wH7XkMdNj1"}},{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6fLNGDgml4K"}}]},{"kind":"ontimelinetick","time":2000,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"5a1AGGT21Cq"}}]},{"kind":"ontimelinetick","time":4250,"actions":[{"kind":"show","transition":"custom","animationId":"Entrance","reverse":false,"objRef":{"type":"string","value":"6ex489sIWwL"}}]}]},"objects":[{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":0,"tabIndex":6,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":640,"rotateYPos":360,"scaleX":100,"scaleY":100,"alpha":100,"depth":1,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":1280,"bottom":720,"altText":"Rectangle 1","pngfb":false,"pr":{"l":"Lib","i":28}},"html5data":{"xPos":0,"yPos":0,"width":1280,"height":720,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":720,"resume":true,"useHandCursor":true,"id":"6TRpicPw6tU"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":0,"yPos":64,"tabIndex":7,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":639.5,"rotateYPos":279.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":2,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":1281,"bottom":561,"altText":"Rectangle 2","pngfb":false,"pr":{"l":"Lib","i":45}},"html5data":{"xPos":-1,"yPos":-1,"width":1282,"height":562,"strokewidth":1}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":1280,"height":560,"resume":true,"useHandCursor":true,"id":"5VcGQGd6sQ4"},{"kind":"vectorshape","rotation":0,"accType":"image","cliptobounds":false,"defaultAction":"","imagelib":[{"kind":"imagedata","assetId":3,"id":"01","url":"story_content/6S7pEErDZEG.png","type":"normal","altText":"Agrupar 1.png","width":959,"height":657,"mobiledx":0,"mobiledy":0}],"shapemaskId":"","xPos":1,"yPos":80,"tabIndex":8,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":391,"rotateYPos":268,"scaleX":100,"scaleY":100,"alpha":100,"depth":3,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":782,"bottom":536,"altText":"Agrupar 1.png","pngfb":false,"pr":{"l":"Lib","i":46}},"html5data":{"xPos":0,"yPos":0,"width":782,"height":536,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":782,"height":536,"resume":true,"useHandCursor":true,"id":"6HniC2d4w8h"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","shapemaskId":"","xPos":16,"yPos":94,"tabIndex":10,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":40,"rotateYPos":18.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":4,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":80,"bottom":37,"altText":"Rectangle 3","pngfb":false,"pr":{"l":"Lib","i":47}},"html5data":{"xPos":0,"yPos":0,"width":80,"height":37,"strokewidth":0}},"width":80,"height":37,"resume":true,"useHandCursor":true,"id":"5nw6rMDcq88"},{"kind":"vectorshape","rotation":0,"accType":"button","cliptobounds":false,"defaultAction":"onrelease","shapemaskId":"","xPos":1192,"yPos":88,"tabIndex":9,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":39.5,"rotateYPos":39.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":5,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 2","pngfb":false,"pr":{"l":"Lib","i":33}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}},"states":[{"kind":"state","name":"_default_Hover","data":{"hotlinkId":"","accState":0,"vectorData":{"left":-1,"top":-1,"right":81,"bottom":81,"altText":"Multiply 3","pngfb":false,"pr":{"l":"Lib","i":34}},"html5data":{"xPos":-1,"yPos":-1,"width":82,"height":82,"strokewidth":1}}}],"width":80,"height":80,"resume":true,"useHandCursor":true,"id":"5wH7XkMdNj1","variables":[{"kind":"variable","name":"_hover","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_state","type":"string","value":"_default","resume":true},{"kind":"variable","name":"_disabled","type":"boolean","value":false,"resume":true},{"kind":"variable","name":"_stateName","type":"string","value":"","resume":true},{"kind":"variable","name":"_tempStateName","type":"string","value":"","resume":false}],"actionGroups":{"ActGrpSetHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":true}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearHoverState":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}},{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},"ActGrpClearStateVars":{"kind":"actiongroup","actions":[{"kind":"adjustvar","variable":"_hover","operator":"set","value":{"type":"boolean","value":false}}]}},"events":[{"kind":"onrelease","actions":[{"kind":"exe_actiongroup","id":"_this.ActGrpClearHoverState"},{"kind":"hide_slidelayer","transition":"appear","objRef":{"type":"string","value":"_parent"}}]},{"kind":"ontransitionin","actions":[{"kind":"exe_actiongroup","id":"_player._setstates","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollover","actions":[{"kind":"exe_actiongroup","id":"ActGrpSetHoverState","scopeRef":{"type":"string","value":"_this"}}]},{"kind":"onrollout","actions":[{"kind":"exe_actiongroup","id":"ActGrpClearHoverState","scopeRef":{"type":"string","value":"_this"}}]}]},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6fLNGDgml4K_1529763356","id":"01","linkId":"txt__default_6fLNGDgml4K","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":308,"height":190,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Play/Pause\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":11,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Timeline\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":9,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Reiniciar ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Volume\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":7,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Voltar ao menu\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":15,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Objectivos do curso","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":48,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"listNumberedAsArabic","listTypeFormat":"period","size":100,"bulletFont":"Neo Sans Std","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":19,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":191,"bottom":193,"pngfb":false,"pr":{"l":"Lib","i":49}}}],"shapemaskId":"","xPos":24,"yPos":128,"tabIndex":11,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":164,"rotateYPos":100,"scaleX":100,"scaleY":100,"alpha":100,"depth":6,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":328,"bottom":200,"altText":"Play/Pause\\nTimeline\\nReiniciar ecrã\\nVolume\\nVoltar ao menu\\nObjectivos do curso","pngfb":false,"pr":{"l":"Lib","i":48}},"html5data":{"xPos":0,"yPos":0,"width":328,"height":200,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":328,"height":200,"resume":true,"useHandCursor":true,"id":"6fLNGDgml4K"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"5a1AGGT21Cq_1013326046","id":"01","linkId":"txt__default_5a1AGGT21Cq","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":420,"height":222,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"7.     Instruções de navegação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":31,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"8.      A sua pontuação\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"9.      Instruções de interacções no ecrã\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":42,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"10.   Notas/curiosidade\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":24,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"11.   Voltar ao ecrã anterior\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":30,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"12.   Paginação \\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":17,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"13.   Avançar para o próximo ecrã","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":13,"linkColor":"#0000FF","ascent":16.987,"descent":4.073,"leading":0,"underlinePosition":-0.884,"underlineThickness":0.849,"xHeight":9.533}}],"style":{"leadingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":85.333,"lineSpacingRule":"singlePt5","lineSpacing":30,"listStyle":{"listType":"none","listTypeFormat":"plain","size":100,"bulletFont":"Myriad Pro","bulletPicture":{"w":0,"h":0,"base64":0}},"tagType":"P"},"runs":[{"idx":0,"len":33,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":304,"bottom":225,"pngfb":false,"pr":{"l":"Lib","i":51}}}],"shapemaskId":"","xPos":320,"yPos":128,"tabIndex":12,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":220,"rotateYPos":116,"scaleX":100,"scaleY":100,"alpha":100,"depth":7,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":440,"bottom":232,"altText":"7.     Instruções de navegação\\n8.      A sua pontuação\\n9.      Instruções de interacções no ecrã\\n10.   Notas/curiosidade\\n11.   Voltar ao ecrã anterior\\n12.   Paginação \\n13.   Avançar para o próximo ecrã","pngfb":false,"pr":{"l":"Lib","i":50}},"html5data":{"xPos":0,"yPos":0,"width":440,"height":232,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":440,"height":232,"resume":true,"useHandCursor":true,"id":"5a1AGGT21Cq"},{"kind":"vectorshape","rotation":0,"accType":"text","cliptobounds":false,"defaultAction":"","textLib":[{"kind":"textdata","uniqueId":"6ex489sIWwL_1502302535","id":"01","linkId":"txt__default_6ex489sIWwL","type":"acctext","xPos":10,"yPos":5,"xAccOffset":10,"yAccOffset":5,"width":428,"height":363,"valign":"top","wordwrap":true,"textshadow":false,"shadowIndex":-1,"scrollOverflow":false,"vartext":{"blocks":[{"spans":[{"text":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":136,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"Ao finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\n","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":133,"flowDirection":"leftToRight","cursive":false}]},{"spans":[{"text":"A pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","style":{"fontFamily":"\\"Neo Sans Std Charset0_v3F026DC3\\",\\"Neo Sans Std\\"","fontSize":16,"linkColor":"#0000FF","ascent":20.907,"descent":5.013,"leading":0,"underlinePosition":-1.088,"underlineThickness":1.045,"xHeight":11.733}}],"style":{"defaultTabStop":85.333,"lineSpacing":20,"tagType":"P"},"runs":[{"idx":0,"len":153,"flowDirection":"leftToRight","cursive":false}]}],"defaultBlockStyle":{"flowDirection":"leftToRight","leadingMargin":0,"trailingMargin":0,"firstLineMargin":0,"justification":"left","defaultTabStop":64,"listLevel":0,"lineSpacingRule":"multiple","lineSpacing":20,"indentSize":48,"spacingBefore":0,"spacingAfter":0,"baseSpanStyle":{"fontFamily":"Neo Sans Std","fontSize":21.333,"fontIsBold":false,"fontIsItalic":false,"fontIsUnderline":false,"fontIsStrikeout":false,"underlineStyle":"normal","elevation":"normal","spacing":0,"ignoreKerningTable":false,"displayCase":"asIs","languageId":0,"foregroundColor":"#000000","linkColor":"#0000FF"},"listStyle":{"listType":"none","listTypeFormat":"plain","start":0,"size":100}},"direction":"horizontal"},"vectortext":{"left":0,"top":0,"right":435,"bottom":368,"pngfb":false,"pr":{"l":"Lib","i":53}}}],"shapemaskId":"","xPos":808,"yPos":168,"tabIndex":13,"tabEnabled":true,"xOffset":0,"yOffset":0,"rotateXPos":224,"rotateYPos":186.5,"scaleX":100,"scaleY":100,"alpha":100,"depth":8,"scrolling":true,"shuffleLock":false,"data":{"hotlinkId":"","accState":0,"vectorData":{"left":0,"top":0,"right":448,"bottom":373,"altText":"Neste módulo, será necessário acumular uma pontuação mínima de 16 pontos e concluir todas as unidades para desbloquear o Teste Final. \\r\\nAo finalizar o conteúdo de cada unidade, receberá pontos, e por cada exercício respondido correctamente, ganhará 1 ponto adicional.\\r\\nA pontuação foi estruturada de forma a que, além de completar o conteúdo, seja necessário acertar em alguns exercícios para atingir o mínimo necessário. ","pngfb":false,"pr":{"l":"Lib","i":52}},"html5data":{"xPos":0,"yPos":0,"width":448,"height":373,"strokewidth":0}},"animations":[{"kind":"animation","id":"Entrance","duration":750,"hidetextatstart":true,"animateshapewithtext":false,"tweens":[{"kind":"tween","time":0,"duration":750,"alpha":{"path":[{"kind":"segment","start":"0","dstart":"0","end":"100","dend":"0"}],"duration":750,"easing":"linear","easingdir":"easein"}}]}],"width":448,"height":373,"resume":true,"useHandCursor":true,"id":"6ex489sIWwL"}],"startTime":-1,"elapsedTimeMode":"normal","width":1280,"height":720,"resume":true,"useHandCursor":false,"id":"5Xlh34Ekb5w"}],"showAnimationId":"","lmsId":"Slide10","width":1280,"height":720,"resume":true,"background":{"type":"fill","fill":{"type":"linear","rotation":90,"colors":[{"kind":"color","rgb":"0xFFFFFF","alpha":100,"stop":0}]}},"id":"5owJEM2xLBR","actionGroups":{"ReviewInt_6c51XNI6rQi":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5w10urQzp0f"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"6j5RK56qg3w"},"enabled":{"type":"boolean","value":false}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6SF9pMHnwVK.$Status","typea":"property","valueb":"correct","typeb":"string"}},"thenActions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6c51XNI6rQi_CorrectReview"}}],"elseActions":[{"kind":"show","transition":"appear","objRef":{"type":"string","value":"6c51XNI6rQi_IncorrectReview"}}]},{"kind":"show_slidelayer","hideOthers":"oncomplete","transition":"appear","objRef":{"type":"string","value":"6KzukPLYT8S"}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#CurrentQuiz_6c51XNI6rQi","typea":"var","valueb":"5y4R5TkhW1I","typeb":"string"}},"thenActions":[{"kind":"exe_actiongroup","id":"SetLayout_pxabnsnfns10110000101"},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.5y4R5TkhW1I.$Passed","typea":"property","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"exe_actiongroup","id":"ReviewIntCorrectIncorrect_6c51XNI6rQi"}]},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.5y4R5TkhW1I.$Passed","typea":"property","valueb":false,"typeb":"boolean"}},"thenActions":[{"kind":"exe_actiongroup","id":"ReviewIntCorrectIncorrect_6c51XNI6rQi"}]}]}]},"ReviewIntCorrectIncorrect_6c51XNI6rQi":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5w10urQzp0f"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"6j5RK56qg3w"},"enabled":{"type":"boolean","value":false}}]},"AnsweredInt_6c51XNI6rQi":{"kind":"actiongroup","actions":[{"kind":"exe_actiongroup","id":"DisableChoices_6c51XNI6rQi"},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"pxabnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"pxabnsnfns10110000101"}]}]},"DisableChoices_6c51XNI6rQi":{"kind":"actiongroup","actions":[{"kind":"set_enabled","objRef":{"type":"string","value":"5w10urQzp0f"},"enabled":{"type":"boolean","value":false}},{"kind":"set_enabled","objRef":{"type":"string","value":"6j5RK56qg3w"},"enabled":{"type":"boolean","value":false}}]},"6c51XNI6rQi_CheckAnswered":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"or","statements":[{"kind":"compare","operator":"eq","valuea":"6SF9pMHnwVK.$Status","typea":"property","valueb":"correct","typeb":"string"},{"kind":"compare","operator":"eq","valuea":"_player.5y4R5TkhW1I.$QuizComplete","typea":"property","valueb":true,"typeb":"boolean"}]}},"thenActions":[{"kind":"exe_actiongroup","id":"AnsweredInt_6c51XNI6rQi"}],"elseActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"6SF9pMHnwVK.$Status","typea":"property","valueb":"incorrect","typeb":"string"}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"gte","valuea":"6SF9pMHnwVK.$AttemptCount","typea":"property","valueb":1,"typeb":"number"}},"thenActions":[{"kind":"exe_actiongroup","id":"AnsweredInt_6c51XNI6rQi"}]}]}]}]},"SetLayout_pxabnsnfns10110000101":{"kind":"actiongroup","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"pxabnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"pxabnsnfns10110000101"}]}]},"NavigationRestrictionNextSlide_5owJEM2xLBR":{"kind":"actiongroup","actions":[{"kind":"gotoplay","window":"_current","wndtype":"normal","objRef":{"type":"string","value":"_parent.6aV3LVrn62Y"}}]},"NavigationRestrictionPreviousSlide_5owJEM2xLBR":{"kind":"actiongroup","actions":[{"kind":"history_prev"}]}},"events":[{"kind":"onslidestart","actions":[{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"next","visible":false},{"kind":"enable_window_control","name":"next","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swiperight","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"previous","visible":false},{"kind":"enable_window_control","name":"previous","enable":true,"affectTabStop":false},{"kind":"enable_window_control","name":"swipeleft","enable":true,"affectTabStop":false},{"kind":"set_window_control_visible","name":"submit","visible":false},{"kind":"enable_window_control","name":"submit","enable":true,"affectTabStop":false}]},{"kind":"onbeforeslidein","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"$WindowId","typea":"property","valueb":"_frame","typeb":"string"}},"thenActions":[{"kind":"set_frame_layout","name":"npnxnanbnsnfns10110000101"}],"elseActions":[{"kind":"set_window_control_layout","name":"npnxnanbnsnfns10110000101"}]}]},{"kind":"ontransitionin","actions":[{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"set","value":{"type":"string","value":"_player."}},{"kind":"adjustvar","variable":"_player.LastSlideViewed_6qX7xzvLyR5","operator":"add","value":{"type":"property","value":"$AbsoluteId"}},{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#ReviewMode_6c51XNI6rQi","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"exe_actiongroup","id":"ReviewInt_6c51XNI6rQi"}],"elseActions":[{"kind":"exe_actiongroup","id":"6c51XNI6rQi_CheckAnswered"}]}]},{"kind":"onnextslide","actions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#ReviewMode_6c51XNI6rQi","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#CurrentQuiz_6c51XNI6rQi","typea":"var","valueb":"5y4R5TkhW1I","typeb":"string"}},"thenActions":[{"kind":"nextviewedslide","quizRef":{"type":"string","value":"_player.5y4R5TkhW1I"},"completed_slide_ref":{"type":"string","value":"_player.6rTmS2JpDep.6P1mwFmEyPG"},"status_filter":""}],"elseActions":[]}],"elseActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#RetryMode_6c51XNI6rQi","typea":"var","valueb":true,"typeb":"boolean"}},"thenActions":[{"kind":"if_action","condition":{"statement":{"kind":"compare","operator":"eq","valuea":"_player.#CurrentQuiz_6c51XNI6rQi","typea":"var","valueb":"5y4R5TkhW1I","typeb":"string"}},"thenActions":[{"kind":"nextviewedslide","quizRef":{"type":"string","value":"_player.5y4R5TkhW1I"},"completed_slide_ref":{"type":"string","value":"_player.6rTmS2JpDep.6P1mwFmEyPG"},"status_filter":""}],"elseActions":[]}],"elseActions":[{"kind":"exe_actiongroup","id":"NavigationRestrictionNextSlide_5owJEM2xLBR"}]}]}]},{"kind":"onprevslide","actions":[{"kind":"exe_actiongroup","id":"NavigationRestrictionPreviousSlide_5owJEM2xLBR"}]}]}');