import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Breadcrumb from '@/components/Breadcrumb';
import { BookOpen, LogOut, User, Settings, UserCircle } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  if (!user) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-background smooth-scroll">
      {/* Top Navigation */}
      <nav className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50 animate-slide-up">
        <div className="container-responsive">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center gap-3 hover-lift">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center hover-glow transition-all duration-300">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent hidden sm:block">
                e_curso
              </span>
              <span className="text-lg font-bold bg-gradient-primary bg-clip-text text-transparent sm:hidden">
                e_curso
              </span>
            </div>

            {/* User Info */}
            <div className="flex items-center gap-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-3 hover:bg-muted/50 transition-all duration-300 hover-lift focus-ring">
                    <div className="w-8 h-8 bg-gradient-secondary rounded-full flex items-center justify-center hover-glow transition-all duration-300">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div className="hidden md:block text-left">
                      <p className="text-sm font-medium">{user.name}</p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {user.type === 'admin' ? 'Administrador' : 'Aluno'}
                      </p>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-popover border shadow-lg animate-scale-in">
                  <DropdownMenuLabel>Minha Conta</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => navigate('/settings')}
                    className="cursor-pointer hover:bg-accent transition-colors duration-200 focus-ring"
                  >
                    <UserCircle className="w-4 h-4 mr-2" />
                    Perfil
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => navigate('/settings')}
                    className="cursor-pointer hover:bg-accent transition-colors duration-200 focus-ring"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Configurações
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={logout}
                    className="cursor-pointer text-destructive focus:text-destructive hover:bg-destructive/10 transition-colors duration-200 focus-ring"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sair
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb */}
      <div className="border-b bg-muted/30">
        <div className="container-responsive py-3">
          <Breadcrumb />
        </div>
      </div>

      {/* Main Content */}
      <main className="animate-fade-in">{children}</main>
    </div>
  );
};

export default Layout;