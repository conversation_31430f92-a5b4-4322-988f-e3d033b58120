import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { Course, getStoredData, initializeMockData } from '@/data/mockData';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import CourseModal from '@/components/CourseModal';
import DeleteCourseDialog from '@/components/DeleteCourseDialog';
import StudentModal from '@/components/StudentModal';
import DeleteStudentDialog from '@/components/DeleteStudentDialog';
import { Student, setStoredData } from '@/data/mockData';
import StatsCard from '@/components/StatsCard';
import CourseCard from '@/components/CourseCard';
import StudentCard from '@/components/StudentCard';
import {
  Users,
  BookOpen,
  BarChart3,
  Plus,
  Search,
  Settings,
  TrendingUp,
  FileText,
  Eye,
  Edit,
  MoreHorizontal,
  Trash2,
  Brain
} from 'lucide-react';

interface StudentSummary {
  id: string;
  name: string;
  email: string;
  coursesEnrolled: number;
  totalProgress: number;
}

const AdminDashboard = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [courses, setCourses] = useState<Course[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCourses: 0,
    totalLessons: 0,
    avgProgress: 0
  });

  // Estados dos modais de cursos
  const [courseModalOpen, setCourseModalOpen] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);

  // Estados dos modais de alunos
  const [studentModalOpen, setStudentModalOpen] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [deleteStudentDialogOpen, setDeleteStudentDialogOpen] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null);

  const loadData = useCallback(() => {
    initializeMockData();
    const storedCourses = getStoredData<Course[]>('ecurso_courses', []);
    const storedStudents = getStoredData<Student[]>('ecurso_students', []);

    setCourses(storedCourses);
    setStudents(storedStudents);

    // Calculate stats
    const totalLessons = storedCourses.reduce(
      (acc, course) => acc + course.modules.reduce(
        (moduleAcc, module) => moduleAcc + module.lessons.length, 0
      ), 0
    );

    setStats({
      totalStudents: storedStudents.length,
      totalCourses: storedCourses.length,
      totalLessons,
      avgProgress: 65 // Mock average progress
    });
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Memoized calculations for better performance
  const memoizedStats = useMemo(() => {
    const totalLessons = courses.reduce(
      (acc, course) => acc + course.modules.reduce(
        (moduleAcc, module) => moduleAcc + module.lessons.length, 0
      ), 0
    );

    return {
      totalStudents: students.length,
      totalCourses: courses.length,
      totalLessons,
      avgProgress: 65 // Mock average progress
    };
  }, [courses, students]);

  // Handlers para modais de cursos (memoized)
  const handleNewCourse = useCallback(() => {
    setEditingCourse(null);
    setCourseModalOpen(true);
  }, []);

  const handleEditCourse = useCallback((course: Course) => {
    setEditingCourse(course);
    setCourseModalOpen(true);
  }, []);

  const handleDeleteCourse = useCallback((course: Course) => {
    setCourseToDelete(course);
    setDeleteDialogOpen(true);
  }, []);

  const handleCourseSaved = useCallback((course: Course) => {
    loadData(); // Recarregar dados
    toast({
      title: editingCourse ? "Curso atualizado!" : "Curso criado!",
      description: editingCourse
        ? "As alterações foram salvas com sucesso."
        : "O novo curso foi adicionado à plataforma.",
    });
  }, [editingCourse, loadData, toast]);

  const handleCourseDeleted = useCallback((courseId: string) => {
    const courses = getStoredData<Course[]>('ecurso_courses', []);
    const updatedCourses = courses.filter(c => c.id !== courseId);
    setStoredData('ecurso_courses', updatedCourses);

    loadData(); // Recarregar dados
    toast({
      title: "Curso excluído!",
      description: "O curso foi removido da plataforma.",
    });
  }, [loadData, toast]);

  // Handlers para modais de alunos
  const handleNewStudent = () => {
    setEditingStudent(null);
    setStudentModalOpen(true);
  };

  const handleEditStudent = (student: Student) => {
    setEditingStudent(student);
    setStudentModalOpen(true);
  };

  const handleDeleteStudent = (student: Student) => {
    setStudentToDelete(student);
    setDeleteStudentDialogOpen(true);
  };

  const handleStudentSaved = (student: Student) => {
    loadData(); // Recarregar dados
    toast({
      title: editingStudent ? "Aluno atualizado!" : "Aluno criado!",
      description: editingStudent
        ? "As alterações foram salvas com sucesso."
        : "O novo aluno foi adicionado à plataforma.",
    });
  };

  const handleStudentDeleted = (studentId: string) => {
    // Remover aluno
    const students = getStoredData<Student[]>('ecurso_students', []);
    const updatedStudents = students.filter(s => s.id !== studentId);
    setStoredData('ecurso_students', updatedStudents);

    // Remover aluno dos cursos
    const courses = getStoredData<Course[]>('ecurso_courses', []);
    const updatedCourses = courses.map(course => ({
      ...course,
      students: course.students.filter(id => id !== studentId)
    }));
    setStoredData('ecurso_courses', updatedCourses);

    // Remover progresso do aluno
    const progress = getStoredData('ecurso_progress', []);
    const updatedProgress = progress.filter((p: any) => p.studentId !== studentId);
    setStoredData('ecurso_progress', updatedProgress);

    loadData(); // Recarregar dados
    toast({
      title: "Aluno excluído!",
      description: "O aluno foi removido da plataforma.",
    });
  };

  const mockStudents: StudentSummary[] = [
    {
      id: '1',
      name: 'João Silva',
      email: '<EMAIL>',
      coursesEnrolled: 2,
      totalProgress: 45
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card animate-slide-up">
        <div className="container-responsive py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 animate-fade-in">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center hover-glow transition-all duration-300">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">Painel Admin</h1>
                <p className="text-muted-foreground">Bem-vindo, {user?.name}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="container-responsive py-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total de Alunos"
            value={memoizedStats.totalStudents}
            icon={Users}
            iconColor="bg-primary/10"
          />
          <StatsCard
            title="Cursos Ativos"
            value={memoizedStats.totalCourses}
            icon={BookOpen}
            iconColor="bg-secondary/10"
            delay="0.1s"
          />
          <StatsCard
            title="Total de Aulas"
            value={memoizedStats.totalLessons}
            icon={FileText}
            iconColor="bg-success/10"
            delay="0.2s"
          />
          <StatsCard
            title="Progresso Médio"
            value={`${memoizedStats.avgProgress}%`}
            icon={TrendingUp}
            iconColor="bg-warning/10"
            delay="0.3s"
          />
        </div>

        {/* Main Content */}
        <Tabs defaultValue="courses" className="space-y-6 animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 bg-muted/50 backdrop-blur-sm">
            <TabsTrigger value="courses" className="transition-all duration-300 hover-lift focus-ring">Cursos</TabsTrigger>
            <TabsTrigger value="students" className="transition-all duration-300 hover-lift focus-ring">Alunos</TabsTrigger>
            <TabsTrigger value="reports" className="transition-all duration-300 hover-lift focus-ring">Relatórios</TabsTrigger>
          </TabsList>

          <TabsContent value="courses" className="space-y-6 animate-slide-up">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <h2 className="text-xl sm:text-2xl font-bold">Gerenciar Cursos</h2>
              <div className="flex gap-2">
                <Button
                  onClick={() => window.open('/course/ensa-ia', '_blank')}
                  variant="outline"
                  className="hover-lift focus-ring bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 text-purple-700 hover:from-purple-100 hover:to-pink-100"
                >
                  <Brain className="w-4 h-4 mr-2" />
                  Curso ENSA IA
                </Button>
                <Button
                  onClick={handleNewCourse}
                  className="btn-primary-enhanced focus-ring"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Novo Curso
                </Button>
              </div>
            </div>

            <div className="grid gap-4">
              {courses.map((course) => (
                <Card key={course.id} className="shadow-card">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-secondary rounded-lg flex items-center justify-center">
                            <BookOpen className="w-5 h-5 text-white" />
                          </div>
                          {course.title}
                        </CardTitle>
                        <CardDescription>{course.description}</CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover-lift focus-ring"
                          onClick={() => window.open(`/course/${course.id}`, '_blank')}
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          Ver
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="hover-lift focus-ring">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="animate-scale-in">
                            <DropdownMenuItem
                              onClick={() => handleEditCourse(course)}
                              className="cursor-pointer focus-ring"
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteCourse(course)}
                              className="cursor-pointer text-destructive focus:text-destructive hover:bg-destructive/10 focus-ring"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-6 text-sm text-muted-foreground">
                      <span>Instrutor: {course.instructor}</span>
                      <span>Duração: {course.duration}</span>
                      <span>Alunos: {course.students.length}</span>
                      <Badge variant="outline" className="ml-auto">
                        {course.modules.length} módulos
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="students" className="space-y-6 animate-slide-up">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <h2 className="text-xl sm:text-2xl font-bold">Gerenciar Alunos</h2>
              <Button
                onClick={handleNewStudent}
                className="btn-primary-enhanced focus-ring"
              >
                <Plus className="w-4 h-4 mr-2" />
                Novo Aluno
              </Button>
            </div>

            <div className="grid gap-4">
              {students.map((student) => {
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'active': return 'bg-success/10 text-success border-success/20';
                    case 'inactive': return 'bg-muted/50 text-muted-foreground border-muted';
                    case 'suspended': return 'bg-destructive/10 text-destructive border-destructive/20';
                    default: return 'bg-muted/50 text-muted-foreground border-muted';
                  }
                };

                const getStatusLabel = (status: string) => {
                  switch (status) {
                    case 'active': return 'Ativo';
                    case 'inactive': return 'Inativo';
                    case 'suspended': return 'Suspenso';
                    default: return status;
                  }
                };

                return (
                  <Card key={student.id} className="shadow-card card-interactive animate-fade-in">
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center hover-glow transition-all duration-300">
                            <Users className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-semibold">{student.name}</h3>
                              <Badge
                                variant="outline"
                                className={`text-xs ${getStatusColor(student.status)}`}
                              >
                                {getStatusLabel(student.status)}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{student.email}</p>
                            {student.phone && (
                              <p className="text-xs text-muted-foreground">{student.phone}</p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">Cursos</p>
                            <p className="font-semibold">{student.enrolledCourses.length}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">Cadastrado</p>
                            <p className="font-semibold text-xs">
                              {new Date(student.createdAt).toLocaleDateString('pt-PT')}
                            </p>
                          </div>
                          {student.lastAccess && (
                            <div className="text-center">
                              <p className="text-sm text-muted-foreground">Último Acesso</p>
                              <p className="font-semibold text-xs">
                                {new Date(student.lastAccess).toLocaleDateString('pt-PT')}
                              </p>
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="hover-lift focus-ring"
                              onClick={() => window.open(`/student/${student.id}`, '_blank')}
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              Ver
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm" className="hover-lift focus-ring">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="animate-scale-in">
                                <DropdownMenuItem
                                  onClick={() => handleEditStudent(student)}
                                  className="cursor-pointer focus-ring"
                                >
                                  <Edit className="w-4 h-4 mr-2" />
                                  Editar
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteStudent(student)}
                                  className="cursor-pointer text-destructive focus:text-destructive hover:bg-destructive/10 focus-ring"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Excluir
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}

              {students.length === 0 && (
                <div className="text-center py-12 text-muted-foreground border-2 border-dashed rounded-lg">
                  <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">Nenhum aluno cadastrado</p>
                  <p className="text-sm mb-4">Comece adicionando o primeiro aluno à plataforma.</p>
                  <Button
                    onClick={handleNewStudent}
                    className="btn-primary-enhanced focus-ring"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Adicionar Primeiro Aluno
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6 animate-slide-up">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <h2 className="text-xl sm:text-2xl font-bold">Relatórios</h2>
              <Button
                onClick={() => window.open('/reports', '_blank')}
                className="btn-primary-enhanced focus-ring"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Ver Relatórios Completos
              </Button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Engajamento por Curso
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {courses.map((course) => (
                      <div key={course.id} className="flex items-center justify-between">
                        <span className="text-sm">{course.title}</span>
                        <span className="font-semibold">{course.students.length} alunos</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Performance Geral
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Taxa de Conclusão</span>
                      <span className="font-semibold text-success">78%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Tempo Médio por Aula</span>
                      <span className="font-semibold">15 min</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Satisfação</span>
                      <span className="font-semibold text-warning">4.5/5</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modais de Cursos */}
      <CourseModal
        isOpen={courseModalOpen}
        onClose={() => setCourseModalOpen(false)}
        course={editingCourse}
        onSave={handleCourseSaved}
      />

      <DeleteCourseDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        course={courseToDelete}
        onConfirm={handleCourseDeleted}
      />

      {/* Modais de Alunos */}
      <StudentModal
        isOpen={studentModalOpen}
        onClose={() => setStudentModalOpen(false)}
        student={editingStudent}
        onSave={handleStudentSaved}
      />

      <DeleteStudentDialog
        isOpen={deleteStudentDialogOpen}
        onClose={() => setDeleteStudentDialogOpen(false)}
        student={studentToDelete}
        onConfirm={handleStudentDeleted}
      />
    </div>
  );
};

export default AdminDashboard;