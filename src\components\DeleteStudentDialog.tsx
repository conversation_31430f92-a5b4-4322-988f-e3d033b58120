import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Student, Course, getStoredData } from '@/data/mockData';
import { AlertTriangle, Trash2, BookOpen, Clock } from 'lucide-react';

interface DeleteStudentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  student: Student | null;
  onConfirm: (studentId: string) => void;
}

const DeleteStudentDialog: React.FC<DeleteStudentDialogProps> = ({
  isOpen,
  onClose,
  student,
  onConfirm,
}) => {
  const handleConfirm = () => {
    if (student) {
      onConfirm(student.id);
    }
    onClose();
  };

  if (!student) return null;

  // Buscar cursos matriculados
  const courses = getStoredData<Course[]>('ecurso_courses', []);
  const enrolledCourses = courses.filter(course => 
    student.enrolledCourses.includes(course.id)
  );

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Ativo';
      case 'inactive': return 'Inativo';
      case 'suspended': return 'Suspenso';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-success';
      case 'inactive': return 'text-muted-foreground';
      case 'suspended': return 'text-destructive';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="animate-scale-in">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="w-5 h-5" />
            Confirmar Exclusão
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-4">
            <p>
              Tem certeza que deseja excluir o aluno{' '}
              <span className="font-semibold">"{student.name}"</span>?
            </p>
            
            <div className="bg-muted/50 rounded-lg p-4 space-y-3">
              <p className="text-sm font-medium">Informações do aluno:</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground">
                <div>• Email: {student.email}</div>
                {student.phone && <div>• Telefone: {student.phone}</div>}
                <div className="flex items-center gap-1">
                  • Status: 
                  <span className={`ml-1 ${getStatusColor(student.status)}`}>
                    {getStatusLabel(student.status)}
                  </span>
                </div>
                <div>• Cadastrado em: {new Date(student.createdAt).toLocaleDateString('pt-PT')}</div>
                {student.lastAccess && (
                  <div>• Último acesso: {new Date(student.lastAccess).toLocaleDateString('pt-PT')}</div>
                )}
                <div>• Cursos matriculados: {enrolledCourses.length}</div>
              </div>
            </div>

            {enrolledCourses.length > 0 && (
              <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <BookOpen className="w-4 h-4 text-warning mt-0.5 flex-shrink-0" />
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-warning">
                      Cursos Matriculados ({enrolledCourses.length})
                    </p>
                    <div className="space-y-1">
                      {enrolledCourses.map((course) => (
                        <div key={course.id} className="text-sm text-warning/80">
                          • {course.title}
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-warning/70">
                      O aluno será automaticamente removido destes cursos.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-destructive">
                    Atenção: Esta ação não pode ser desfeita!
                  </p>
                  <p className="text-sm text-destructive/80">
                    Todos os dados do aluno, incluindo progresso nos cursos e histórico de atividades, serão perdidos permanentemente.
                  </p>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel className="focus-ring">
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-ring"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Excluir Aluno
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteStudentDialog;
